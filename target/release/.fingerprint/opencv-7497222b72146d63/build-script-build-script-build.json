{"rustc": 11410426090777951712, "features": "[\"alphamat\", \"aruco\", \"aruco_detector\", \"barcode\", \"bgsegm\", \"bioinspired\", \"calib3d\", \"ccalib\", \"cudaarithm\", \"cudabgsegm\", \"cudacodec\", \"cudafeatures2d\", \"cudafilters\", \"cudaimgproc\", \"cudaobjdetect\", \"cudaoptflow\", \"cudastereo\", \"cudawarping\", \"cvv\", \"default\", \"dnn\", \"dnn_superres\", \"dpm\", \"face\", \"features2d\", \"flann\", \"freetype\", \"fuzzy\", \"gapi\", \"hdf\", \"hfs\", \"highgui\", \"img_hash\", \"imgcodecs\", \"imgproc\", \"intensity_transform\", \"line_descriptor\", \"mcc\", \"ml\", \"objdetect\", \"optflow\", \"ovis\", \"phase_unwrapping\", \"photo\", \"plot\", \"quality\", \"rapid\", \"rgbd\", \"saliency\", \"sfm\", \"shape\", \"stereo\", \"stitching\", \"structured_light\", \"superres\", \"surface_matching\", \"text\", \"tracking\", \"video\", \"videoio\", \"videostab\", \"viz\", \"wechat_qrcode\", \"xfeatures2d\", \"ximgproc\", \"xobjdetect\", \"xphoto\"]", "declared_features": "[\"alphamat\", \"aruco\", \"aruco_detector\", \"barcode\", \"bgsegm\", \"bioinspired\", \"calib3d\", \"ccalib\", \"clang-runtime\", \"cudaarithm\", \"cudabgsegm\", \"cudacodec\", \"cudafeatures2d\", \"cudafilters\", \"cudaimgproc\", \"cudaobjdetect\", \"cudaoptflow\", \"cudastereo\", \"cudawarping\", \"cvv\", \"default\", \"dnn\", \"dnn_superres\", \"dpm\", \"face\", \"features2d\", \"flann\", \"freetype\", \"fuzzy\", \"gapi\", \"hdf\", \"hfs\", \"highgui\", \"img_hash\", \"imgcodecs\", \"imgproc\", \"intensity_transform\", \"line_descriptor\", \"mcc\", \"ml\", \"objdetect\", \"optflow\", \"ovis\", \"phase_unwrapping\", \"photo\", \"plot\", \"quality\", \"rapid\", \"rgb\", \"rgbd\", \"saliency\", \"sfm\", \"shape\", \"stereo\", \"stitching\", \"structured_light\", \"superres\", \"surface_matching\", \"text\", \"tracking\", \"video\", \"videoio\", \"videostab\", \"viz\", \"wechat_qrcode\", \"xfeatures2d\", \"ximgproc\", \"xobjdetect\", \"xphoto\"]", "target": 5408242616063297496, "profile": 17257705230225558938, "path": 5711659673913883100, "deps": [[3214373357989284387, "pkg_config", false, 18302300272212894571], [3722963349756955755, "once_cell", false, 1874758402727087889], [4899080583175475170, "semver", false, 14143702239116940552], [8410525223747752176, "shlex", false, 10635455268801515678], [10798972438384834726, "jobslot", false, 10918284355513840450], [11989259058781683633, "dunce", false, 15759786078819905084], [12410631062411814511, "cc", false, 17699466464649920944], [12933202132622624734, "vcpkg", false, 8558125060241842420], [15103513257332197228, "opencv_binding_generator", false, 7690057957319929458]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/opencv-7497222b72146d63/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
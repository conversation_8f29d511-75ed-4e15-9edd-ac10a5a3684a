{"rustc": 11410426090777951712, "features": "[\"alphamat\", \"aruco\", \"aruco_detector\", \"barcode\", \"bgsegm\", \"bioinspired\", \"calib3d\", \"ccalib\", \"cudaarithm\", \"cudabgsegm\", \"cudacodec\", \"cudafeatures2d\", \"cudafilters\", \"cudaimgproc\", \"cudaobjdetect\", \"cudaoptflow\", \"cudastereo\", \"cudawarping\", \"cvv\", \"default\", \"dnn\", \"dnn_superres\", \"dpm\", \"face\", \"features2d\", \"flann\", \"freetype\", \"fuzzy\", \"gapi\", \"hdf\", \"hfs\", \"highgui\", \"img_hash\", \"imgcodecs\", \"imgproc\", \"intensity_transform\", \"line_descriptor\", \"mcc\", \"ml\", \"objdetect\", \"optflow\", \"ovis\", \"phase_unwrapping\", \"photo\", \"plot\", \"quality\", \"rapid\", \"rgbd\", \"saliency\", \"sfm\", \"shape\", \"stereo\", \"stitching\", \"structured_light\", \"superres\", \"surface_matching\", \"text\", \"tracking\", \"video\", \"videoio\", \"videostab\", \"viz\", \"wechat_qrcode\", \"xfeatures2d\", \"ximgproc\", \"xobjdetect\", \"xphoto\"]", "declared_features": "[\"alphamat\", \"aruco\", \"aruco_detector\", \"barcode\", \"bgsegm\", \"bioinspired\", \"calib3d\", \"ccalib\", \"clang-runtime\", \"cudaarithm\", \"cudabgsegm\", \"cudacodec\", \"cudafeatures2d\", \"cudafilters\", \"cudaimgproc\", \"cudaobjdetect\", \"cudaoptflow\", \"cudastereo\", \"cudawarping\", \"cvv\", \"default\", \"dnn\", \"dnn_superres\", \"dpm\", \"face\", \"features2d\", \"flann\", \"freetype\", \"fuzzy\", \"gapi\", \"hdf\", \"hfs\", \"highgui\", \"img_hash\", \"imgcodecs\", \"imgproc\", \"intensity_transform\", \"line_descriptor\", \"mcc\", \"ml\", \"objdetect\", \"optflow\", \"ovis\", \"phase_unwrapping\", \"photo\", \"plot\", \"quality\", \"rapid\", \"rgb\", \"rgbd\", \"saliency\", \"sfm\", \"shape\", \"stereo\", \"stitching\", \"structured_light\", \"superres\", \"surface_matching\", \"text\", \"tracking\", \"video\", \"videoio\", \"videostab\", \"viz\", \"wechat_qrcode\", \"xfeatures2d\", \"ximgproc\", \"xobjdetect\", \"xphoto\"]", "target": 5205197980989264040, "profile": 16503403049695105087, "path": 2932059086439931089, "deps": [[2552259300034505578, "build_script_build", false, 14307577476574313282], [3722963349756955755, "once_cell", false, 17143724529708961827], [4684437522915235464, "libc", false, 5997092620628279967], [5157631553186200874, "num_traits", false, 17321525460663224106]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/opencv-c16932caba72c1e1/dep-lib-opencv", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
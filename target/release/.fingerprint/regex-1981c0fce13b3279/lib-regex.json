{"rustc": 11410426090777951712, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 17257705230225558938, "path": 12959529377469898766, "deps": [[555019317135488525, "regex_automata", false, 1806243361722662937], [2779309023524819297, "aho_corasick", false, 6894272882143276108], [9408802513701742484, "regex_syntax", false, 4020021097287481104], [15932120279885307830, "memchr", false, 2487316765071357320]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/regex-1981c0fce13b3279/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 11410426090777951712, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[2552259300034505578, "build_script_build", false, 8857772700048397148]], "local": [{"RerunIfChanged": {"output": "release/build/opencv-1ee1a75bddb6573b/output", "paths": ["src_cpp/dnn.hpp", "src_cpp/photo.hpp", "src_cpp/core.hpp", "src_cpp/alphamat.hpp", "src_cpp/videoio.hpp", "src_cpp/manual-core.cpp", "src_cpp/xfeatures2d.hpp", "src_cpp/aruco.hpp", "src_cpp/face.hpp", "src_cpp/sfm.hpp", "src_cpp/ocvrs_common.hpp", "src_cpp/bioinspired.hpp", "src_cpp/hdf.hpp", "src_cpp/gapi.hpp", "src_cpp/ccalib.hpp", "Cargo.toml"]}}, {"RerunIfEnvChanged": {"var": "OPENCV4_NO_PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENCV4_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENCV4_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_LIBDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "SYSROOT", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENCV4_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENCV4_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENCV4_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENCV4_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_LIBDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENCV_PACKAGE_NAME", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENCV_PKGCONFIG_NAME", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENCV_CMAKE_NAME", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENCV_CMAKE_BIN", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENCV_VCPKG_NAME", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENCV_LINK_LIBS", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENCV_LINK_PATHS", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENCV_INCLUDE_PATHS", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENCV_DISABLE_PROBES", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENCV_MSVC_CRT", "val": null}}, {"RerunIfEnvChanged": {"var": "CMAKE_PREFIX_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "OpenCV_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "VCPKG_ROOT", "val": null}}, {"RerunIfEnvChanged": {"var": "VCPKGRS_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "VCPKGRS_TRIPLET", "val": null}}, {"RerunIfEnvChanged": {"var": "OCVRS_DOCS_GENERATE_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "DOCS_RS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXX_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CXX_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CXX", "val": null}}, {"RerunIfEnvChanged": {"var": "CXX", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_AR", "val": null}}, {"RerunIfEnvChanged": {"var": "AR", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXSTDLIB_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXSTDLIB_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CXXSTDLIB", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXSTDLIB", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}
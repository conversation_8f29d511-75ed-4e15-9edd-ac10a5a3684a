{"$message_type":"diagnostic","message":"unused imports: `CV_8UC3` and `imgproc`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/frame_diff.rs","byte_start":98,"byte_end":105,"line_start":3,"line_end":3,"column_start":17,"column_end":24,"is_primary":true,"text":[{"text":"    core::{Mat, CV_8UC3},","highlight_start":17,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/frame_diff.rs","byte_start":112,"byte_end":119,"line_start":4,"line_end":4,"column_start":5,"column_end":12,"is_primary":true,"text":[{"text":"    imgproc,","highlight_start":5,"highlight_end":12}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/frame_diff.rs","byte_start":96,"byte_end":105,"line_start":3,"line_end":3,"column_start":15,"column_end":24,"is_primary":true,"text":[{"text":"    core::{Mat, CV_8UC3},","highlight_start":15,"highlight_end":24}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/frame_diff.rs","byte_start":92,"byte_end":93,"line_start":3,"line_end":3,"column_start":11,"column_end":12,"is_primary":true,"text":[{"text":"    core::{Mat, CV_8UC3},","highlight_start":11,"highlight_end":12}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/frame_diff.rs","byte_start":105,"byte_end":106,"line_start":3,"line_end":3,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"    core::{Mat, CV_8UC3},","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/frame_diff.rs","byte_start":106,"byte_end":119,"line_start":3,"line_end":4,"column_start":25,"column_end":12,"is_primary":true,"text":[{"text":"    core::{Mat, CV_8UC3},","highlight_start":25,"highlight_end":26},{"text":"    imgproc,","highlight_start":1,"highlight_end":12}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `CV_8UC3` and `imgproc`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/frame_diff.rs:3:17\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    core::{Mat, CV_8UC3},\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    imgproc,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `ImageBuffer` and `Rgb`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/frame_diff.rs","byte_start":152,"byte_end":163,"line_start":7,"line_end":7,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"use image::{ImageBuffer, Rgb, RgbImage};","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/frame_diff.rs","byte_start":165,"byte_end":168,"line_start":7,"line_end":7,"column_start":26,"column_end":29,"is_primary":true,"text":[{"text":"use image::{ImageBuffer, Rgb, RgbImage};","highlight_start":26,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/frame_diff.rs","byte_start":152,"byte_end":170,"line_start":7,"line_end":7,"column_start":13,"column_end":31,"is_primary":true,"text":[{"text":"use image::{ImageBuffer, Rgb, RgbImage};","highlight_start":13,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/frame_diff.rs","byte_start":151,"byte_end":152,"line_start":7,"line_end":7,"column_start":12,"column_end":13,"is_primary":true,"text":[{"text":"use image::{ImageBuffer, Rgb, RgbImage};","highlight_start":12,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/frame_diff.rs","byte_start":178,"byte_end":179,"line_start":7,"line_end":7,"column_start":39,"column_end":40,"is_primary":true,"text":[{"text":"use image::{ImageBuffer, Rgb, RgbImage};","highlight_start":39,"highlight_end":40}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `ImageBuffer` and `Rgb`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/frame_diff.rs:7:13\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse image::{ImageBuffer, Rgb, RgbImage};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `ImageFormat` and `RgbImage`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/detector.rs","byte_start":102,"byte_end":110,"line_start":4,"line_end":4,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"use image::{RgbImage, ImageFormat};","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/detector.rs","byte_start":112,"byte_end":123,"line_start":4,"line_end":4,"column_start":23,"column_end":34,"is_primary":true,"text":[{"text":"use image::{RgbImage, ImageFormat};","highlight_start":23,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/detector.rs","byte_start":90,"byte_end":126,"line_start":4,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use image::{RgbImage, ImageFormat};","highlight_start":1,"highlight_end":36},{"text":"use std::fs;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `ImageFormat` and `RgbImage`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/detector.rs:4:13\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse image::{RgbImage, ImageFormat};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `core::Mat` and `imgcodecs`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/extractor.rs","byte_start":2026,"byte_end":2035,"line_start":66,"line_end":66,"column_start":31,"column_end":40,"is_primary":true,"text":[{"text":"        use opencv::{videoio, core::Mat, imgcodecs, prelude::*};","highlight_start":31,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/extractor.rs","byte_start":2037,"byte_end":2046,"line_start":66,"line_end":66,"column_start":42,"column_end":51,"is_primary":true,"text":[{"text":"        use opencv::{videoio, core::Mat, imgcodecs, prelude::*};","highlight_start":42,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/extractor.rs","byte_start":2024,"byte_end":2046,"line_start":66,"line_end":66,"column_start":29,"column_end":51,"is_primary":true,"text":[{"text":"        use opencv::{videoio, core::Mat, imgcodecs, prelude::*};","highlight_start":29,"highlight_end":51}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `core::Mat` and `imgcodecs`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/extractor.rs:66:31\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m66\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        use opencv::{videoio, core::Mat, imgcodecs, prelude::*};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `data`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/frame_diff.rs","byte_start":11559,"byte_end":11563,"line_start":335,"line_end":335,"column_start":44,"column_end":48,"is_primary":true,"text":[{"text":"            Frame::Simple { width, height, data } => {","highlight_start":44,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/frame_diff.rs","byte_start":11559,"byte_end":11563,"line_start":335,"line_end":335,"column_start":44,"column_end":48,"is_primary":true,"text":[{"text":"            Frame::Simple { width, height, data } => {","highlight_start":44,"highlight_end":48}],"label":null,"suggested_replacement":"data: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `data`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/frame_diff.rs:335:44\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m335\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            Frame::Simple { width, height, data } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `data: _`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `input`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/main.rs","byte_start":3101,"byte_end":3106,"line_start":126,"line_end":126,"column_start":21,"column_end":26,"is_primary":true,"text":[{"text":"    if let Some(ref input) = cli.input {","highlight_start":21,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/main.rs","byte_start":3101,"byte_end":3106,"line_start":126,"line_end":126,"column_start":21,"column_end":26,"is_primary":true,"text":[{"text":"    if let Some(ref input) = cli.input {","highlight_start":21,"highlight_end":26}],"label":null,"suggested_replacement":"_input","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `input`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/main.rs:126:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m126\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    if let Some(ref input) = cli.input {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_input`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"associated function `from_str` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/types.rs","byte_start":2953,"byte_end":2976,"line_start":117,"line_end":117,"column_start":1,"column_end":24,"is_primary":false,"text":[{"text":"impl DetectionAlgorithm {","highlight_start":1,"highlight_end":24}],"label":"associated function in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/types.rs","byte_start":2990,"byte_end":2998,"line_start":118,"line_end":118,"column_start":12,"column_end":20,"is_primary":true,"text":[{"text":"    pub fn from_str(s: &str) -> Option<Self> {","highlight_start":12,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: associated function `from_str` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/types.rs:118:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m117\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl DetectionAlgorithm {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12massociated function in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m118\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn from_str(s: &str) -> Option<Self> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"methods `scene_count` and `average_scene_duration` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/types.rs","byte_start":4104,"byte_end":4124,"line_start":159,"line_end":159,"column_start":1,"column_end":21,"is_primary":false,"text":[{"text":"impl DetectionResult {","highlight_start":1,"highlight_end":21}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/types.rs","byte_start":4696,"byte_end":4707,"line_start":183,"line_end":183,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"    pub fn scene_count(&self) -> usize {","highlight_start":12,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/types.rs","byte_start":4812,"byte_end":4834,"line_start":188,"line_end":188,"column_start":12,"column_end":34,"is_primary":true,"text":[{"text":"    pub fn average_scene_duration(&self) -> f64 {","highlight_start":12,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: methods `scene_count` and `average_scene_duration` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/types.rs:183:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m159\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl DetectionResult {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m183\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn scene_count(&self) -> usize {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m188\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn average_scene_duration(&self) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"associated function `from_opencv_mat` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/frame_diff.rs","byte_start":387,"byte_end":397,"line_start":20,"line_end":20,"column_start":1,"column_end":11,"is_primary":false,"text":[{"text":"impl Frame {","highlight_start":1,"highlight_end":11}],"label":"associated function in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/frame_diff.rs","byte_start":733,"byte_end":748,"line_start":33,"line_end":33,"column_start":12,"column_end":27,"is_primary":true,"text":[{"text":"    pub fn from_opencv_mat(mat: Mat) -> Self {","highlight_start":12,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: associated function `from_opencv_mat` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/frame_diff.rs:33:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m20\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl Frame {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12massociated function in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn from_opencv_mat(mat: Mat) -> Self {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"methods `create_demo_result`, `config`, and `update_config` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/detector.rs","byte_start":542,"byte_end":560,"line_start":17,"line_end":17,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"impl SceneDetector {","highlight_start":1,"highlight_end":19}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/detector.rs","byte_start":13574,"byte_end":13592,"line_start":383,"line_end":383,"column_start":8,"column_end":26,"is_primary":true,"text":[{"text":"    fn create_demo_result<P: AsRef<Path>>(&self, video_path: P) -> Result<DetectionResult> {","highlight_start":8,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/detector.rs","byte_start":15800,"byte_end":15806,"line_start":446,"line_end":446,"column_start":12,"column_end":18,"is_primary":true,"text":[{"text":"    pub fn config(&self) -> &DetectorConfig {","highlight_start":12,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/detector.rs","byte_start":15895,"byte_end":15908,"line_start":451,"line_end":451,"column_start":12,"column_end":25,"is_primary":true,"text":[{"text":"    pub fn update_config(&mut self, config: DetectorConfig) {","highlight_start":12,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: methods `create_demo_result`, `config`, and `update_config` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/detector.rs:383:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m17\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl SceneDetector {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m383\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn create_demo_result<P: AsRef<Path>>(&self, video_path: P) -> Result<DetectionResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m446\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn config(&self) -> &DetectorConfig {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m451\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn update_config(&mut self, config: DetectorConfig) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `BatchProcessor` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/detector.rs","byte_start":17508,"byte_end":17522,"line_start":499,"line_end":499,"column_start":12,"column_end":26,"is_primary":true,"text":[{"text":"pub struct BatchProcessor {","highlight_start":12,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: struct `BatchProcessor` is never constructed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/detector.rs:499:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m499\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct BatchProcessor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"associated items `new` and `process_inputs` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/detector.rs","byte_start":17565,"byte_end":17584,"line_start":503,"line_end":503,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"impl BatchProcessor {","highlight_start":1,"highlight_end":20}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/detector.rs","byte_start":17598,"byte_end":17601,"line_start":504,"line_end":504,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(config: DetectorConfig) -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/detector.rs","byte_start":17756,"byte_end":17770,"line_start":511,"line_end":511,"column_start":12,"column_end":26,"is_primary":true,"text":[{"text":"    pub fn process_inputs<P: AsRef<Path>>(&self, input_paths: &[P]) -> Result<Vec<DetectionResult>> {","highlight_start":12,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: associated items `new` and `process_inputs` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/detector.rs:504:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m503\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl BatchProcessor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m504\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(config: DetectorConfig) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m511\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn process_inputs<P: AsRef<Path>>(&self, input_paths: &[P]) -> Result<Vec<DetectionResult>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"12 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 12 warnings emitted\u001b[0m\n\n"}

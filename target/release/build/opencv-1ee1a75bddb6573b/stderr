=== Crate version: Some("0.88.9")
=== Environment configuration:
===   OPENCV_PACKAGE_NAME = None
===   OPENCV_PKGCONFIG_NAME = None
===   OPENCV_CMAKE_NAME = None
===   OPENCV_CMAKE_BIN = None
===   OPENCV_VCPKG_NAME = None
===   OPENCV_LINK_LIBS = None
===   OPENCV_LINK_PATHS = None
===   OPENCV_INCLUDE_PATHS = None
===   OPENCV_DISABLE_PROBES = None
===   OPENCV_MSVC_CRT = None
===   CMAKE_PREFIX_PATH = None
===   OpenCV_DIR = None
===   PKG_CONFIG_PATH = None
===   VCPKG_ROOT = None
===   VCPKGRS_DYNAMIC = None
===   VCPKGRS_TRIPLET = None
===   OCVRS_DOCS_GENERATE_DIR = None
===   DOCS_RS = None
===   PATH = Some("/home/<USER>/.vscode-server/bin/488a1f239235055e34e673291fb8d8c810886f81/bin/remote-cli:/home/<USER>/.cargo/bin:/home/<USER>/.nvm/versions/node/v20.15.1/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/usr/lib/wsl/lib:/mnt/c/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin:/mnt/c/Program Files/Common Files/Oracle/Java/javapath:/mnt/c/Windows/system32:/mnt/c/Windows:/mnt/c/Windows/System32/Wbem:/mnt/c/Windows/System32/WindowsPowerShell/v1.0/:/mnt/c/Windows/System32/OpenSSH/:/mnt/d/Program Files/nodejs/:/mnt/c/Program Files/Git/cmd:/mnt/c/Users/<USER>/AppData/Local/Microsoft/WindowsApps:/mnt/c/Users/<USER>/AppData/Roaming/npm:/mnt/d/Espressif/tools/xtensa-esp-elf-gdb/14.2_20240403/xtensa-esp-elf-gdb/bin:/mnt/d/Espressif/tools/riscv32-esp-elf-gdb/14.2_20240403/riscv32-esp-elf-gdb/bin:/mnt/d/Espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin:/mnt/d/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin:/mnt/d/Espressif/tools/esp32ulp-elf/2.38_20240113/esp32ulp-elf/bin:/mnt/d/Espressif/tools/cmake/3.24.0/bin:/mnt/d/Espressif/tools/openocd-esp32/v0.12.0-esp32-20240318/openocd-esp32/bin:/mnt/d/Espressif/tools/ninja/1.11.1/:/mnt/d/Espressif/tools/idf-exe/1.0.3/:/mnt/d/Espressif/tools/ccache/4.8/ccache-4.8-windows-x86_64:/mnt/d/Espressif/tools/dfu-util/0.11/dfu-util-0.11-wi:/mnt/c/ProgramData/chocolatey/bin:/mnt/c/Program Files/dotnet/:/mnt/c/Program Files/Go/bin:/mnt/c/Program Files/Docker/Docker/resources/bin:/mnt/c/Users/<USER>/.cargo/bin:/mnt/c/Users/<USER>/AppData/Local/pnpm:/mnt/d/Espressif/tools/idf-python/3.11.2/:/mnt/d/Espressif/tools/idf-git/2.44.0/cmd/:/mnt/c/Program Files (x86)/Thunder Network/Thunder/Program/:/mnt/c/Program Files/Google/Chrome/Application:/mnt/c/Program Files/Common Files/Oracle/Java/javapath:/mnt/c/Windows/system32:/mnt/c/Windows:/mnt/c/Windows/System32/Wbem:/mnt/c/Windows/System32/WindowsPowerShell/v1.0/:/mnt/c/Windows/System32/OpenSSH/:/mnt/d/Program Files/nodejs/:/mnt/c/Program Files/Git/cmd:/mnt/c/Users/<USER>/AppData/Local/Microsoft/WindowsApps:/mnt/c/Users/<USER>/AppData/Roaming/npm:/mnt/d/Espressif/tools/xtensa-esp-elf-gdb/14.2_20240403/xtensa-esp-elf-gdb/bin:/mnt/d/Espressif/tools/riscv32-esp-elf-gdb/14.2_20240403/riscv32-esp-elf-gdb/bin:/mnt/d/Espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin:/mnt/d/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin:/mnt/d/Espressif/tools/esp32ulp-elf/2.38_20240113/esp32ulp-elf/bin:/mnt/d/Espressif/tools/cmake/3.24.0/bin:/mnt/d/Espressif/tools/openocd-esp32/v0.12.0-esp32-20240318/op:/mnt/d/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin:/mnt/c/Users/<USER>/.cargo/bin:/snap/bin:/usr/lib/jvm/java-8-openjdk-amd64//bin:/usr/lib/android-sdk/tools:/usr/lib/android-sdk/platform-tools:/usr/lib/android-sdk/build-tools/29.0.3:/mnt/d/workspace/libs/go/bin:/home/<USER>/go/bin:/usr/lib/jvm/java-8-openjdk-amd64//bin:/usr/lib/android-sdk/tools:/usr/lib/android-sdk/platform-tools:/usr/lib/android-sdk/build-tools/29.0.3:/mnt/d/workspace/libs/go/bin:/home/<USER>/go/bin")
=== Enabled features:
===   ALPHAMAT
===   ARUCO
===   ARUCO_DETECTOR
===   BARCODE
===   BGSEGM
===   BIOINSPIRED
===   CALIB3D
===   CCALIB
===   CUDAARITHM
===   CUDABGSEGM
===   CUDACODEC
===   CUDAFEATURES2D
===   CUDAFILTERS
===   CUDAIMGPROC
===   CUDAOBJDETECT
===   CUDAOPTFLOW
===   CUDASTEREO
===   CUDAWARPING
===   CVV
===   DEFAULT
===   DNN
===   DNN_SUPERRES
===   DPM
===   FACE
===   FEATURES2D
===   FLANN
===   FREETYPE
===   FUZZY
===   GAPI
===   HDF
===   HFS
===   HIGHGUI
===   IMGCODECS
===   IMGPROC
===   IMG_HASH
===   INTENSITY_TRANSFORM
===   LINE_DESCRIPTOR
===   MCC
===   ML
===   OBJDETECT
===   OPTFLOW
===   OVIS
===   PHASE_UNWRAPPING
===   PHOTO
===   PLOT
===   QUALITY
===   RAPID
===   RGBD
===   SALIENCY
===   SFM
===   SHAPE
===   STEREO
===   STITCHING
===   STRUCTURED_LIGHT
===   SUPERRES
===   SURFACE_MATCHING
===   TEXT
===   TRACKING
===   VIDEO
===   VIDEOIO
===   VIDEOSTAB
===   VIZ
===   WECHAT_QRCODE
===   XFEATURES2D
===   XIMGPROC
===   XOBJDETECT
===   XPHOTO
=== Detected probe priority based on environment vars: pkg_config: false, cmake: false, vcpkg: false
=== Probing the OpenCV library in the following order: environment, pkg_config, cmake, vcpkg_cmake, vcpkg
=== Can't probe using: environment, continuing with other methods because: Some environment variables are missing
=== Probing OpenCV library using pkg_config
=== Successfully probed using: pkg_config
=== OpenCV library configuration: Library {
    include_paths: [
        "/usr/include/opencv4",
    ],
    version: Version {
        major: 4,
        minor: 5,
        patch: 4,
    },
    cargo_metadata: [
        "cargo:rustc-link-search=/usr/lib/x86_64-linux-gnu",
        "cargo:rustc-link-lib=opencv_stitching",
        "cargo:rustc-link-lib=opencv_alphamat",
        "cargo:rustc-link-lib=opencv_aruco",
        "cargo:rustc-link-lib=opencv_barcode",
        "cargo:rustc-link-lib=opencv_bgsegm",
        "cargo:rustc-link-lib=opencv_bioinspired",
        "cargo:rustc-link-lib=opencv_ccalib",
        "cargo:rustc-link-lib=opencv_dnn_objdetect",
        "cargo:rustc-link-lib=opencv_dnn_superres",
        "cargo:rustc-link-lib=opencv_dpm",
        "cargo:rustc-link-lib=opencv_face",
        "cargo:rustc-link-lib=opencv_freetype",
        "cargo:rustc-link-lib=opencv_fuzzy",
        "cargo:rustc-link-lib=opencv_hdf",
        "cargo:rustc-link-lib=opencv_hfs",
        "cargo:rustc-link-lib=opencv_img_hash",
        "cargo:rustc-link-lib=opencv_intensity_transform",
        "cargo:rustc-link-lib=opencv_line_descriptor",
        "cargo:rustc-link-lib=opencv_mcc",
        "cargo:rustc-link-lib=opencv_quality",
        "cargo:rustc-link-lib=opencv_rapid",
        "cargo:rustc-link-lib=opencv_reg",
        "cargo:rustc-link-lib=opencv_rgbd",
        "cargo:rustc-link-lib=opencv_saliency",
        "cargo:rustc-link-lib=opencv_shape",
        "cargo:rustc-link-lib=opencv_stereo",
        "cargo:rustc-link-lib=opencv_structured_light",
        "cargo:rustc-link-lib=opencv_phase_unwrapping",
        "cargo:rustc-link-lib=opencv_superres",
        "cargo:rustc-link-lib=opencv_optflow",
        "cargo:rustc-link-lib=opencv_surface_matching",
        "cargo:rustc-link-lib=opencv_tracking",
        "cargo:rustc-link-lib=opencv_highgui",
        "cargo:rustc-link-lib=opencv_datasets",
        "cargo:rustc-link-lib=opencv_text",
        "cargo:rustc-link-lib=opencv_plot",
        "cargo:rustc-link-lib=opencv_ml",
        "cargo:rustc-link-lib=opencv_videostab",
        "cargo:rustc-link-lib=opencv_videoio",
        "cargo:rustc-link-lib=opencv_viz",
        "cargo:rustc-link-lib=opencv_wechat_qrcode",
        "cargo:rustc-link-lib=opencv_ximgproc",
        "cargo:rustc-link-lib=opencv_video",
        "cargo:rustc-link-lib=opencv_xobjdetect",
        "cargo:rustc-link-lib=opencv_objdetect",
        "cargo:rustc-link-lib=opencv_calib3d",
        "cargo:rustc-link-lib=opencv_imgcodecs",
        "cargo:rustc-link-lib=opencv_features2d",
        "cargo:rustc-link-lib=opencv_dnn",
        "cargo:rustc-link-lib=opencv_flann",
        "cargo:rustc-link-lib=opencv_xphoto",
        "cargo:rustc-link-lib=opencv_photo",
        "cargo:rustc-link-lib=opencv_imgproc",
        "cargo:rustc-link-lib=opencv_core",
    ],
}
=== Detected OpenCV module header dir at: /usr/include/opencv4/opencv2
=== Found OpenCV version: 4.5.4 in headers located at: /usr/include/opencv4
=== Generating code in: /home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out
=== Placing generated bindings into: /home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out/opencv
=== Using OpenCV headers from: /usr/include/opencv4
=== Clang: Ubuntu clang version 14.0.0-1ubuntu1.1
=== Clang command line args: [
    "-isystem/usr/bin/../lib/gcc/x86_64-linux-gnu/11/../../../../include/c++/11",
    "-isystem/usr/bin/../lib/gcc/x86_64-linux-gnu/11/../../../../include/x86_64-linux-gnu/c++/11",
    "-isystem/usr/bin/../lib/gcc/x86_64-linux-gnu/11/../../../../include/c++/11/backward",
    "-isystem/usr/lib/llvm-14/lib/clang/14.0.0/include",
    "-isystem/usr/local/include",
    "-isystem/usr/include/x86_64-linux-gnu",
    "-isystem/usr/include",
    "-I/usr/include/opencv4",
    "-F/usr/include/opencv4",
    "-I/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp",
    "-F/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp",
    "-DOCVRS_PARSING_HEADERS",
    "-includeocvrs_common.hpp",
    "-std=c++14",
]
=== Using environment job server with the the amount of available jobs: 16
=== Generating 51 modules
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "alphamat" ""
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "barcode" ""
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "bgsegm" ""
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "bioinspired" ""
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "aruco" ""
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "calib3d" ""
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "ccalib" ""
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "core" ""
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "dnn" ""
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "dnn_superres" ""
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "dpm" ""
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "features2d" ""
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "face" ""
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "freetype" ""
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "fuzzy" ""
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "flann" ""
=== Generated: barcode in 4.409289604s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "hdf" ""
=== Generated: freetype in 4.450338945s
=== Generated: alphamat in 4.454928753s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "hfs" ""
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "highgui" ""
=== Generated: dpm in 4.524632956s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "img_hash" ""
=== Generated: fuzzy in 4.594954758s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "imgcodecs" ""
=== Generated: dnn_superres in 4.654359096s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "imgproc" ""
=== Generated: bioinspired in 4.824312539s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "intensity_transform" ""
=== Generated: bgsegm in 5.071068822s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "line_descriptor" ""
=== Generated: flann in 5.349815269s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "mcc" ""
=== Generated: aruco in 5.818472432s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "ml" ""
=== Generated: ccalib in 6.086968218s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "objdetect" ""
=== Generated: face in 6.086401276s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "optflow" ""
=== Generated: features2d in 6.574592274s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "phase_unwrapping" ""
=== Generated: hfs in 4.095619292s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "photo" ""
=== Generated: calib3d in 8.66439815s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "plot" ""
=== Generated: hdf in 4.290423134s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "quality" ""
=== Generated: intensity_transform in 3.881743914s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "rapid" ""
=== Generated: highgui in 4.335511565s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "rgbd" ""
=== Generated: img_hash in 4.335213456s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "saliency" ""
=== Generated: imgcodecs in 4.333137284s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "shape" ""
=== Generated: line_descriptor in 5.410538537s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "stereo" ""
=== Generated: mcc in 5.30310139s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "stitching" ""
=== Generated: dnn in 10.975375699s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "structured_light" ""
=== Generated: phase_unwrapping in 5.032784727s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "superres" ""
=== Generated: optflow in 5.985614729s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "surface_matching" ""
=== Generated: plot in 3.982602787s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "text" ""
=== Generated: ml in 6.900365689s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "tracking" ""
=== Generated: objdetect in 6.755557039s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "video" ""
=== Generated: quality in 4.351818832s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "videoio" ""
=== Generated: rapid in 4.409226371s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "videostab" ""
=== Generated: saliency in 4.257755858s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "viz" ""
=== Generated: imgproc in 8.591051514s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "wechat_qrcode" ""
=== Generated: shape in 4.864610655s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "ximgproc" ""
=== Generated: photo in 5.331160455s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "xobjdetect" ""
=== Generated: stereo in 4.943841937s
=== Running: "/home/<USER>/chart/target/release/build/opencv-7497222b72146d63/build-script-build" "/usr/include/opencv4" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp" "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out" "xphoto" ""
=== Generated: structured_light in 5.444032137s
=== Generated: superres in 5.039219356s
=== Generated: surface_matching in 4.896297763s
=== Generated: rgbd in 8.211128477s
=== Generated: wechat_qrcode in 4.039363489s
=== Generated: tracking in 4.616610533s
=== Generated: core in 17.884671814s
=== Generated: videoio in 4.934870723s
=== Generated: text in 5.716762191s
=== Generated: video in 5.64999828s
=== Generated: xobjdetect in 4.892731549s
=== Generated: videostab in 6.30616172s
=== Generated: viz in 6.312921418s
=== Generated: stitching in 9.303941566s
=== Generated: xphoto in 4.636058515s
=== Generated: ximgproc in 6.62598874s
=== Total binding generation time: 20.42713485s
=== Total binding collection time: 468.657217ms
=== Compiler information: Tool {
    path: "c++",
    cc_wrapper_path: None,
    cc_wrapper_args: [],
    args: [
        "-O3",
        "-ffunction-sections",
        "-fdata-sections",
        "-fPIC",
        "-m64",
        "-std=c++14",
        "-I",
        "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/opencv-0.88.9/src_cpp",
        "-I",
        "/home/<USER>/chart/target/release/build/opencv-1ee1a75bddb6573b/out",
        "-I",
        ".",
        "-I",
        "/usr/include/opencv4",
        "-Wall",
        "-Wextra",
        "-Wno-deprecated-declarations",
        "-Wno-deprecated-copy",
        "-Wno-unused-parameter",
        "-Wno-sign-compare",
        "-Wno-comment",
        "-Wunused-but-set-variable",
        "-Wno-unused-variable",
        "-Wno-ignored-qualifiers",
        "-Wno-return-type-c-linkage",
        "-Wno-overloaded-virtual",
    ],
    env: [
        (
            "LC_ALL",
            "C",
        ),
    ],
    family: Gnu,
    cuda: false,
    removed_args: [],
    has_internal_target_arg: false,
}
=== Total cpp build time: 64.872234315s

extern "C" {
	const cv::BaseCascadeClassifier* cv_PtrLcv_BaseCascadeClassifierG_getInnerPtr_const(const cv::Ptr<cv::BaseCascadeClassifier>* instance) {
			return instance->get();
	}
	
	cv::BaseCascadeClassifier* cv_PtrLcv_BaseCascadeClassifierG_getInnerPtrMut(cv::Ptr<cv::BaseCascadeClassifier>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_BaseCascadeClassifierG_delete(cv::Ptr<cv::BaseCascadeClassifier>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_BaseCascadeClassifierG_to_PtrOfAlgorithm(cv::Ptr<cv::BaseCascadeClassifier>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::BaseCascadeClassifier::MaskGenerator* cv_PtrLcv_BaseCascadeClassifier_MaskGeneratorG_getInnerPtr_const(const cv::Ptr<cv::BaseCascadeClassifier::MaskGenerator>* instance) {
			return instance->get();
	}
	
	cv::BaseCascadeClassifier::MaskGenerator* cv_PtrLcv_BaseCascadeClassifier_MaskGeneratorG_getInnerPtrMut(cv::Ptr<cv::BaseCascadeClassifier::MaskGenerator>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_BaseCascadeClassifier_MaskGeneratorG_delete(cv::Ptr<cv::BaseCascadeClassifier::MaskGenerator>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::DetectionBasedTracker::IDetector* cv_PtrLcv_DetectionBasedTracker_IDetectorG_getInnerPtr_const(const cv::Ptr<cv::DetectionBasedTracker::IDetector>* instance) {
			return instance->get();
	}
	
	cv::DetectionBasedTracker::IDetector* cv_PtrLcv_DetectionBasedTracker_IDetectorG_getInnerPtrMut(cv::Ptr<cv::DetectionBasedTracker::IDetector>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_DetectionBasedTracker_IDetectorG_delete(cv::Ptr<cv::DetectionBasedTracker::IDetector>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::FaceDetectorYN* cv_PtrLcv_FaceDetectorYNG_getInnerPtr_const(const cv::Ptr<cv::FaceDetectorYN>* instance) {
			return instance->get();
	}
	
	cv::FaceDetectorYN* cv_PtrLcv_FaceDetectorYNG_getInnerPtrMut(cv::Ptr<cv::FaceDetectorYN>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_FaceDetectorYNG_delete(cv::Ptr<cv::FaceDetectorYN>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::FaceRecognizerSF* cv_PtrLcv_FaceRecognizerSFG_getInnerPtr_const(const cv::Ptr<cv::FaceRecognizerSF>* instance) {
			return instance->get();
	}
	
	cv::FaceRecognizerSF* cv_PtrLcv_FaceRecognizerSFG_getInnerPtrMut(cv::Ptr<cv::FaceRecognizerSF>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_FaceRecognizerSFG_delete(cv::Ptr<cv::FaceRecognizerSF>* instance) {
			delete instance;
	}
	
}

extern "C" {
	std::vector<cv::DetectionBasedTracker::ExtObject>* std_vectorLcv_DetectionBasedTracker_ExtObjectG_new_const() {
			std::vector<cv::DetectionBasedTracker::ExtObject>* ret = new std::vector<cv::DetectionBasedTracker::ExtObject>();
			return ret;
	}
	
	void std_vectorLcv_DetectionBasedTracker_ExtObjectG_delete(std::vector<cv::DetectionBasedTracker::ExtObject>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_DetectionBasedTracker_ExtObjectG_len_const(const std::vector<cv::DetectionBasedTracker::ExtObject>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_DetectionBasedTracker_ExtObjectG_isEmpty_const(const std::vector<cv::DetectionBasedTracker::ExtObject>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_DetectionBasedTracker_ExtObjectG_capacity_const(const std::vector<cv::DetectionBasedTracker::ExtObject>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_DetectionBasedTracker_ExtObjectG_shrinkToFit(std::vector<cv::DetectionBasedTracker::ExtObject>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_DetectionBasedTracker_ExtObjectG_reserve_size_t(std::vector<cv::DetectionBasedTracker::ExtObject>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_DetectionBasedTracker_ExtObjectG_remove_size_t(std::vector<cv::DetectionBasedTracker::ExtObject>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_DetectionBasedTracker_ExtObjectG_swap_size_t_size_t(std::vector<cv::DetectionBasedTracker::ExtObject>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_DetectionBasedTracker_ExtObjectG_clear(std::vector<cv::DetectionBasedTracker::ExtObject>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_DetectionBasedTracker_ExtObjectG_push_const_ExtObject(std::vector<cv::DetectionBasedTracker::ExtObject>* instance, const cv::DetectionBasedTracker::ExtObject* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_DetectionBasedTracker_ExtObjectG_insert_size_t_const_ExtObject(std::vector<cv::DetectionBasedTracker::ExtObject>* instance, size_t index, const cv::DetectionBasedTracker::ExtObject* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_DetectionBasedTracker_ExtObjectG_get_const_size_t(const std::vector<cv::DetectionBasedTracker::ExtObject>* instance, size_t index, cv::DetectionBasedTracker::ExtObject** ocvrs_return) {
			cv::DetectionBasedTracker::ExtObject ret = (*instance)[index];
			*ocvrs_return = new cv::DetectionBasedTracker::ExtObject(ret);
	}
	
	void std_vectorLcv_DetectionBasedTracker_ExtObjectG_set_size_t_const_ExtObject(std::vector<cv::DetectionBasedTracker::ExtObject>* instance, size_t index, const cv::DetectionBasedTracker::ExtObject* val) {
			(*instance)[index] = *val;
	}
	
}


extern "C" {
	std::vector<cv::DetectionBasedTracker::Object>* std_vectorLcv_DetectionBasedTracker_ObjectG_new_const() {
			std::vector<cv::DetectionBasedTracker::Object>* ret = new std::vector<cv::DetectionBasedTracker::Object>();
			return ret;
	}
	
	void std_vectorLcv_DetectionBasedTracker_ObjectG_delete(std::vector<cv::DetectionBasedTracker::Object>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_DetectionBasedTracker_ObjectG_len_const(const std::vector<cv::DetectionBasedTracker::Object>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_DetectionBasedTracker_ObjectG_isEmpty_const(const std::vector<cv::DetectionBasedTracker::Object>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_DetectionBasedTracker_ObjectG_capacity_const(const std::vector<cv::DetectionBasedTracker::Object>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_DetectionBasedTracker_ObjectG_shrinkToFit(std::vector<cv::DetectionBasedTracker::Object>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_DetectionBasedTracker_ObjectG_reserve_size_t(std::vector<cv::DetectionBasedTracker::Object>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_DetectionBasedTracker_ObjectG_remove_size_t(std::vector<cv::DetectionBasedTracker::Object>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_DetectionBasedTracker_ObjectG_swap_size_t_size_t(std::vector<cv::DetectionBasedTracker::Object>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_DetectionBasedTracker_ObjectG_clear(std::vector<cv::DetectionBasedTracker::Object>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_DetectionBasedTracker_ObjectG_push_const_Object(std::vector<cv::DetectionBasedTracker::Object>* instance, const cv::DetectionBasedTracker::Object* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_DetectionBasedTracker_ObjectG_insert_size_t_const_Object(std::vector<cv::DetectionBasedTracker::Object>* instance, size_t index, const cv::DetectionBasedTracker::Object* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_DetectionBasedTracker_ObjectG_get_const_size_t(const std::vector<cv::DetectionBasedTracker::Object>* instance, size_t index, cv::DetectionBasedTracker::Object** ocvrs_return) {
			cv::DetectionBasedTracker::Object ret = (*instance)[index];
			*ocvrs_return = new cv::DetectionBasedTracker::Object(ret);
	}
	
	void std_vectorLcv_DetectionBasedTracker_ObjectG_set_size_t_const_Object(std::vector<cv::DetectionBasedTracker::Object>* instance, size_t index, const cv::DetectionBasedTracker::Object* val) {
			(*instance)[index] = *val;
	}
	
}


extern "C" {
	std::vector<cv::DetectionROI>* std_vectorLcv_DetectionROIG_new_const() {
			std::vector<cv::DetectionROI>* ret = new std::vector<cv::DetectionROI>();
			return ret;
	}
	
	void std_vectorLcv_DetectionROIG_delete(std::vector<cv::DetectionROI>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_DetectionROIG_len_const(const std::vector<cv::DetectionROI>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_DetectionROIG_isEmpty_const(const std::vector<cv::DetectionROI>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_DetectionROIG_capacity_const(const std::vector<cv::DetectionROI>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_DetectionROIG_shrinkToFit(std::vector<cv::DetectionROI>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_DetectionROIG_reserve_size_t(std::vector<cv::DetectionROI>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_DetectionROIG_remove_size_t(std::vector<cv::DetectionROI>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_DetectionROIG_swap_size_t_size_t(std::vector<cv::DetectionROI>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_DetectionROIG_clear(std::vector<cv::DetectionROI>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_DetectionROIG_push_const_DetectionROI(std::vector<cv::DetectionROI>* instance, const cv::DetectionROI* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_DetectionROIG_insert_size_t_const_DetectionROI(std::vector<cv::DetectionROI>* instance, size_t index, const cv::DetectionROI* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_DetectionROIG_get_const_size_t(const std::vector<cv::DetectionROI>* instance, size_t index, cv::DetectionROI** ocvrs_return) {
			cv::DetectionROI ret = (*instance)[index];
			*ocvrs_return = new cv::DetectionROI(ret);
	}
	
	void std_vectorLcv_DetectionROIG_set_size_t_const_DetectionROI(std::vector<cv::DetectionROI>* instance, size_t index, const cv::DetectionROI* val) {
			(*instance)[index] = *val;
	}
	
}



extern "C" {
	const cv::AKAZE* cv_PtrLcv_AKAZEG_getInnerPtr_const(const cv::Ptr<cv::AKAZE>* instance) {
			return instance->get();
	}
	
	cv::AKAZE* cv_PtrLcv_AKAZEG_getInnerPtrMut(cv::Ptr<cv::AKAZE>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_AKAZEG_delete(cv::Ptr<cv::AKAZE>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_AKAZEG_to_PtrOfAlgorithm(cv::Ptr<cv::AKAZE>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::Feature2D>* cv_PtrLcv_AKAZEG_to_PtrOfFeature2D(cv::Ptr<cv::AKAZE>* instance) {
			return new cv::Ptr<cv::Feature2D>(instance->dynamicCast<cv::Feature2D>());
	}
	
}

extern "C" {
	const cv::AffineFeature* cv_PtrLcv_AffineFeatureG_getInnerPtr_const(const cv::Ptr<cv::AffineFeature>* instance) {
			return instance->get();
	}
	
	cv::AffineFeature* cv_PtrLcv_AffineFeatureG_getInnerPtrMut(cv::Ptr<cv::AffineFeature>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_AffineFeatureG_delete(cv::Ptr<cv::AffineFeature>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_AffineFeatureG_to_PtrOfAlgorithm(cv::Ptr<cv::AffineFeature>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::Feature2D>* cv_PtrLcv_AffineFeatureG_to_PtrOfFeature2D(cv::Ptr<cv::AffineFeature>* instance) {
			return new cv::Ptr<cv::Feature2D>(instance->dynamicCast<cv::Feature2D>());
	}
	
}

extern "C" {
	const cv::AgastFeatureDetector* cv_PtrLcv_AgastFeatureDetectorG_getInnerPtr_const(const cv::Ptr<cv::AgastFeatureDetector>* instance) {
			return instance->get();
	}
	
	cv::AgastFeatureDetector* cv_PtrLcv_AgastFeatureDetectorG_getInnerPtrMut(cv::Ptr<cv::AgastFeatureDetector>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_AgastFeatureDetectorG_delete(cv::Ptr<cv::AgastFeatureDetector>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_AgastFeatureDetectorG_to_PtrOfAlgorithm(cv::Ptr<cv::AgastFeatureDetector>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::Feature2D>* cv_PtrLcv_AgastFeatureDetectorG_to_PtrOfFeature2D(cv::Ptr<cv::AgastFeatureDetector>* instance) {
			return new cv::Ptr<cv::Feature2D>(instance->dynamicCast<cv::Feature2D>());
	}
	
}

extern "C" {
	const cv::BFMatcher* cv_PtrLcv_BFMatcherG_getInnerPtr_const(const cv::Ptr<cv::BFMatcher>* instance) {
			return instance->get();
	}
	
	cv::BFMatcher* cv_PtrLcv_BFMatcherG_getInnerPtrMut(cv::Ptr<cv::BFMatcher>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_BFMatcherG_delete(cv::Ptr<cv::BFMatcher>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_BFMatcherG_to_PtrOfAlgorithm(cv::Ptr<cv::BFMatcher>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::DescriptorMatcher>* cv_PtrLcv_BFMatcherG_to_PtrOfDescriptorMatcher(cv::Ptr<cv::BFMatcher>* instance) {
			return new cv::Ptr<cv::DescriptorMatcher>(instance->dynamicCast<cv::DescriptorMatcher>());
	}
	
	cv::Ptr<cv::BFMatcher>* cv_PtrLcv_BFMatcherG_new_const_BFMatcher(cv::BFMatcher* val) {
			return new cv::Ptr<cv::BFMatcher>(val);
	}
	
}

extern "C" {
	const cv::BRISK* cv_PtrLcv_BRISKG_getInnerPtr_const(const cv::Ptr<cv::BRISK>* instance) {
			return instance->get();
	}
	
	cv::BRISK* cv_PtrLcv_BRISKG_getInnerPtrMut(cv::Ptr<cv::BRISK>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_BRISKG_delete(cv::Ptr<cv::BRISK>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_BRISKG_to_PtrOfAlgorithm(cv::Ptr<cv::BRISK>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::Feature2D>* cv_PtrLcv_BRISKG_to_PtrOfFeature2D(cv::Ptr<cv::BRISK>* instance) {
			return new cv::Ptr<cv::Feature2D>(instance->dynamicCast<cv::Feature2D>());
	}
	
	cv::Ptr<cv::BRISK>* cv_PtrLcv_BRISKG_new_const_BRISK(cv::BRISK* val) {
			return new cv::Ptr<cv::BRISK>(val);
	}
	
}

extern "C" {
	const cv::DescriptorMatcher* cv_PtrLcv_DescriptorMatcherG_getInnerPtr_const(const cv::Ptr<cv::DescriptorMatcher>* instance) {
			return instance->get();
	}
	
	cv::DescriptorMatcher* cv_PtrLcv_DescriptorMatcherG_getInnerPtrMut(cv::Ptr<cv::DescriptorMatcher>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_DescriptorMatcherG_delete(cv::Ptr<cv::DescriptorMatcher>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_DescriptorMatcherG_to_PtrOfAlgorithm(cv::Ptr<cv::DescriptorMatcher>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::FastFeatureDetector* cv_PtrLcv_FastFeatureDetectorG_getInnerPtr_const(const cv::Ptr<cv::FastFeatureDetector>* instance) {
			return instance->get();
	}
	
	cv::FastFeatureDetector* cv_PtrLcv_FastFeatureDetectorG_getInnerPtrMut(cv::Ptr<cv::FastFeatureDetector>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_FastFeatureDetectorG_delete(cv::Ptr<cv::FastFeatureDetector>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_FastFeatureDetectorG_to_PtrOfAlgorithm(cv::Ptr<cv::FastFeatureDetector>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::Feature2D>* cv_PtrLcv_FastFeatureDetectorG_to_PtrOfFeature2D(cv::Ptr<cv::FastFeatureDetector>* instance) {
			return new cv::Ptr<cv::Feature2D>(instance->dynamicCast<cv::Feature2D>());
	}
	
}

extern "C" {
	const cv::Feature2D* cv_PtrLcv_Feature2DG_getInnerPtr_const(const cv::Ptr<cv::Feature2D>* instance) {
			return instance->get();
	}
	
	cv::Feature2D* cv_PtrLcv_Feature2DG_getInnerPtrMut(cv::Ptr<cv::Feature2D>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_Feature2DG_delete(cv::Ptr<cv::Feature2D>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_Feature2DG_to_PtrOfAlgorithm(cv::Ptr<cv::Feature2D>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::Feature2D>* cv_PtrLcv_Feature2DG_new_const_Feature2D(cv::Feature2D* val) {
			return new cv::Ptr<cv::Feature2D>(val);
	}
	
}

extern "C" {
	const cv::FlannBasedMatcher* cv_PtrLcv_FlannBasedMatcherG_getInnerPtr_const(const cv::Ptr<cv::FlannBasedMatcher>* instance) {
			return instance->get();
	}
	
	cv::FlannBasedMatcher* cv_PtrLcv_FlannBasedMatcherG_getInnerPtrMut(cv::Ptr<cv::FlannBasedMatcher>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_FlannBasedMatcherG_delete(cv::Ptr<cv::FlannBasedMatcher>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_FlannBasedMatcherG_to_PtrOfAlgorithm(cv::Ptr<cv::FlannBasedMatcher>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::DescriptorMatcher>* cv_PtrLcv_FlannBasedMatcherG_to_PtrOfDescriptorMatcher(cv::Ptr<cv::FlannBasedMatcher>* instance) {
			return new cv::Ptr<cv::DescriptorMatcher>(instance->dynamicCast<cv::DescriptorMatcher>());
	}
	
	cv::Ptr<cv::FlannBasedMatcher>* cv_PtrLcv_FlannBasedMatcherG_new_const_FlannBasedMatcher(cv::FlannBasedMatcher* val) {
			return new cv::Ptr<cv::FlannBasedMatcher>(val);
	}
	
}

extern "C" {
	const cv::GFTTDetector* cv_PtrLcv_GFTTDetectorG_getInnerPtr_const(const cv::Ptr<cv::GFTTDetector>* instance) {
			return instance->get();
	}
	
	cv::GFTTDetector* cv_PtrLcv_GFTTDetectorG_getInnerPtrMut(cv::Ptr<cv::GFTTDetector>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_GFTTDetectorG_delete(cv::Ptr<cv::GFTTDetector>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_GFTTDetectorG_to_PtrOfAlgorithm(cv::Ptr<cv::GFTTDetector>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::Feature2D>* cv_PtrLcv_GFTTDetectorG_to_PtrOfFeature2D(cv::Ptr<cv::GFTTDetector>* instance) {
			return new cv::Ptr<cv::Feature2D>(instance->dynamicCast<cv::Feature2D>());
	}
	
}

extern "C" {
	const cv::KAZE* cv_PtrLcv_KAZEG_getInnerPtr_const(const cv::Ptr<cv::KAZE>* instance) {
			return instance->get();
	}
	
	cv::KAZE* cv_PtrLcv_KAZEG_getInnerPtrMut(cv::Ptr<cv::KAZE>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_KAZEG_delete(cv::Ptr<cv::KAZE>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_KAZEG_to_PtrOfAlgorithm(cv::Ptr<cv::KAZE>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::Feature2D>* cv_PtrLcv_KAZEG_to_PtrOfFeature2D(cv::Ptr<cv::KAZE>* instance) {
			return new cv::Ptr<cv::Feature2D>(instance->dynamicCast<cv::Feature2D>());
	}
	
}

extern "C" {
	const cv::MSER* cv_PtrLcv_MSERG_getInnerPtr_const(const cv::Ptr<cv::MSER>* instance) {
			return instance->get();
	}
	
	cv::MSER* cv_PtrLcv_MSERG_getInnerPtrMut(cv::Ptr<cv::MSER>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_MSERG_delete(cv::Ptr<cv::MSER>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_MSERG_to_PtrOfAlgorithm(cv::Ptr<cv::MSER>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::Feature2D>* cv_PtrLcv_MSERG_to_PtrOfFeature2D(cv::Ptr<cv::MSER>* instance) {
			return new cv::Ptr<cv::Feature2D>(instance->dynamicCast<cv::Feature2D>());
	}
	
}

extern "C" {
	const cv::ORB* cv_PtrLcv_ORBG_getInnerPtr_const(const cv::Ptr<cv::ORB>* instance) {
			return instance->get();
	}
	
	cv::ORB* cv_PtrLcv_ORBG_getInnerPtrMut(cv::Ptr<cv::ORB>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ORBG_delete(cv::Ptr<cv::ORB>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ORBG_to_PtrOfAlgorithm(cv::Ptr<cv::ORB>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::Feature2D>* cv_PtrLcv_ORBG_to_PtrOfFeature2D(cv::Ptr<cv::ORB>* instance) {
			return new cv::Ptr<cv::Feature2D>(instance->dynamicCast<cv::Feature2D>());
	}
	
}

extern "C" {
	const cv::SIFT* cv_PtrLcv_SIFTG_getInnerPtr_const(const cv::Ptr<cv::SIFT>* instance) {
			return instance->get();
	}
	
	cv::SIFT* cv_PtrLcv_SIFTG_getInnerPtrMut(cv::Ptr<cv::SIFT>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_SIFTG_delete(cv::Ptr<cv::SIFT>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_SIFTG_to_PtrOfAlgorithm(cv::Ptr<cv::SIFT>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::Feature2D>* cv_PtrLcv_SIFTG_to_PtrOfFeature2D(cv::Ptr<cv::SIFT>* instance) {
			return new cv::Ptr<cv::Feature2D>(instance->dynamicCast<cv::Feature2D>());
	}
	
	cv::Ptr<cv::SIFT>* cv_PtrLcv_SIFTG_new_const_SIFT(cv::SIFT* val) {
			return new cv::Ptr<cv::SIFT>(val);
	}
	
}

extern "C" {
	const cv::SimpleBlobDetector* cv_PtrLcv_SimpleBlobDetectorG_getInnerPtr_const(const cv::Ptr<cv::SimpleBlobDetector>* instance) {
			return instance->get();
	}
	
	cv::SimpleBlobDetector* cv_PtrLcv_SimpleBlobDetectorG_getInnerPtrMut(cv::Ptr<cv::SimpleBlobDetector>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_SimpleBlobDetectorG_delete(cv::Ptr<cv::SimpleBlobDetector>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_SimpleBlobDetectorG_to_PtrOfAlgorithm(cv::Ptr<cv::SimpleBlobDetector>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::Feature2D>* cv_PtrLcv_SimpleBlobDetectorG_to_PtrOfFeature2D(cv::Ptr<cv::SimpleBlobDetector>* instance) {
			return new cv::Ptr<cv::Feature2D>(instance->dynamicCast<cv::Feature2D>());
	}
	
	cv::Ptr<cv::SimpleBlobDetector>* cv_PtrLcv_SimpleBlobDetectorG_new_const_SimpleBlobDetector(cv::SimpleBlobDetector* val) {
			return new cv::Ptr<cv::SimpleBlobDetector>(val);
	}
	
}


extern "C" {
	const cv::ml::ANN_MLP* cv_PtrLcv_ml_ANN_MLPG_getInnerPtr_const(const cv::Ptr<cv::ml::ANN_MLP>* instance) {
			return instance->get();
	}
	
	cv::ml::ANN_MLP* cv_PtrLcv_ml_ANN_MLPG_getInnerPtrMut(cv::Ptr<cv::ml::ANN_MLP>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ml_ANN_MLPG_delete(cv::Ptr<cv::ml::ANN_MLP>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ml_ANN_MLPG_to_PtrOfAlgorithm(cv::Ptr<cv::ml::ANN_MLP>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::ml::StatModel>* cv_PtrLcv_ml_ANN_MLPG_to_PtrOfStatModel(cv::Ptr<cv::ml::ANN_MLP>* instance) {
			return new cv::Ptr<cv::ml::StatModel>(instance->dynamicCast<cv::ml::StatModel>());
	}
	
}

extern "C" {
	const cv::ml::Boost* cv_PtrLcv_ml_BoostG_getInnerPtr_const(const cv::Ptr<cv::ml::Boost>* instance) {
			return instance->get();
	}
	
	cv::ml::Boost* cv_PtrLcv_ml_BoostG_getInnerPtrMut(cv::Ptr<cv::ml::Boost>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ml_BoostG_delete(cv::Ptr<cv::ml::Boost>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ml_BoostG_to_PtrOfAlgorithm(cv::Ptr<cv::ml::Boost>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::ml::DTrees>* cv_PtrLcv_ml_BoostG_to_PtrOfDTrees(cv::Ptr<cv::ml::Boost>* instance) {
			return new cv::Ptr<cv::ml::DTrees>(instance->dynamicCast<cv::ml::DTrees>());
	}
	
	cv::Ptr<cv::ml::StatModel>* cv_PtrLcv_ml_BoostG_to_PtrOfStatModel(cv::Ptr<cv::ml::Boost>* instance) {
			return new cv::Ptr<cv::ml::StatModel>(instance->dynamicCast<cv::ml::StatModel>());
	}
	
}

extern "C" {
	const cv::ml::DTrees* cv_PtrLcv_ml_DTreesG_getInnerPtr_const(const cv::Ptr<cv::ml::DTrees>* instance) {
			return instance->get();
	}
	
	cv::ml::DTrees* cv_PtrLcv_ml_DTreesG_getInnerPtrMut(cv::Ptr<cv::ml::DTrees>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ml_DTreesG_delete(cv::Ptr<cv::ml::DTrees>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ml_DTreesG_to_PtrOfAlgorithm(cv::Ptr<cv::ml::DTrees>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::ml::StatModel>* cv_PtrLcv_ml_DTreesG_to_PtrOfStatModel(cv::Ptr<cv::ml::DTrees>* instance) {
			return new cv::Ptr<cv::ml::StatModel>(instance->dynamicCast<cv::ml::StatModel>());
	}
	
}

extern "C" {
	const cv::ml::EM* cv_PtrLcv_ml_EMG_getInnerPtr_const(const cv::Ptr<cv::ml::EM>* instance) {
			return instance->get();
	}
	
	cv::ml::EM* cv_PtrLcv_ml_EMG_getInnerPtrMut(cv::Ptr<cv::ml::EM>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ml_EMG_delete(cv::Ptr<cv::ml::EM>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ml_EMG_to_PtrOfAlgorithm(cv::Ptr<cv::ml::EM>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::ml::StatModel>* cv_PtrLcv_ml_EMG_to_PtrOfStatModel(cv::Ptr<cv::ml::EM>* instance) {
			return new cv::Ptr<cv::ml::StatModel>(instance->dynamicCast<cv::ml::StatModel>());
	}
	
}

extern "C" {
	const cv::ml::KNearest* cv_PtrLcv_ml_KNearestG_getInnerPtr_const(const cv::Ptr<cv::ml::KNearest>* instance) {
			return instance->get();
	}
	
	cv::ml::KNearest* cv_PtrLcv_ml_KNearestG_getInnerPtrMut(cv::Ptr<cv::ml::KNearest>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ml_KNearestG_delete(cv::Ptr<cv::ml::KNearest>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ml_KNearestG_to_PtrOfAlgorithm(cv::Ptr<cv::ml::KNearest>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::ml::StatModel>* cv_PtrLcv_ml_KNearestG_to_PtrOfStatModel(cv::Ptr<cv::ml::KNearest>* instance) {
			return new cv::Ptr<cv::ml::StatModel>(instance->dynamicCast<cv::ml::StatModel>());
	}
	
}

extern "C" {
	const cv::ml::LogisticRegression* cv_PtrLcv_ml_LogisticRegressionG_getInnerPtr_const(const cv::Ptr<cv::ml::LogisticRegression>* instance) {
			return instance->get();
	}
	
	cv::ml::LogisticRegression* cv_PtrLcv_ml_LogisticRegressionG_getInnerPtrMut(cv::Ptr<cv::ml::LogisticRegression>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ml_LogisticRegressionG_delete(cv::Ptr<cv::ml::LogisticRegression>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ml_LogisticRegressionG_to_PtrOfAlgorithm(cv::Ptr<cv::ml::LogisticRegression>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::ml::StatModel>* cv_PtrLcv_ml_LogisticRegressionG_to_PtrOfStatModel(cv::Ptr<cv::ml::LogisticRegression>* instance) {
			return new cv::Ptr<cv::ml::StatModel>(instance->dynamicCast<cv::ml::StatModel>());
	}
	
}

extern "C" {
	const cv::ml::NormalBayesClassifier* cv_PtrLcv_ml_NormalBayesClassifierG_getInnerPtr_const(const cv::Ptr<cv::ml::NormalBayesClassifier>* instance) {
			return instance->get();
	}
	
	cv::ml::NormalBayesClassifier* cv_PtrLcv_ml_NormalBayesClassifierG_getInnerPtrMut(cv::Ptr<cv::ml::NormalBayesClassifier>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ml_NormalBayesClassifierG_delete(cv::Ptr<cv::ml::NormalBayesClassifier>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ml_NormalBayesClassifierG_to_PtrOfAlgorithm(cv::Ptr<cv::ml::NormalBayesClassifier>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::ml::StatModel>* cv_PtrLcv_ml_NormalBayesClassifierG_to_PtrOfStatModel(cv::Ptr<cv::ml::NormalBayesClassifier>* instance) {
			return new cv::Ptr<cv::ml::StatModel>(instance->dynamicCast<cv::ml::StatModel>());
	}
	
}

extern "C" {
	const cv::ml::ParamGrid* cv_PtrLcv_ml_ParamGridG_getInnerPtr_const(const cv::Ptr<cv::ml::ParamGrid>* instance) {
			return instance->get();
	}
	
	cv::ml::ParamGrid* cv_PtrLcv_ml_ParamGridG_getInnerPtrMut(cv::Ptr<cv::ml::ParamGrid>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ml_ParamGridG_delete(cv::Ptr<cv::ml::ParamGrid>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::ml::ParamGrid>* cv_PtrLcv_ml_ParamGridG_new_const_ParamGrid(cv::ml::ParamGrid* val) {
			return new cv::Ptr<cv::ml::ParamGrid>(val);
	}
	
}

extern "C" {
	const cv::ml::RTrees* cv_PtrLcv_ml_RTreesG_getInnerPtr_const(const cv::Ptr<cv::ml::RTrees>* instance) {
			return instance->get();
	}
	
	cv::ml::RTrees* cv_PtrLcv_ml_RTreesG_getInnerPtrMut(cv::Ptr<cv::ml::RTrees>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ml_RTreesG_delete(cv::Ptr<cv::ml::RTrees>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ml_RTreesG_to_PtrOfAlgorithm(cv::Ptr<cv::ml::RTrees>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::ml::DTrees>* cv_PtrLcv_ml_RTreesG_to_PtrOfDTrees(cv::Ptr<cv::ml::RTrees>* instance) {
			return new cv::Ptr<cv::ml::DTrees>(instance->dynamicCast<cv::ml::DTrees>());
	}
	
	cv::Ptr<cv::ml::StatModel>* cv_PtrLcv_ml_RTreesG_to_PtrOfStatModel(cv::Ptr<cv::ml::RTrees>* instance) {
			return new cv::Ptr<cv::ml::StatModel>(instance->dynamicCast<cv::ml::StatModel>());
	}
	
}

extern "C" {
	const cv::ml::SVM* cv_PtrLcv_ml_SVMG_getInnerPtr_const(const cv::Ptr<cv::ml::SVM>* instance) {
			return instance->get();
	}
	
	cv::ml::SVM* cv_PtrLcv_ml_SVMG_getInnerPtrMut(cv::Ptr<cv::ml::SVM>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ml_SVMG_delete(cv::Ptr<cv::ml::SVM>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ml_SVMG_to_PtrOfAlgorithm(cv::Ptr<cv::ml::SVM>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::ml::StatModel>* cv_PtrLcv_ml_SVMG_to_PtrOfStatModel(cv::Ptr<cv::ml::SVM>* instance) {
			return new cv::Ptr<cv::ml::StatModel>(instance->dynamicCast<cv::ml::StatModel>());
	}
	
}

extern "C" {
	const cv::ml::SVMSGD* cv_PtrLcv_ml_SVMSGDG_getInnerPtr_const(const cv::Ptr<cv::ml::SVMSGD>* instance) {
			return instance->get();
	}
	
	cv::ml::SVMSGD* cv_PtrLcv_ml_SVMSGDG_getInnerPtrMut(cv::Ptr<cv::ml::SVMSGD>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ml_SVMSGDG_delete(cv::Ptr<cv::ml::SVMSGD>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ml_SVMSGDG_to_PtrOfAlgorithm(cv::Ptr<cv::ml::SVMSGD>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::ml::StatModel>* cv_PtrLcv_ml_SVMSGDG_to_PtrOfStatModel(cv::Ptr<cv::ml::SVMSGD>* instance) {
			return new cv::Ptr<cv::ml::StatModel>(instance->dynamicCast<cv::ml::StatModel>());
	}
	
}

extern "C" {
	const cv::ml::SVM::Kernel* cv_PtrLcv_ml_SVM_KernelG_getInnerPtr_const(const cv::Ptr<cv::ml::SVM::Kernel>* instance) {
			return instance->get();
	}
	
	cv::ml::SVM::Kernel* cv_PtrLcv_ml_SVM_KernelG_getInnerPtrMut(cv::Ptr<cv::ml::SVM::Kernel>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ml_SVM_KernelG_delete(cv::Ptr<cv::ml::SVM::Kernel>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ml_SVM_KernelG_to_PtrOfAlgorithm(cv::Ptr<cv::ml::SVM::Kernel>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::ml::StatModel* cv_PtrLcv_ml_StatModelG_getInnerPtr_const(const cv::Ptr<cv::ml::StatModel>* instance) {
			return instance->get();
	}
	
	cv::ml::StatModel* cv_PtrLcv_ml_StatModelG_getInnerPtrMut(cv::Ptr<cv::ml::StatModel>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ml_StatModelG_delete(cv::Ptr<cv::ml::StatModel>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ml_StatModelG_to_PtrOfAlgorithm(cv::Ptr<cv::ml::StatModel>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::ml::TrainData* cv_PtrLcv_ml_TrainDataG_getInnerPtr_const(const cv::Ptr<cv::ml::TrainData>* instance) {
			return instance->get();
	}
	
	cv::ml::TrainData* cv_PtrLcv_ml_TrainDataG_getInnerPtrMut(cv::Ptr<cv::ml::TrainData>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ml_TrainDataG_delete(cv::Ptr<cv::ml::TrainData>* instance) {
			delete instance;
	}
	
}

extern "C" {
	std::vector<cv::ml::DTrees::Node>* std_vectorLcv_ml_DTrees_NodeG_new_const() {
			std::vector<cv::ml::DTrees::Node>* ret = new std::vector<cv::ml::DTrees::Node>();
			return ret;
	}
	
	void std_vectorLcv_ml_DTrees_NodeG_delete(std::vector<cv::ml::DTrees::Node>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_ml_DTrees_NodeG_len_const(const std::vector<cv::ml::DTrees::Node>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_ml_DTrees_NodeG_isEmpty_const(const std::vector<cv::ml::DTrees::Node>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_ml_DTrees_NodeG_capacity_const(const std::vector<cv::ml::DTrees::Node>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_ml_DTrees_NodeG_shrinkToFit(std::vector<cv::ml::DTrees::Node>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_ml_DTrees_NodeG_reserve_size_t(std::vector<cv::ml::DTrees::Node>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_ml_DTrees_NodeG_remove_size_t(std::vector<cv::ml::DTrees::Node>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_ml_DTrees_NodeG_swap_size_t_size_t(std::vector<cv::ml::DTrees::Node>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_ml_DTrees_NodeG_clear(std::vector<cv::ml::DTrees::Node>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_ml_DTrees_NodeG_push_const_Node(std::vector<cv::ml::DTrees::Node>* instance, const cv::ml::DTrees::Node* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_ml_DTrees_NodeG_insert_size_t_const_Node(std::vector<cv::ml::DTrees::Node>* instance, size_t index, const cv::ml::DTrees::Node* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_ml_DTrees_NodeG_get_const_size_t(const std::vector<cv::ml::DTrees::Node>* instance, size_t index, cv::ml::DTrees::Node** ocvrs_return) {
			cv::ml::DTrees::Node ret = (*instance)[index];
			*ocvrs_return = new cv::ml::DTrees::Node(ret);
	}
	
	void std_vectorLcv_ml_DTrees_NodeG_set_size_t_const_Node(std::vector<cv::ml::DTrees::Node>* instance, size_t index, const cv::ml::DTrees::Node* val) {
			(*instance)[index] = *val;
	}
	
}


extern "C" {
	std::vector<cv::ml::DTrees::Split>* std_vectorLcv_ml_DTrees_SplitG_new_const() {
			std::vector<cv::ml::DTrees::Split>* ret = new std::vector<cv::ml::DTrees::Split>();
			return ret;
	}
	
	void std_vectorLcv_ml_DTrees_SplitG_delete(std::vector<cv::ml::DTrees::Split>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_ml_DTrees_SplitG_len_const(const std::vector<cv::ml::DTrees::Split>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_ml_DTrees_SplitG_isEmpty_const(const std::vector<cv::ml::DTrees::Split>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_ml_DTrees_SplitG_capacity_const(const std::vector<cv::ml::DTrees::Split>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_ml_DTrees_SplitG_shrinkToFit(std::vector<cv::ml::DTrees::Split>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_ml_DTrees_SplitG_reserve_size_t(std::vector<cv::ml::DTrees::Split>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_ml_DTrees_SplitG_remove_size_t(std::vector<cv::ml::DTrees::Split>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_ml_DTrees_SplitG_swap_size_t_size_t(std::vector<cv::ml::DTrees::Split>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_ml_DTrees_SplitG_clear(std::vector<cv::ml::DTrees::Split>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_ml_DTrees_SplitG_push_const_Split(std::vector<cv::ml::DTrees::Split>* instance, const cv::ml::DTrees::Split* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_ml_DTrees_SplitG_insert_size_t_const_Split(std::vector<cv::ml::DTrees::Split>* instance, size_t index, const cv::ml::DTrees::Split* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_ml_DTrees_SplitG_get_const_size_t(const std::vector<cv::ml::DTrees::Split>* instance, size_t index, cv::ml::DTrees::Split** ocvrs_return) {
			cv::ml::DTrees::Split ret = (*instance)[index];
			*ocvrs_return = new cv::ml::DTrees::Split(ret);
	}
	
	void std_vectorLcv_ml_DTrees_SplitG_set_size_t_const_Split(std::vector<cv::ml::DTrees::Split>* instance, size_t index, const cv::ml::DTrees::Split* val) {
			(*instance)[index] = *val;
	}
	
}



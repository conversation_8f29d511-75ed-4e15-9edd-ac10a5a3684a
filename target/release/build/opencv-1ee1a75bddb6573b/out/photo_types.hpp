extern "C" {
	const cv::AlignExposures* cv_PtrLcv_AlignExposuresG_getInnerPtr_const(const cv::Ptr<cv::AlignExposures>* instance) {
			return instance->get();
	}
	
	cv::AlignExposures* cv_PtrLcv_AlignExposuresG_getInnerPtrMut(cv::Ptr<cv::AlignExposures>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_AlignExposuresG_delete(cv::Ptr<cv::AlignExposures>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_AlignExposuresG_to_PtrOfAlgorithm(cv::Ptr<cv::AlignExposures>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::AlignMTB* cv_PtrLcv_AlignMTBG_getInnerPtr_const(const cv::Ptr<cv::AlignMTB>* instance) {
			return instance->get();
	}
	
	cv::AlignMTB* cv_PtrLcv_AlignMTBG_getInnerPtrMut(cv::Ptr<cv::AlignMTB>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_AlignMTBG_delete(cv::Ptr<cv::AlignMTB>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_AlignMTBG_to_PtrOfAlgorithm(cv::Ptr<cv::AlignMTB>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::AlignExposures>* cv_PtrLcv_AlignMTBG_to_PtrOfAlignExposures(cv::Ptr<cv::AlignMTB>* instance) {
			return new cv::Ptr<cv::AlignExposures>(instance->dynamicCast<cv::AlignExposures>());
	}
	
}

extern "C" {
	const cv::CalibrateCRF* cv_PtrLcv_CalibrateCRFG_getInnerPtr_const(const cv::Ptr<cv::CalibrateCRF>* instance) {
			return instance->get();
	}
	
	cv::CalibrateCRF* cv_PtrLcv_CalibrateCRFG_getInnerPtrMut(cv::Ptr<cv::CalibrateCRF>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_CalibrateCRFG_delete(cv::Ptr<cv::CalibrateCRF>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_CalibrateCRFG_to_PtrOfAlgorithm(cv::Ptr<cv::CalibrateCRF>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::CalibrateDebevec* cv_PtrLcv_CalibrateDebevecG_getInnerPtr_const(const cv::Ptr<cv::CalibrateDebevec>* instance) {
			return instance->get();
	}
	
	cv::CalibrateDebevec* cv_PtrLcv_CalibrateDebevecG_getInnerPtrMut(cv::Ptr<cv::CalibrateDebevec>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_CalibrateDebevecG_delete(cv::Ptr<cv::CalibrateDebevec>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_CalibrateDebevecG_to_PtrOfAlgorithm(cv::Ptr<cv::CalibrateDebevec>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::CalibrateCRF>* cv_PtrLcv_CalibrateDebevecG_to_PtrOfCalibrateCRF(cv::Ptr<cv::CalibrateDebevec>* instance) {
			return new cv::Ptr<cv::CalibrateCRF>(instance->dynamicCast<cv::CalibrateCRF>());
	}
	
}

extern "C" {
	const cv::CalibrateRobertson* cv_PtrLcv_CalibrateRobertsonG_getInnerPtr_const(const cv::Ptr<cv::CalibrateRobertson>* instance) {
			return instance->get();
	}
	
	cv::CalibrateRobertson* cv_PtrLcv_CalibrateRobertsonG_getInnerPtrMut(cv::Ptr<cv::CalibrateRobertson>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_CalibrateRobertsonG_delete(cv::Ptr<cv::CalibrateRobertson>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_CalibrateRobertsonG_to_PtrOfAlgorithm(cv::Ptr<cv::CalibrateRobertson>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::CalibrateCRF>* cv_PtrLcv_CalibrateRobertsonG_to_PtrOfCalibrateCRF(cv::Ptr<cv::CalibrateRobertson>* instance) {
			return new cv::Ptr<cv::CalibrateCRF>(instance->dynamicCast<cv::CalibrateCRF>());
	}
	
}

extern "C" {
	const cv::MergeDebevec* cv_PtrLcv_MergeDebevecG_getInnerPtr_const(const cv::Ptr<cv::MergeDebevec>* instance) {
			return instance->get();
	}
	
	cv::MergeDebevec* cv_PtrLcv_MergeDebevecG_getInnerPtrMut(cv::Ptr<cv::MergeDebevec>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_MergeDebevecG_delete(cv::Ptr<cv::MergeDebevec>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_MergeDebevecG_to_PtrOfAlgorithm(cv::Ptr<cv::MergeDebevec>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::MergeExposures>* cv_PtrLcv_MergeDebevecG_to_PtrOfMergeExposures(cv::Ptr<cv::MergeDebevec>* instance) {
			return new cv::Ptr<cv::MergeExposures>(instance->dynamicCast<cv::MergeExposures>());
	}
	
}

extern "C" {
	const cv::MergeExposures* cv_PtrLcv_MergeExposuresG_getInnerPtr_const(const cv::Ptr<cv::MergeExposures>* instance) {
			return instance->get();
	}
	
	cv::MergeExposures* cv_PtrLcv_MergeExposuresG_getInnerPtrMut(cv::Ptr<cv::MergeExposures>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_MergeExposuresG_delete(cv::Ptr<cv::MergeExposures>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_MergeExposuresG_to_PtrOfAlgorithm(cv::Ptr<cv::MergeExposures>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::MergeMertens* cv_PtrLcv_MergeMertensG_getInnerPtr_const(const cv::Ptr<cv::MergeMertens>* instance) {
			return instance->get();
	}
	
	cv::MergeMertens* cv_PtrLcv_MergeMertensG_getInnerPtrMut(cv::Ptr<cv::MergeMertens>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_MergeMertensG_delete(cv::Ptr<cv::MergeMertens>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_MergeMertensG_to_PtrOfAlgorithm(cv::Ptr<cv::MergeMertens>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::MergeExposures>* cv_PtrLcv_MergeMertensG_to_PtrOfMergeExposures(cv::Ptr<cv::MergeMertens>* instance) {
			return new cv::Ptr<cv::MergeExposures>(instance->dynamicCast<cv::MergeExposures>());
	}
	
}

extern "C" {
	const cv::MergeRobertson* cv_PtrLcv_MergeRobertsonG_getInnerPtr_const(const cv::Ptr<cv::MergeRobertson>* instance) {
			return instance->get();
	}
	
	cv::MergeRobertson* cv_PtrLcv_MergeRobertsonG_getInnerPtrMut(cv::Ptr<cv::MergeRobertson>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_MergeRobertsonG_delete(cv::Ptr<cv::MergeRobertson>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_MergeRobertsonG_to_PtrOfAlgorithm(cv::Ptr<cv::MergeRobertson>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::MergeExposures>* cv_PtrLcv_MergeRobertsonG_to_PtrOfMergeExposures(cv::Ptr<cv::MergeRobertson>* instance) {
			return new cv::Ptr<cv::MergeExposures>(instance->dynamicCast<cv::MergeExposures>());
	}
	
}

extern "C" {
	const cv::Tonemap* cv_PtrLcv_TonemapG_getInnerPtr_const(const cv::Ptr<cv::Tonemap>* instance) {
			return instance->get();
	}
	
	cv::Tonemap* cv_PtrLcv_TonemapG_getInnerPtrMut(cv::Ptr<cv::Tonemap>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_TonemapG_delete(cv::Ptr<cv::Tonemap>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_TonemapG_to_PtrOfAlgorithm(cv::Ptr<cv::Tonemap>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::TonemapDrago* cv_PtrLcv_TonemapDragoG_getInnerPtr_const(const cv::Ptr<cv::TonemapDrago>* instance) {
			return instance->get();
	}
	
	cv::TonemapDrago* cv_PtrLcv_TonemapDragoG_getInnerPtrMut(cv::Ptr<cv::TonemapDrago>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_TonemapDragoG_delete(cv::Ptr<cv::TonemapDrago>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_TonemapDragoG_to_PtrOfAlgorithm(cv::Ptr<cv::TonemapDrago>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::Tonemap>* cv_PtrLcv_TonemapDragoG_to_PtrOfTonemap(cv::Ptr<cv::TonemapDrago>* instance) {
			return new cv::Ptr<cv::Tonemap>(instance->dynamicCast<cv::Tonemap>());
	}
	
}

extern "C" {
	const cv::TonemapMantiuk* cv_PtrLcv_TonemapMantiukG_getInnerPtr_const(const cv::Ptr<cv::TonemapMantiuk>* instance) {
			return instance->get();
	}
	
	cv::TonemapMantiuk* cv_PtrLcv_TonemapMantiukG_getInnerPtrMut(cv::Ptr<cv::TonemapMantiuk>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_TonemapMantiukG_delete(cv::Ptr<cv::TonemapMantiuk>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_TonemapMantiukG_to_PtrOfAlgorithm(cv::Ptr<cv::TonemapMantiuk>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::Tonemap>* cv_PtrLcv_TonemapMantiukG_to_PtrOfTonemap(cv::Ptr<cv::TonemapMantiuk>* instance) {
			return new cv::Ptr<cv::Tonemap>(instance->dynamicCast<cv::Tonemap>());
	}
	
}

extern "C" {
	const cv::TonemapReinhard* cv_PtrLcv_TonemapReinhardG_getInnerPtr_const(const cv::Ptr<cv::TonemapReinhard>* instance) {
			return instance->get();
	}
	
	cv::TonemapReinhard* cv_PtrLcv_TonemapReinhardG_getInnerPtrMut(cv::Ptr<cv::TonemapReinhard>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_TonemapReinhardG_delete(cv::Ptr<cv::TonemapReinhard>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_TonemapReinhardG_to_PtrOfAlgorithm(cv::Ptr<cv::TonemapReinhard>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::Tonemap>* cv_PtrLcv_TonemapReinhardG_to_PtrOfTonemap(cv::Ptr<cv::TonemapReinhard>* instance) {
			return new cv::Ptr<cv::Tonemap>(instance->dynamicCast<cv::Tonemap>());
	}
	
}


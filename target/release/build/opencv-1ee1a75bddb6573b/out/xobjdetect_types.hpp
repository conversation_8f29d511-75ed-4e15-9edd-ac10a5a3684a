extern "C" {
	const cv::xobjdetect::WBDetector* cv_PtrLcv_xobjdetect_WBDetectorG_getInnerPtr_const(const cv::Ptr<cv::xobjdetect::WBDetector>* instance) {
			return instance->get();
	}
	
	cv::xobjdetect::WBDetector* cv_PtrLcv_xobjdetect_WBDetectorG_getInnerPtrMut(cv::Ptr<cv::xobjdetect::WBDetector>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_xobjdetect_WBDetectorG_delete(cv::Ptr<cv::xobjdetect::WBDetector>* instance) {
			delete instance;
	}
	
}


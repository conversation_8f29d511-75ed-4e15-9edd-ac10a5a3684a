extern "C" {
	const cv::rapid::GOSTracker* cv_PtrLcv_rapid_GOSTrackerG_getInnerPtr_const(const cv::Ptr<cv::rapid::GOSTracker>* instance) {
			return instance->get();
	}
	
	cv::rapid::GOSTracker* cv_PtrLcv_rapid_GOSTrackerG_getInnerPtrMut(cv::Ptr<cv::rapid::GOSTracker>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_rapid_GOSTrackerG_delete(cv::Ptr<cv::rapid::GOSTracker>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_rapid_GOSTrackerG_to_PtrOfAlgorithm(cv::Ptr<cv::rapid::GOSTracker>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::rapid::Tracker>* cv_PtrLcv_rapid_GOSTrackerG_to_PtrOfRapid_Tracker(cv::Ptr<cv::rapid::GOSTracker>* instance) {
			return new cv::Ptr<cv::rapid::Tracker>(instance->dynamicCast<cv::rapid::Tracker>());
	}
	
}

extern "C" {
	const cv::rapid::OLSTracker* cv_PtrLcv_rapid_OLSTrackerG_getInnerPtr_const(const cv::Ptr<cv::rapid::OLSTracker>* instance) {
			return instance->get();
	}
	
	cv::rapid::OLSTracker* cv_PtrLcv_rapid_OLSTrackerG_getInnerPtrMut(cv::Ptr<cv::rapid::OLSTracker>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_rapid_OLSTrackerG_delete(cv::Ptr<cv::rapid::OLSTracker>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_rapid_OLSTrackerG_to_PtrOfAlgorithm(cv::Ptr<cv::rapid::OLSTracker>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::rapid::Tracker>* cv_PtrLcv_rapid_OLSTrackerG_to_PtrOfRapid_Tracker(cv::Ptr<cv::rapid::OLSTracker>* instance) {
			return new cv::Ptr<cv::rapid::Tracker>(instance->dynamicCast<cv::rapid::Tracker>());
	}
	
}

extern "C" {
	const cv::rapid::Rapid* cv_PtrLcv_rapid_RapidG_getInnerPtr_const(const cv::Ptr<cv::rapid::Rapid>* instance) {
			return instance->get();
	}
	
	cv::rapid::Rapid* cv_PtrLcv_rapid_RapidG_getInnerPtrMut(cv::Ptr<cv::rapid::Rapid>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_rapid_RapidG_delete(cv::Ptr<cv::rapid::Rapid>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_rapid_RapidG_to_PtrOfAlgorithm(cv::Ptr<cv::rapid::Rapid>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::rapid::Tracker>* cv_PtrLcv_rapid_RapidG_to_PtrOfRapid_Tracker(cv::Ptr<cv::rapid::Rapid>* instance) {
			return new cv::Ptr<cv::rapid::Tracker>(instance->dynamicCast<cv::rapid::Tracker>());
	}
	
}

extern "C" {
	const cv::rapid::Tracker* cv_PtrLcv_rapid_TrackerG_getInnerPtr_const(const cv::Ptr<cv::rapid::Tracker>* instance) {
			return instance->get();
	}
	
	cv::rapid::Tracker* cv_PtrLcv_rapid_TrackerG_getInnerPtrMut(cv::Ptr<cv::rapid::Tracker>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_rapid_TrackerG_delete(cv::Ptr<cv::rapid::Tracker>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_rapid_TrackerG_to_PtrOfAlgorithm(cv::Ptr<cv::rapid::Tracker>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}


extern "C" {
	const cv::quality::QualityBRISQUE* cv_PtrLcv_quality_QualityBRISQUEG_getInnerPtr_const(const cv::Ptr<cv::quality::QualityBRISQUE>* instance) {
			return instance->get();
	}
	
	cv::quality::QualityBRISQUE* cv_PtrLcv_quality_QualityBRISQUEG_getInnerPtrMut(cv::Ptr<cv::quality::QualityBRISQUE>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_quality_QualityBRISQUEG_delete(cv::Ptr<cv::quality::QualityBRISQUE>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_quality_QualityBRISQUEG_to_PtrOfAlgorithm(cv::Ptr<cv::quality::QualityBRISQUE>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::quality::QualityBase>* cv_PtrLcv_quality_QualityBRISQUEG_to_PtrOfQualityBase(cv::Ptr<cv::quality::QualityBRISQUE>* instance) {
			return new cv::Ptr<cv::quality::QualityBase>(instance->dynamicCast<cv::quality::QualityBase>());
	}
	
	cv::Ptr<cv::quality::QualityBRISQUE>* cv_PtrLcv_quality_QualityBRISQUEG_new_const_QualityBRISQUE(cv::quality::QualityBRISQUE* val) {
			return new cv::Ptr<cv::quality::QualityBRISQUE>(val);
	}
	
}

extern "C" {
	const cv::quality::QualityBase* cv_PtrLcv_quality_QualityBaseG_getInnerPtr_const(const cv::Ptr<cv::quality::QualityBase>* instance) {
			return instance->get();
	}
	
	cv::quality::QualityBase* cv_PtrLcv_quality_QualityBaseG_getInnerPtrMut(cv::Ptr<cv::quality::QualityBase>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_quality_QualityBaseG_delete(cv::Ptr<cv::quality::QualityBase>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_quality_QualityBaseG_to_PtrOfAlgorithm(cv::Ptr<cv::quality::QualityBase>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::quality::QualityGMSD* cv_PtrLcv_quality_QualityGMSDG_getInnerPtr_const(const cv::Ptr<cv::quality::QualityGMSD>* instance) {
			return instance->get();
	}
	
	cv::quality::QualityGMSD* cv_PtrLcv_quality_QualityGMSDG_getInnerPtrMut(cv::Ptr<cv::quality::QualityGMSD>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_quality_QualityGMSDG_delete(cv::Ptr<cv::quality::QualityGMSD>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_quality_QualityGMSDG_to_PtrOfAlgorithm(cv::Ptr<cv::quality::QualityGMSD>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::quality::QualityBase>* cv_PtrLcv_quality_QualityGMSDG_to_PtrOfQualityBase(cv::Ptr<cv::quality::QualityGMSD>* instance) {
			return new cv::Ptr<cv::quality::QualityBase>(instance->dynamicCast<cv::quality::QualityBase>());
	}
	
	cv::Ptr<cv::quality::QualityGMSD>* cv_PtrLcv_quality_QualityGMSDG_new_const_QualityGMSD(cv::quality::QualityGMSD* val) {
			return new cv::Ptr<cv::quality::QualityGMSD>(val);
	}
	
}

extern "C" {
	const cv::quality::QualityMSE* cv_PtrLcv_quality_QualityMSEG_getInnerPtr_const(const cv::Ptr<cv::quality::QualityMSE>* instance) {
			return instance->get();
	}
	
	cv::quality::QualityMSE* cv_PtrLcv_quality_QualityMSEG_getInnerPtrMut(cv::Ptr<cv::quality::QualityMSE>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_quality_QualityMSEG_delete(cv::Ptr<cv::quality::QualityMSE>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_quality_QualityMSEG_to_PtrOfAlgorithm(cv::Ptr<cv::quality::QualityMSE>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::quality::QualityBase>* cv_PtrLcv_quality_QualityMSEG_to_PtrOfQualityBase(cv::Ptr<cv::quality::QualityMSE>* instance) {
			return new cv::Ptr<cv::quality::QualityBase>(instance->dynamicCast<cv::quality::QualityBase>());
	}
	
	cv::Ptr<cv::quality::QualityMSE>* cv_PtrLcv_quality_QualityMSEG_new_const_QualityMSE(cv::quality::QualityMSE* val) {
			return new cv::Ptr<cv::quality::QualityMSE>(val);
	}
	
}

extern "C" {
	const cv::quality::QualityPSNR* cv_PtrLcv_quality_QualityPSNRG_getInnerPtr_const(const cv::Ptr<cv::quality::QualityPSNR>* instance) {
			return instance->get();
	}
	
	cv::quality::QualityPSNR* cv_PtrLcv_quality_QualityPSNRG_getInnerPtrMut(cv::Ptr<cv::quality::QualityPSNR>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_quality_QualityPSNRG_delete(cv::Ptr<cv::quality::QualityPSNR>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_quality_QualityPSNRG_to_PtrOfAlgorithm(cv::Ptr<cv::quality::QualityPSNR>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::quality::QualityBase>* cv_PtrLcv_quality_QualityPSNRG_to_PtrOfQualityBase(cv::Ptr<cv::quality::QualityPSNR>* instance) {
			return new cv::Ptr<cv::quality::QualityBase>(instance->dynamicCast<cv::quality::QualityBase>());
	}
	
	cv::Ptr<cv::quality::QualityPSNR>* cv_PtrLcv_quality_QualityPSNRG_new_const_QualityPSNR(cv::quality::QualityPSNR* val) {
			return new cv::Ptr<cv::quality::QualityPSNR>(val);
	}
	
}

extern "C" {
	const cv::quality::QualitySSIM* cv_PtrLcv_quality_QualitySSIMG_getInnerPtr_const(const cv::Ptr<cv::quality::QualitySSIM>* instance) {
			return instance->get();
	}
	
	cv::quality::QualitySSIM* cv_PtrLcv_quality_QualitySSIMG_getInnerPtrMut(cv::Ptr<cv::quality::QualitySSIM>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_quality_QualitySSIMG_delete(cv::Ptr<cv::quality::QualitySSIM>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_quality_QualitySSIMG_to_PtrOfAlgorithm(cv::Ptr<cv::quality::QualitySSIM>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::quality::QualityBase>* cv_PtrLcv_quality_QualitySSIMG_to_PtrOfQualityBase(cv::Ptr<cv::quality::QualitySSIM>* instance) {
			return new cv::Ptr<cv::quality::QualityBase>(instance->dynamicCast<cv::quality::QualityBase>());
	}
	
	cv::Ptr<cv::quality::QualitySSIM>* cv_PtrLcv_quality_QualitySSIMG_new_const_QualitySSIM(cv::quality::QualitySSIM* val) {
			return new cv::Ptr<cv::quality::QualitySSIM>(val);
	}
	
}


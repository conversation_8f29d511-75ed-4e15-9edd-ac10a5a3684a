extern "C" {
	const cv::xphoto::GrayworldWB* cv_PtrLcv_xphoto_GrayworldWBG_getInnerPtr_const(const cv::Ptr<cv::xphoto::GrayworldWB>* instance) {
			return instance->get();
	}
	
	cv::xphoto::GrayworldWB* cv_PtrLcv_xphoto_GrayworldWBG_getInnerPtrMut(cv::Ptr<cv::xphoto::GrayworldWB>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_xphoto_GrayworldWBG_delete(cv::Ptr<cv::xphoto::GrayworldWB>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_xphoto_GrayworldWBG_to_PtrOfAlgorithm(cv::Ptr<cv::xphoto::GrayworldWB>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::xphoto::WhiteBalancer>* cv_PtrLcv_xphoto_GrayworldWBG_to_PtrOfWhiteBalancer(cv::Ptr<cv::xphoto::GrayworldWB>* instance) {
			return new cv::Ptr<cv::xphoto::WhiteBalancer>(instance->dynamicCast<cv::xphoto::WhiteBalancer>());
	}
	
}

extern "C" {
	const cv::xphoto::LearningBasedWB* cv_PtrLcv_xphoto_LearningBasedWBG_getInnerPtr_const(const cv::Ptr<cv::xphoto::LearningBasedWB>* instance) {
			return instance->get();
	}
	
	cv::xphoto::LearningBasedWB* cv_PtrLcv_xphoto_LearningBasedWBG_getInnerPtrMut(cv::Ptr<cv::xphoto::LearningBasedWB>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_xphoto_LearningBasedWBG_delete(cv::Ptr<cv::xphoto::LearningBasedWB>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_xphoto_LearningBasedWBG_to_PtrOfAlgorithm(cv::Ptr<cv::xphoto::LearningBasedWB>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::xphoto::WhiteBalancer>* cv_PtrLcv_xphoto_LearningBasedWBG_to_PtrOfWhiteBalancer(cv::Ptr<cv::xphoto::LearningBasedWB>* instance) {
			return new cv::Ptr<cv::xphoto::WhiteBalancer>(instance->dynamicCast<cv::xphoto::WhiteBalancer>());
	}
	
}

extern "C" {
	const cv::xphoto::SimpleWB* cv_PtrLcv_xphoto_SimpleWBG_getInnerPtr_const(const cv::Ptr<cv::xphoto::SimpleWB>* instance) {
			return instance->get();
	}
	
	cv::xphoto::SimpleWB* cv_PtrLcv_xphoto_SimpleWBG_getInnerPtrMut(cv::Ptr<cv::xphoto::SimpleWB>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_xphoto_SimpleWBG_delete(cv::Ptr<cv::xphoto::SimpleWB>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_xphoto_SimpleWBG_to_PtrOfAlgorithm(cv::Ptr<cv::xphoto::SimpleWB>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::xphoto::WhiteBalancer>* cv_PtrLcv_xphoto_SimpleWBG_to_PtrOfWhiteBalancer(cv::Ptr<cv::xphoto::SimpleWB>* instance) {
			return new cv::Ptr<cv::xphoto::WhiteBalancer>(instance->dynamicCast<cv::xphoto::WhiteBalancer>());
	}
	
}

extern "C" {
	const cv::xphoto::TonemapDurand* cv_PtrLcv_xphoto_TonemapDurandG_getInnerPtr_const(const cv::Ptr<cv::xphoto::TonemapDurand>* instance) {
			return instance->get();
	}
	
	cv::xphoto::TonemapDurand* cv_PtrLcv_xphoto_TonemapDurandG_getInnerPtrMut(cv::Ptr<cv::xphoto::TonemapDurand>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_xphoto_TonemapDurandG_delete(cv::Ptr<cv::xphoto::TonemapDurand>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_xphoto_TonemapDurandG_to_PtrOfAlgorithm(cv::Ptr<cv::xphoto::TonemapDurand>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::Tonemap>* cv_PtrLcv_xphoto_TonemapDurandG_to_PtrOfTonemap(cv::Ptr<cv::xphoto::TonemapDurand>* instance) {
			return new cv::Ptr<cv::Tonemap>(instance->dynamicCast<cv::Tonemap>());
	}
	
}

extern "C" {
	const cv::xphoto::WhiteBalancer* cv_PtrLcv_xphoto_WhiteBalancerG_getInnerPtr_const(const cv::Ptr<cv::xphoto::WhiteBalancer>* instance) {
			return instance->get();
	}
	
	cv::xphoto::WhiteBalancer* cv_PtrLcv_xphoto_WhiteBalancerG_getInnerPtrMut(cv::Ptr<cv::xphoto::WhiteBalancer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_xphoto_WhiteBalancerG_delete(cv::Ptr<cv::xphoto::WhiteBalancer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_xphoto_WhiteBalancerG_to_PtrOfAlgorithm(cv::Ptr<cv::xphoto::WhiteBalancer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}


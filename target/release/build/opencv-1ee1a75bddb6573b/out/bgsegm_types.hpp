extern "C" {
	const cv::bgsegm::BackgroundSubtractorCNT* cv_PtrLcv_bgsegm_BackgroundSubtractorCNTG_getInnerPtr_const(const cv::Ptr<cv::bgsegm::BackgroundSubtractorCNT>* instance) {
			return instance->get();
	}
	
	cv::bgsegm::BackgroundSubtractorCNT* cv_PtrLcv_bgsegm_BackgroundSubtractorCNTG_getInnerPtrMut(cv::Ptr<cv::bgsegm::BackgroundSubtractorCNT>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_bgsegm_BackgroundSubtractorCNTG_delete(cv::Ptr<cv::bgsegm::BackgroundSubtractorCNT>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_bgsegm_BackgroundSubtractorCNTG_to_PtrOfAlgorithm(cv::Ptr<cv::bgsegm::BackgroundSubtractorCNT>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::BackgroundSubtractor>* cv_PtrLcv_bgsegm_BackgroundSubtractorCNTG_to_PtrOfBackgroundSubtractor(cv::Ptr<cv::bgsegm::BackgroundSubtractorCNT>* instance) {
			return new cv::Ptr<cv::BackgroundSubtractor>(instance->dynamicCast<cv::BackgroundSubtractor>());
	}
	
}

extern "C" {
	const cv::bgsegm::BackgroundSubtractorGMG* cv_PtrLcv_bgsegm_BackgroundSubtractorGMGG_getInnerPtr_const(const cv::Ptr<cv::bgsegm::BackgroundSubtractorGMG>* instance) {
			return instance->get();
	}
	
	cv::bgsegm::BackgroundSubtractorGMG* cv_PtrLcv_bgsegm_BackgroundSubtractorGMGG_getInnerPtrMut(cv::Ptr<cv::bgsegm::BackgroundSubtractorGMG>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_bgsegm_BackgroundSubtractorGMGG_delete(cv::Ptr<cv::bgsegm::BackgroundSubtractorGMG>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_bgsegm_BackgroundSubtractorGMGG_to_PtrOfAlgorithm(cv::Ptr<cv::bgsegm::BackgroundSubtractorGMG>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::BackgroundSubtractor>* cv_PtrLcv_bgsegm_BackgroundSubtractorGMGG_to_PtrOfBackgroundSubtractor(cv::Ptr<cv::bgsegm::BackgroundSubtractorGMG>* instance) {
			return new cv::Ptr<cv::BackgroundSubtractor>(instance->dynamicCast<cv::BackgroundSubtractor>());
	}
	
}

extern "C" {
	const cv::bgsegm::BackgroundSubtractorGSOC* cv_PtrLcv_bgsegm_BackgroundSubtractorGSOCG_getInnerPtr_const(const cv::Ptr<cv::bgsegm::BackgroundSubtractorGSOC>* instance) {
			return instance->get();
	}
	
	cv::bgsegm::BackgroundSubtractorGSOC* cv_PtrLcv_bgsegm_BackgroundSubtractorGSOCG_getInnerPtrMut(cv::Ptr<cv::bgsegm::BackgroundSubtractorGSOC>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_bgsegm_BackgroundSubtractorGSOCG_delete(cv::Ptr<cv::bgsegm::BackgroundSubtractorGSOC>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_bgsegm_BackgroundSubtractorGSOCG_to_PtrOfAlgorithm(cv::Ptr<cv::bgsegm::BackgroundSubtractorGSOC>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::BackgroundSubtractor>* cv_PtrLcv_bgsegm_BackgroundSubtractorGSOCG_to_PtrOfBackgroundSubtractor(cv::Ptr<cv::bgsegm::BackgroundSubtractorGSOC>* instance) {
			return new cv::Ptr<cv::BackgroundSubtractor>(instance->dynamicCast<cv::BackgroundSubtractor>());
	}
	
}

extern "C" {
	const cv::bgsegm::BackgroundSubtractorLSBP* cv_PtrLcv_bgsegm_BackgroundSubtractorLSBPG_getInnerPtr_const(const cv::Ptr<cv::bgsegm::BackgroundSubtractorLSBP>* instance) {
			return instance->get();
	}
	
	cv::bgsegm::BackgroundSubtractorLSBP* cv_PtrLcv_bgsegm_BackgroundSubtractorLSBPG_getInnerPtrMut(cv::Ptr<cv::bgsegm::BackgroundSubtractorLSBP>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_bgsegm_BackgroundSubtractorLSBPG_delete(cv::Ptr<cv::bgsegm::BackgroundSubtractorLSBP>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_bgsegm_BackgroundSubtractorLSBPG_to_PtrOfAlgorithm(cv::Ptr<cv::bgsegm::BackgroundSubtractorLSBP>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::BackgroundSubtractor>* cv_PtrLcv_bgsegm_BackgroundSubtractorLSBPG_to_PtrOfBackgroundSubtractor(cv::Ptr<cv::bgsegm::BackgroundSubtractorLSBP>* instance) {
			return new cv::Ptr<cv::BackgroundSubtractor>(instance->dynamicCast<cv::BackgroundSubtractor>());
	}
	
}

extern "C" {
	const cv::bgsegm::BackgroundSubtractorMOG* cv_PtrLcv_bgsegm_BackgroundSubtractorMOGG_getInnerPtr_const(const cv::Ptr<cv::bgsegm::BackgroundSubtractorMOG>* instance) {
			return instance->get();
	}
	
	cv::bgsegm::BackgroundSubtractorMOG* cv_PtrLcv_bgsegm_BackgroundSubtractorMOGG_getInnerPtrMut(cv::Ptr<cv::bgsegm::BackgroundSubtractorMOG>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_bgsegm_BackgroundSubtractorMOGG_delete(cv::Ptr<cv::bgsegm::BackgroundSubtractorMOG>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_bgsegm_BackgroundSubtractorMOGG_to_PtrOfAlgorithm(cv::Ptr<cv::bgsegm::BackgroundSubtractorMOG>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::BackgroundSubtractor>* cv_PtrLcv_bgsegm_BackgroundSubtractorMOGG_to_PtrOfBackgroundSubtractor(cv::Ptr<cv::bgsegm::BackgroundSubtractorMOG>* instance) {
			return new cv::Ptr<cv::BackgroundSubtractor>(instance->dynamicCast<cv::BackgroundSubtractor>());
	}
	
}

extern "C" {
	const cv::bgsegm::SyntheticSequenceGenerator* cv_PtrLcv_bgsegm_SyntheticSequenceGeneratorG_getInnerPtr_const(const cv::Ptr<cv::bgsegm::SyntheticSequenceGenerator>* instance) {
			return instance->get();
	}
	
	cv::bgsegm::SyntheticSequenceGenerator* cv_PtrLcv_bgsegm_SyntheticSequenceGeneratorG_getInnerPtrMut(cv::Ptr<cv::bgsegm::SyntheticSequenceGenerator>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_bgsegm_SyntheticSequenceGeneratorG_delete(cv::Ptr<cv::bgsegm::SyntheticSequenceGenerator>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_bgsegm_SyntheticSequenceGeneratorG_to_PtrOfAlgorithm(cv::Ptr<cv::bgsegm::SyntheticSequenceGenerator>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::bgsegm::SyntheticSequenceGenerator>* cv_PtrLcv_bgsegm_SyntheticSequenceGeneratorG_new_const_SyntheticSequenceGenerator(cv::bgsegm::SyntheticSequenceGenerator* val) {
			return new cv::Ptr<cv::bgsegm::SyntheticSequenceGenerator>(val);
	}
	
}


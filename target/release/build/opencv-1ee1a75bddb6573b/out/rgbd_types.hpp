extern "C" {
	const cv::colored_kinfu::ColoredKinFu* cv_PtrLcv_colored_kinfu_ColoredKinFuG_getInnerPtr_const(const cv::Ptr<cv::colored_kinfu::ColoredKinFu>* instance) {
			return instance->get();
	}
	
	cv::colored_kinfu::ColoredKinFu* cv_PtrLcv_colored_kinfu_ColoredKinFuG_getInnerPtrMut(cv::Ptr<cv::colored_kinfu::ColoredKinFu>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_colored_kinfu_ColoredKinFuG_delete(cv::Ptr<cv::colored_kinfu::ColoredKinFu>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::colored_kinfu::Params* cv_PtrLcv_colored_kinfu_ParamsG_getInnerPtr_const(const cv::Ptr<cv::colored_kinfu::Params>* instance) {
			return instance->get();
	}
	
	cv::colored_kinfu::Params* cv_PtrLcv_colored_kinfu_ParamsG_getInnerPtrMut(cv::Ptr<cv::colored_kinfu::Params>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_colored_kinfu_ParamsG_delete(cv::Ptr<cv::colored_kinfu::Params>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::colored_kinfu::Params>* cv_PtrLcv_colored_kinfu_ParamsG_new_const_Params(cv::colored_kinfu::Params* val) {
			return new cv::Ptr<cv::colored_kinfu::Params>(val);
	}
	
}

extern "C" {
	const cv::rgbd::DepthCleaner* cv_PtrLcv_rgbd_DepthCleanerG_getInnerPtr_const(const cv::Ptr<cv::rgbd::DepthCleaner>* instance) {
			return instance->get();
	}
	
	cv::rgbd::DepthCleaner* cv_PtrLcv_rgbd_DepthCleanerG_getInnerPtrMut(cv::Ptr<cv::rgbd::DepthCleaner>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_rgbd_DepthCleanerG_delete(cv::Ptr<cv::rgbd::DepthCleaner>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_rgbd_DepthCleanerG_to_PtrOfAlgorithm(cv::Ptr<cv::rgbd::DepthCleaner>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::rgbd::DepthCleaner>* cv_PtrLcv_rgbd_DepthCleanerG_new_const_DepthCleaner(cv::rgbd::DepthCleaner* val) {
			return new cv::Ptr<cv::rgbd::DepthCleaner>(val);
	}
	
}

extern "C" {
	const cv::dynafu::DynaFu* cv_PtrLcv_dynafu_DynaFuG_getInnerPtr_const(const cv::Ptr<cv::dynafu::DynaFu>* instance) {
			return instance->get();
	}
	
	cv::dynafu::DynaFu* cv_PtrLcv_dynafu_DynaFuG_getInnerPtrMut(cv::Ptr<cv::dynafu::DynaFu>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dynafu_DynaFuG_delete(cv::Ptr<cv::dynafu::DynaFu>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::rgbd::FastICPOdometry* cv_PtrLcv_rgbd_FastICPOdometryG_getInnerPtr_const(const cv::Ptr<cv::rgbd::FastICPOdometry>* instance) {
			return instance->get();
	}
	
	cv::rgbd::FastICPOdometry* cv_PtrLcv_rgbd_FastICPOdometryG_getInnerPtrMut(cv::Ptr<cv::rgbd::FastICPOdometry>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_rgbd_FastICPOdometryG_delete(cv::Ptr<cv::rgbd::FastICPOdometry>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_rgbd_FastICPOdometryG_to_PtrOfAlgorithm(cv::Ptr<cv::rgbd::FastICPOdometry>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::rgbd::Odometry>* cv_PtrLcv_rgbd_FastICPOdometryG_to_PtrOfOdometry(cv::Ptr<cv::rgbd::FastICPOdometry>* instance) {
			return new cv::Ptr<cv::rgbd::Odometry>(instance->dynamicCast<cv::rgbd::Odometry>());
	}
	
	cv::Ptr<cv::rgbd::FastICPOdometry>* cv_PtrLcv_rgbd_FastICPOdometryG_new_const_FastICPOdometry(cv::rgbd::FastICPOdometry* val) {
			return new cv::Ptr<cv::rgbd::FastICPOdometry>(val);
	}
	
}

extern "C" {
	const cv::rgbd::ICPOdometry* cv_PtrLcv_rgbd_ICPOdometryG_getInnerPtr_const(const cv::Ptr<cv::rgbd::ICPOdometry>* instance) {
			return instance->get();
	}
	
	cv::rgbd::ICPOdometry* cv_PtrLcv_rgbd_ICPOdometryG_getInnerPtrMut(cv::Ptr<cv::rgbd::ICPOdometry>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_rgbd_ICPOdometryG_delete(cv::Ptr<cv::rgbd::ICPOdometry>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_rgbd_ICPOdometryG_to_PtrOfAlgorithm(cv::Ptr<cv::rgbd::ICPOdometry>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::rgbd::Odometry>* cv_PtrLcv_rgbd_ICPOdometryG_to_PtrOfOdometry(cv::Ptr<cv::rgbd::ICPOdometry>* instance) {
			return new cv::Ptr<cv::rgbd::Odometry>(instance->dynamicCast<cv::rgbd::Odometry>());
	}
	
	cv::Ptr<cv::rgbd::ICPOdometry>* cv_PtrLcv_rgbd_ICPOdometryG_new_const_ICPOdometry(cv::rgbd::ICPOdometry* val) {
			return new cv::Ptr<cv::rgbd::ICPOdometry>(val);
	}
	
}

extern "C" {
	const cv::kinfu::detail::PoseGraph* cv_PtrLcv_kinfu_detail_PoseGraphG_getInnerPtr_const(const cv::Ptr<cv::kinfu::detail::PoseGraph>* instance) {
			return instance->get();
	}
	
	cv::kinfu::detail::PoseGraph* cv_PtrLcv_kinfu_detail_PoseGraphG_getInnerPtrMut(cv::Ptr<cv::kinfu::detail::PoseGraph>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_kinfu_detail_PoseGraphG_delete(cv::Ptr<cv::kinfu::detail::PoseGraph>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::kinfu::KinFu* cv_PtrLcv_kinfu_KinFuG_getInnerPtr_const(const cv::Ptr<cv::kinfu::KinFu>* instance) {
			return instance->get();
	}
	
	cv::kinfu::KinFu* cv_PtrLcv_kinfu_KinFuG_getInnerPtrMut(cv::Ptr<cv::kinfu::KinFu>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_kinfu_KinFuG_delete(cv::Ptr<cv::kinfu::KinFu>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::kinfu::Params* cv_PtrLcv_kinfu_ParamsG_getInnerPtr_const(const cv::Ptr<cv::kinfu::Params>* instance) {
			return instance->get();
	}
	
	cv::kinfu::Params* cv_PtrLcv_kinfu_ParamsG_getInnerPtrMut(cv::Ptr<cv::kinfu::Params>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_kinfu_ParamsG_delete(cv::Ptr<cv::kinfu::Params>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::kinfu::Params>* cv_PtrLcv_kinfu_ParamsG_new_const_Params(cv::kinfu::Params* val) {
			return new cv::Ptr<cv::kinfu::Params>(val);
	}
	
}

extern "C" {
	const cv::kinfu::Volume* cv_PtrLcv_kinfu_VolumeG_getInnerPtr_const(const cv::Ptr<cv::kinfu::Volume>* instance) {
			return instance->get();
	}
	
	cv::kinfu::Volume* cv_PtrLcv_kinfu_VolumeG_getInnerPtrMut(cv::Ptr<cv::kinfu::Volume>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_kinfu_VolumeG_delete(cv::Ptr<cv::kinfu::Volume>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::kinfu::VolumeParams* cv_PtrLcv_kinfu_VolumeParamsG_getInnerPtr_const(const cv::Ptr<cv::kinfu::VolumeParams>* instance) {
			return instance->get();
	}
	
	cv::kinfu::VolumeParams* cv_PtrLcv_kinfu_VolumeParamsG_getInnerPtrMut(cv::Ptr<cv::kinfu::VolumeParams>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_kinfu_VolumeParamsG_delete(cv::Ptr<cv::kinfu::VolumeParams>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::kinfu::VolumeParams>* cv_PtrLcv_kinfu_VolumeParamsG_new_const_VolumeParams(cv::kinfu::VolumeParams* val) {
			return new cv::Ptr<cv::kinfu::VolumeParams>(val);
	}
	
}

extern "C" {
	const cv::large_kinfu::LargeKinfu* cv_PtrLcv_large_kinfu_LargeKinfuG_getInnerPtr_const(const cv::Ptr<cv::large_kinfu::LargeKinfu>* instance) {
			return instance->get();
	}
	
	cv::large_kinfu::LargeKinfu* cv_PtrLcv_large_kinfu_LargeKinfuG_getInnerPtrMut(cv::Ptr<cv::large_kinfu::LargeKinfu>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_large_kinfu_LargeKinfuG_delete(cv::Ptr<cv::large_kinfu::LargeKinfu>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::linemod::ColorGradient* cv_PtrLcv_linemod_ColorGradientG_getInnerPtr_const(const cv::Ptr<cv::linemod::ColorGradient>* instance) {
			return instance->get();
	}
	
	cv::linemod::ColorGradient* cv_PtrLcv_linemod_ColorGradientG_getInnerPtrMut(cv::Ptr<cv::linemod::ColorGradient>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_linemod_ColorGradientG_delete(cv::Ptr<cv::linemod::ColorGradient>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::linemod::Modality>* cv_PtrLcv_linemod_ColorGradientG_to_PtrOfLineMod_Modality(cv::Ptr<cv::linemod::ColorGradient>* instance) {
			return new cv::Ptr<cv::linemod::Modality>(instance->dynamicCast<cv::linemod::Modality>());
	}
	
	cv::Ptr<cv::linemod::ColorGradient>* cv_PtrLcv_linemod_ColorGradientG_new_const_ColorGradient(cv::linemod::ColorGradient* val) {
			return new cv::Ptr<cv::linemod::ColorGradient>(val);
	}
	
}

extern "C" {
	const cv::linemod::DepthNormal* cv_PtrLcv_linemod_DepthNormalG_getInnerPtr_const(const cv::Ptr<cv::linemod::DepthNormal>* instance) {
			return instance->get();
	}
	
	cv::linemod::DepthNormal* cv_PtrLcv_linemod_DepthNormalG_getInnerPtrMut(cv::Ptr<cv::linemod::DepthNormal>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_linemod_DepthNormalG_delete(cv::Ptr<cv::linemod::DepthNormal>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::linemod::Modality>* cv_PtrLcv_linemod_DepthNormalG_to_PtrOfLineMod_Modality(cv::Ptr<cv::linemod::DepthNormal>* instance) {
			return new cv::Ptr<cv::linemod::Modality>(instance->dynamicCast<cv::linemod::Modality>());
	}
	
	cv::Ptr<cv::linemod::DepthNormal>* cv_PtrLcv_linemod_DepthNormalG_new_const_DepthNormal(cv::linemod::DepthNormal* val) {
			return new cv::Ptr<cv::linemod::DepthNormal>(val);
	}
	
}

extern "C" {
	const cv::linemod::Detector* cv_PtrLcv_linemod_DetectorG_getInnerPtr_const(const cv::Ptr<cv::linemod::Detector>* instance) {
			return instance->get();
	}
	
	cv::linemod::Detector* cv_PtrLcv_linemod_DetectorG_getInnerPtrMut(cv::Ptr<cv::linemod::Detector>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_linemod_DetectorG_delete(cv::Ptr<cv::linemod::Detector>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::linemod::Detector>* cv_PtrLcv_linemod_DetectorG_new_const_Detector(cv::linemod::Detector* val) {
			return new cv::Ptr<cv::linemod::Detector>(val);
	}
	
}

extern "C" {
	const cv::linemod::Modality* cv_PtrLcv_linemod_ModalityG_getInnerPtr_const(const cv::Ptr<cv::linemod::Modality>* instance) {
			return instance->get();
	}
	
	cv::linemod::Modality* cv_PtrLcv_linemod_ModalityG_getInnerPtrMut(cv::Ptr<cv::linemod::Modality>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_linemod_ModalityG_delete(cv::Ptr<cv::linemod::Modality>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::linemod::QuantizedPyramid* cv_PtrLcv_linemod_QuantizedPyramidG_getInnerPtr_const(const cv::Ptr<cv::linemod::QuantizedPyramid>* instance) {
			return instance->get();
	}
	
	cv::linemod::QuantizedPyramid* cv_PtrLcv_linemod_QuantizedPyramidG_getInnerPtrMut(cv::Ptr<cv::linemod::QuantizedPyramid>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_linemod_QuantizedPyramidG_delete(cv::Ptr<cv::linemod::QuantizedPyramid>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::rgbd::Odometry* cv_PtrLcv_rgbd_OdometryG_getInnerPtr_const(const cv::Ptr<cv::rgbd::Odometry>* instance) {
			return instance->get();
	}
	
	cv::rgbd::Odometry* cv_PtrLcv_rgbd_OdometryG_getInnerPtrMut(cv::Ptr<cv::rgbd::Odometry>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_rgbd_OdometryG_delete(cv::Ptr<cv::rgbd::Odometry>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_rgbd_OdometryG_to_PtrOfAlgorithm(cv::Ptr<cv::rgbd::Odometry>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::rgbd::OdometryFrame* cv_PtrLcv_rgbd_OdometryFrameG_getInnerPtr_const(const cv::Ptr<cv::rgbd::OdometryFrame>* instance) {
			return instance->get();
	}
	
	cv::rgbd::OdometryFrame* cv_PtrLcv_rgbd_OdometryFrameG_getInnerPtrMut(cv::Ptr<cv::rgbd::OdometryFrame>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_rgbd_OdometryFrameG_delete(cv::Ptr<cv::rgbd::OdometryFrame>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::rgbd::RgbdFrame>* cv_PtrLcv_rgbd_OdometryFrameG_to_PtrOfRgbdFrame(cv::Ptr<cv::rgbd::OdometryFrame>* instance) {
			return new cv::Ptr<cv::rgbd::RgbdFrame>(instance->dynamicCast<cv::rgbd::RgbdFrame>());
	}
	
	cv::Ptr<cv::rgbd::OdometryFrame>* cv_PtrLcv_rgbd_OdometryFrameG_new_const_OdometryFrame(cv::rgbd::OdometryFrame* val) {
			return new cv::Ptr<cv::rgbd::OdometryFrame>(val);
	}
	
}

extern "C" {
	const cv::large_kinfu::Params* cv_PtrLcv_large_kinfu_ParamsG_getInnerPtr_const(const cv::Ptr<cv::large_kinfu::Params>* instance) {
			return instance->get();
	}
	
	cv::large_kinfu::Params* cv_PtrLcv_large_kinfu_ParamsG_getInnerPtrMut(cv::Ptr<cv::large_kinfu::Params>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_large_kinfu_ParamsG_delete(cv::Ptr<cv::large_kinfu::Params>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::large_kinfu::Params>* cv_PtrLcv_large_kinfu_ParamsG_new_const_Params(cv::large_kinfu::Params* val) {
			return new cv::Ptr<cv::large_kinfu::Params>(val);
	}
	
}

extern "C" {
	const cv::rgbd::RgbdFrame* cv_PtrLcv_rgbd_RgbdFrameG_getInnerPtr_const(const cv::Ptr<cv::rgbd::RgbdFrame>* instance) {
			return instance->get();
	}
	
	cv::rgbd::RgbdFrame* cv_PtrLcv_rgbd_RgbdFrameG_getInnerPtrMut(cv::Ptr<cv::rgbd::RgbdFrame>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_rgbd_RgbdFrameG_delete(cv::Ptr<cv::rgbd::RgbdFrame>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::rgbd::RgbdFrame>* cv_PtrLcv_rgbd_RgbdFrameG_new_const_RgbdFrame(cv::rgbd::RgbdFrame* val) {
			return new cv::Ptr<cv::rgbd::RgbdFrame>(val);
	}
	
}

extern "C" {
	const cv::rgbd::RgbdICPOdometry* cv_PtrLcv_rgbd_RgbdICPOdometryG_getInnerPtr_const(const cv::Ptr<cv::rgbd::RgbdICPOdometry>* instance) {
			return instance->get();
	}
	
	cv::rgbd::RgbdICPOdometry* cv_PtrLcv_rgbd_RgbdICPOdometryG_getInnerPtrMut(cv::Ptr<cv::rgbd::RgbdICPOdometry>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_rgbd_RgbdICPOdometryG_delete(cv::Ptr<cv::rgbd::RgbdICPOdometry>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_rgbd_RgbdICPOdometryG_to_PtrOfAlgorithm(cv::Ptr<cv::rgbd::RgbdICPOdometry>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::rgbd::Odometry>* cv_PtrLcv_rgbd_RgbdICPOdometryG_to_PtrOfOdometry(cv::Ptr<cv::rgbd::RgbdICPOdometry>* instance) {
			return new cv::Ptr<cv::rgbd::Odometry>(instance->dynamicCast<cv::rgbd::Odometry>());
	}
	
	cv::Ptr<cv::rgbd::RgbdICPOdometry>* cv_PtrLcv_rgbd_RgbdICPOdometryG_new_const_RgbdICPOdometry(cv::rgbd::RgbdICPOdometry* val) {
			return new cv::Ptr<cv::rgbd::RgbdICPOdometry>(val);
	}
	
}

extern "C" {
	const cv::rgbd::RgbdNormals* cv_PtrLcv_rgbd_RgbdNormalsG_getInnerPtr_const(const cv::Ptr<cv::rgbd::RgbdNormals>* instance) {
			return instance->get();
	}
	
	cv::rgbd::RgbdNormals* cv_PtrLcv_rgbd_RgbdNormalsG_getInnerPtrMut(cv::Ptr<cv::rgbd::RgbdNormals>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_rgbd_RgbdNormalsG_delete(cv::Ptr<cv::rgbd::RgbdNormals>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_rgbd_RgbdNormalsG_to_PtrOfAlgorithm(cv::Ptr<cv::rgbd::RgbdNormals>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::rgbd::RgbdNormals>* cv_PtrLcv_rgbd_RgbdNormalsG_new_const_RgbdNormals(cv::rgbd::RgbdNormals* val) {
			return new cv::Ptr<cv::rgbd::RgbdNormals>(val);
	}
	
}

extern "C" {
	const cv::rgbd::RgbdOdometry* cv_PtrLcv_rgbd_RgbdOdometryG_getInnerPtr_const(const cv::Ptr<cv::rgbd::RgbdOdometry>* instance) {
			return instance->get();
	}
	
	cv::rgbd::RgbdOdometry* cv_PtrLcv_rgbd_RgbdOdometryG_getInnerPtrMut(cv::Ptr<cv::rgbd::RgbdOdometry>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_rgbd_RgbdOdometryG_delete(cv::Ptr<cv::rgbd::RgbdOdometry>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_rgbd_RgbdOdometryG_to_PtrOfAlgorithm(cv::Ptr<cv::rgbd::RgbdOdometry>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::rgbd::Odometry>* cv_PtrLcv_rgbd_RgbdOdometryG_to_PtrOfOdometry(cv::Ptr<cv::rgbd::RgbdOdometry>* instance) {
			return new cv::Ptr<cv::rgbd::Odometry>(instance->dynamicCast<cv::rgbd::Odometry>());
	}
	
	cv::Ptr<cv::rgbd::RgbdOdometry>* cv_PtrLcv_rgbd_RgbdOdometryG_new_const_RgbdOdometry(cv::rgbd::RgbdOdometry* val) {
			return new cv::Ptr<cv::rgbd::RgbdOdometry>(val);
	}
	
}

extern "C" {
	const cv::rgbd::RgbdPlane* cv_PtrLcv_rgbd_RgbdPlaneG_getInnerPtr_const(const cv::Ptr<cv::rgbd::RgbdPlane>* instance) {
			return instance->get();
	}
	
	cv::rgbd::RgbdPlane* cv_PtrLcv_rgbd_RgbdPlaneG_getInnerPtrMut(cv::Ptr<cv::rgbd::RgbdPlane>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_rgbd_RgbdPlaneG_delete(cv::Ptr<cv::rgbd::RgbdPlane>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_rgbd_RgbdPlaneG_to_PtrOfAlgorithm(cv::Ptr<cv::rgbd::RgbdPlane>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::rgbd::RgbdPlane>* cv_PtrLcv_rgbd_RgbdPlaneG_new_const_RgbdPlane(cv::rgbd::RgbdPlane* val) {
			return new cv::Ptr<cv::rgbd::RgbdPlane>(val);
	}
	
}

extern "C" {
	std::vector<cv::linemod::Feature>* std_vectorLcv_linemod_FeatureG_new_const() {
			std::vector<cv::linemod::Feature>* ret = new std::vector<cv::linemod::Feature>();
			return ret;
	}
	
	void std_vectorLcv_linemod_FeatureG_delete(std::vector<cv::linemod::Feature>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_linemod_FeatureG_len_const(const std::vector<cv::linemod::Feature>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_linemod_FeatureG_isEmpty_const(const std::vector<cv::linemod::Feature>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_linemod_FeatureG_capacity_const(const std::vector<cv::linemod::Feature>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_linemod_FeatureG_shrinkToFit(std::vector<cv::linemod::Feature>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_linemod_FeatureG_reserve_size_t(std::vector<cv::linemod::Feature>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_linemod_FeatureG_remove_size_t(std::vector<cv::linemod::Feature>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_linemod_FeatureG_swap_size_t_size_t(std::vector<cv::linemod::Feature>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_linemod_FeatureG_clear(std::vector<cv::linemod::Feature>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_linemod_FeatureG_push_const_Feature(std::vector<cv::linemod::Feature>* instance, const cv::linemod::Feature* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_linemod_FeatureG_insert_size_t_const_Feature(std::vector<cv::linemod::Feature>* instance, size_t index, const cv::linemod::Feature* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_linemod_FeatureG_get_const_size_t(const std::vector<cv::linemod::Feature>* instance, size_t index, cv::linemod::Feature* ocvrs_return) {
			cv::linemod::Feature ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLcv_linemod_FeatureG_set_size_t_const_Feature(std::vector<cv::linemod::Feature>* instance, size_t index, const cv::linemod::Feature* val) {
			(*instance)[index] = *val;
	}
	
	std::vector<cv::linemod::Feature>* std_vectorLcv_linemod_FeatureG_clone_const(const std::vector<cv::linemod::Feature>* instance) {
			std::vector<cv::linemod::Feature> ret = std::vector<cv::linemod::Feature>(*instance);
			return new std::vector<cv::linemod::Feature>(ret);
	}
	
	const cv::linemod::Feature* std_vectorLcv_linemod_FeatureG_data_const(const std::vector<cv::linemod::Feature>* instance) {
			const cv::linemod::Feature* ret = instance->data();
			return ret;
	}
	
	cv::linemod::Feature* std_vectorLcv_linemod_FeatureG_dataMut(std::vector<cv::linemod::Feature>* instance) {
			cv::linemod::Feature* ret = instance->data();
			return ret;
	}
	
	std::vector<cv::linemod::Feature>* cv_fromSlice_const_const_FeatureX_size_t(const cv::linemod::Feature* data, size_t len) {
			return new std::vector<cv::linemod::Feature>(data, data + len);
	}
	
}


extern "C" {
	std::vector<cv::linemod::Match>* std_vectorLcv_linemod_MatchG_new_const() {
			std::vector<cv::linemod::Match>* ret = new std::vector<cv::linemod::Match>();
			return ret;
	}
	
	void std_vectorLcv_linemod_MatchG_delete(std::vector<cv::linemod::Match>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_linemod_MatchG_len_const(const std::vector<cv::linemod::Match>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_linemod_MatchG_isEmpty_const(const std::vector<cv::linemod::Match>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_linemod_MatchG_capacity_const(const std::vector<cv::linemod::Match>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_linemod_MatchG_shrinkToFit(std::vector<cv::linemod::Match>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_linemod_MatchG_reserve_size_t(std::vector<cv::linemod::Match>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_linemod_MatchG_remove_size_t(std::vector<cv::linemod::Match>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_linemod_MatchG_swap_size_t_size_t(std::vector<cv::linemod::Match>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_linemod_MatchG_clear(std::vector<cv::linemod::Match>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_linemod_MatchG_push_const_Match(std::vector<cv::linemod::Match>* instance, const cv::linemod::Match* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_linemod_MatchG_insert_size_t_const_Match(std::vector<cv::linemod::Match>* instance, size_t index, const cv::linemod::Match* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_linemod_MatchG_get_const_size_t(const std::vector<cv::linemod::Match>* instance, size_t index, cv::linemod::Match** ocvrs_return) {
			cv::linemod::Match ret = (*instance)[index];
			*ocvrs_return = new cv::linemod::Match(ret);
	}
	
	void std_vectorLcv_linemod_MatchG_set_size_t_const_Match(std::vector<cv::linemod::Match>* instance, size_t index, const cv::linemod::Match* val) {
			(*instance)[index] = *val;
	}
	
}


extern "C" {
	std::vector<cv::linemod::Template>* std_vectorLcv_linemod_TemplateG_new_const() {
			std::vector<cv::linemod::Template>* ret = new std::vector<cv::linemod::Template>();
			return ret;
	}
	
	void std_vectorLcv_linemod_TemplateG_delete(std::vector<cv::linemod::Template>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_linemod_TemplateG_len_const(const std::vector<cv::linemod::Template>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_linemod_TemplateG_isEmpty_const(const std::vector<cv::linemod::Template>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_linemod_TemplateG_capacity_const(const std::vector<cv::linemod::Template>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_linemod_TemplateG_shrinkToFit(std::vector<cv::linemod::Template>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_linemod_TemplateG_reserve_size_t(std::vector<cv::linemod::Template>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_linemod_TemplateG_remove_size_t(std::vector<cv::linemod::Template>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_linemod_TemplateG_swap_size_t_size_t(std::vector<cv::linemod::Template>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_linemod_TemplateG_clear(std::vector<cv::linemod::Template>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_linemod_TemplateG_push_const_Template(std::vector<cv::linemod::Template>* instance, const cv::linemod::Template* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_linemod_TemplateG_insert_size_t_const_Template(std::vector<cv::linemod::Template>* instance, size_t index, const cv::linemod::Template* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_linemod_TemplateG_get_const_size_t(const std::vector<cv::linemod::Template>* instance, size_t index, cv::linemod::Template** ocvrs_return) {
			cv::linemod::Template ret = (*instance)[index];
			*ocvrs_return = new cv::linemod::Template(ret);
	}
	
	void std_vectorLcv_linemod_TemplateG_set_size_t_const_Template(std::vector<cv::linemod::Template>* instance, size_t index, const cv::linemod::Template* val) {
			(*instance)[index] = *val;
	}
	
}


extern "C" {
	std::vector<cv::Ptr<cv::linemod::Modality>>* std_vectorLcv_PtrLcv_linemod_ModalityGG_new_const() {
			std::vector<cv::Ptr<cv::linemod::Modality>>* ret = new std::vector<cv::Ptr<cv::linemod::Modality>>();
			return ret;
	}
	
	void std_vectorLcv_PtrLcv_linemod_ModalityGG_delete(std::vector<cv::Ptr<cv::linemod::Modality>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_PtrLcv_linemod_ModalityGG_len_const(const std::vector<cv::Ptr<cv::linemod::Modality>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_PtrLcv_linemod_ModalityGG_isEmpty_const(const std::vector<cv::Ptr<cv::linemod::Modality>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_PtrLcv_linemod_ModalityGG_capacity_const(const std::vector<cv::Ptr<cv::linemod::Modality>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_PtrLcv_linemod_ModalityGG_shrinkToFit(std::vector<cv::Ptr<cv::linemod::Modality>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_PtrLcv_linemod_ModalityGG_reserve_size_t(std::vector<cv::Ptr<cv::linemod::Modality>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_PtrLcv_linemod_ModalityGG_remove_size_t(std::vector<cv::Ptr<cv::linemod::Modality>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_PtrLcv_linemod_ModalityGG_swap_size_t_size_t(std::vector<cv::Ptr<cv::linemod::Modality>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_PtrLcv_linemod_ModalityGG_clear(std::vector<cv::Ptr<cv::linemod::Modality>>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_PtrLcv_linemod_ModalityGG_push_const_PtrLModalityG(std::vector<cv::Ptr<cv::linemod::Modality>>* instance, const cv::Ptr<cv::linemod::Modality>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_PtrLcv_linemod_ModalityGG_insert_size_t_const_PtrLModalityG(std::vector<cv::Ptr<cv::linemod::Modality>>* instance, size_t index, const cv::Ptr<cv::linemod::Modality>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_PtrLcv_linemod_ModalityGG_get_const_size_t(const std::vector<cv::Ptr<cv::linemod::Modality>>* instance, size_t index, cv::Ptr<cv::linemod::Modality>** ocvrs_return) {
			cv::Ptr<cv::linemod::Modality> ret = (*instance)[index];
			*ocvrs_return = new cv::Ptr<cv::linemod::Modality>(ret);
	}
	
	void std_vectorLcv_PtrLcv_linemod_ModalityGG_set_size_t_const_PtrLModalityG(std::vector<cv::Ptr<cv::linemod::Modality>>* instance, size_t index, const cv::Ptr<cv::linemod::Modality>* val) {
			(*instance)[index] = *val;
	}
	
}



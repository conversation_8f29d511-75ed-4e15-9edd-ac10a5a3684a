extern "C" {
	const cv::Algorithm* cv_PtrLcv_AlgorithmG_getInnerPtr_const(const cv::Ptr<cv::Algorithm>* instance) {
			return instance->get();
	}
	
	cv::Algorithm* cv_PtrLcv_AlgorithmG_getInnerPtrMut(cv::Ptr<cv::Algorithm>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_AlgorithmG_delete(cv::Ptr<cv::Algorithm>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_AlgorithmG_new_const_Algorithm(cv::Algorithm* val) {
			return new cv::Ptr<cv::Algorithm>(val);
	}
	
}

extern "C" {
	const cv::ConjGradSolver* cv_PtrLcv_ConjGradSolverG_getInnerPtr_const(const cv::Ptr<cv::ConjGradSolver>* instance) {
			return instance->get();
	}
	
	cv::ConjGradSolver* cv_PtrLcv_ConjGradSolverG_getInnerPtrMut(cv::Ptr<cv::ConjGradSolver>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ConjGradSolverG_delete(cv::Ptr<cv::ConjGradSolver>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ConjGradSolverG_to_PtrOfAlgorithm(cv::Ptr<cv::ConjGradSolver>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::MinProblemSolver>* cv_PtrLcv_ConjGradSolverG_to_PtrOfMinProblemSolver(cv::Ptr<cv::ConjGradSolver>* instance) {
			return new cv::Ptr<cv::MinProblemSolver>(instance->dynamicCast<cv::MinProblemSolver>());
	}
	
}

extern "C" {
	const cv::DownhillSolver* cv_PtrLcv_DownhillSolverG_getInnerPtr_const(const cv::Ptr<cv::DownhillSolver>* instance) {
			return instance->get();
	}
	
	cv::DownhillSolver* cv_PtrLcv_DownhillSolverG_getInnerPtrMut(cv::Ptr<cv::DownhillSolver>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_DownhillSolverG_delete(cv::Ptr<cv::DownhillSolver>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_DownhillSolverG_to_PtrOfAlgorithm(cv::Ptr<cv::DownhillSolver>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::MinProblemSolver>* cv_PtrLcv_DownhillSolverG_to_PtrOfMinProblemSolver(cv::Ptr<cv::DownhillSolver>* instance) {
			return new cv::Ptr<cv::MinProblemSolver>(instance->dynamicCast<cv::MinProblemSolver>());
	}
	
}

extern "C" {
	const cv::FileStorage* cv_PtrLcv_FileStorageG_getInnerPtr_const(const cv::Ptr<cv::FileStorage>* instance) {
			return instance->get();
	}
	
	cv::FileStorage* cv_PtrLcv_FileStorageG_getInnerPtrMut(cv::Ptr<cv::FileStorage>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_FileStorageG_delete(cv::Ptr<cv::FileStorage>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::FileStorage>* cv_PtrLcv_FileStorageG_new_const_FileStorage(cv::FileStorage* val) {
			return new cv::Ptr<cv::FileStorage>(val);
	}
	
}

extern "C" {
	const cv::Formatted* cv_PtrLcv_FormattedG_getInnerPtr_const(const cv::Ptr<cv::Formatted>* instance) {
			return instance->get();
	}
	
	cv::Formatted* cv_PtrLcv_FormattedG_getInnerPtrMut(cv::Ptr<cv::Formatted>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_FormattedG_delete(cv::Ptr<cv::Formatted>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::Formatter* cv_PtrLcv_FormatterG_getInnerPtr_const(const cv::Ptr<cv::Formatter>* instance) {
			return instance->get();
	}
	
	cv::Formatter* cv_PtrLcv_FormatterG_getInnerPtrMut(cv::Ptr<cv::Formatter>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_FormatterG_delete(cv::Ptr<cv::Formatter>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::cuda::GpuMat::Allocator* cv_PtrLcv_cuda_GpuMat_AllocatorG_getInnerPtr_const(const cv::Ptr<cv::cuda::GpuMat::Allocator>* instance) {
			return instance->get();
	}
	
	cv::cuda::GpuMat::Allocator* cv_PtrLcv_cuda_GpuMat_AllocatorG_getInnerPtrMut(cv::Ptr<cv::cuda::GpuMat::Allocator>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_cuda_GpuMat_AllocatorG_delete(cv::Ptr<cv::cuda::GpuMat::Allocator>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::MinProblemSolver* cv_PtrLcv_MinProblemSolverG_getInnerPtr_const(const cv::Ptr<cv::MinProblemSolver>* instance) {
			return instance->get();
	}
	
	cv::MinProblemSolver* cv_PtrLcv_MinProblemSolverG_getInnerPtrMut(cv::Ptr<cv::MinProblemSolver>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_MinProblemSolverG_delete(cv::Ptr<cv::MinProblemSolver>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_MinProblemSolverG_to_PtrOfAlgorithm(cv::Ptr<cv::MinProblemSolver>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::MinProblemSolver::Function* cv_PtrLcv_MinProblemSolver_FunctionG_getInnerPtr_const(const cv::Ptr<cv::MinProblemSolver::Function>* instance) {
			return instance->get();
	}
	
	cv::MinProblemSolver::Function* cv_PtrLcv_MinProblemSolver_FunctionG_getInnerPtrMut(cv::Ptr<cv::MinProblemSolver::Function>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_MinProblemSolver_FunctionG_delete(cv::Ptr<cv::MinProblemSolver::Function>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const float* cv_PtrLfloatG_getInnerPtr_const(const cv::Ptr<float>* instance) {
			return instance->get();
	}
	
	float* cv_PtrLfloatG_getInnerPtrMut(cv::Ptr<float>* instance) {
			return instance->get();
	}
	
	void cv_PtrLfloatG_delete(cv::Ptr<float>* instance) {
			delete instance;
	}
	
	cv::Ptr<float>* cv_PtrLfloatG_new_const_float(float val) {
			return new cv::Ptr<float>(new float(val));
	}
	
}

extern "C" {
	std::pair<cv::Point2i, cv::Point2i>* std_pairLcv_Point2i__cv_Point2iG_new_const_Point2i_Point2i(cv::Point2i* arg, cv::Point2i* arg_1) {
			std::pair<cv::Point2i, cv::Point2i>* ret = new std::pair<cv::Point2i, cv::Point2i>(*arg, *arg_1);
			return ret;
	}
	
	void std_pairLcv_Point2i__cv_Point2iG_get_0_const(const std::pair<cv::Point2i, cv::Point2i>* instance, cv::Point2i* ocvrs_return) {
			cv::Point2i ret = std::get<0>(*instance);
			*ocvrs_return = ret;
	}
	
	void std_pairLcv_Point2i__cv_Point2iG_get_1_const(const std::pair<cv::Point2i, cv::Point2i>* instance, cv::Point2i* ocvrs_return) {
			cv::Point2i ret = std::get<1>(*instance);
			*ocvrs_return = ret;
	}
	
	void std_pairLcv_Point2i__cv_Point2iG_delete(std::pair<cv::Point2i, cv::Point2i>* instance) {
			delete instance;
	}
	
}

extern "C" {
	std::pair<cv::Rect, int>* std_pairLcv_Rect__intG_new_const_Rect_int(cv::Rect* arg, int arg_1) {
			std::pair<cv::Rect, int>* ret = new std::pair<cv::Rect, int>(*arg, arg_1);
			return ret;
	}
	
	void std_pairLcv_Rect__intG_get_0_const(const std::pair<cv::Rect, int>* instance, cv::Rect* ocvrs_return) {
			cv::Rect ret = std::get<0>(*instance);
			*ocvrs_return = ret;
	}
	
	void std_pairLcv_Rect__intG_get_1_const(const std::pair<cv::Rect, int>* instance, int* ocvrs_return) {
			int ret = std::get<1>(*instance);
			*ocvrs_return = ret;
	}
	
	void std_pairLcv_Rect__intG_delete(std::pair<cv::Rect, int>* instance) {
			delete instance;
	}
	
}

extern "C" {
	std::pair<cv::UMat, unsigned char>* std_pairLcv_UMat__unsigned_charG_new_const_UMat_unsigned_char(cv::UMat* arg, unsigned char arg_1) {
			std::pair<cv::UMat, unsigned char>* ret = new std::pair<cv::UMat, unsigned char>(*arg, arg_1);
			return ret;
	}
	
	void std_pairLcv_UMat__unsigned_charG_get_0_const(const std::pair<cv::UMat, unsigned char>* instance, cv::UMat** ocvrs_return) {
			cv::UMat ret = std::get<0>(*instance);
			*ocvrs_return = new cv::UMat(ret);
	}
	
	void std_pairLcv_UMat__unsigned_charG_get_1_const(const std::pair<cv::UMat, unsigned char>* instance, unsigned char* ocvrs_return) {
			unsigned char ret = std::get<1>(*instance);
			*ocvrs_return = ret;
	}
	
	void std_pairLcv_UMat__unsigned_charG_delete(std::pair<cv::UMat, unsigned char>* instance) {
			delete instance;
	}
	
}

extern "C" {
	std::pair<int, float>* std_pairLint__floatG_new_const_int_float(int arg, float arg_1) {
			std::pair<int, float>* ret = new std::pair<int, float>(arg, arg_1);
			return ret;
	}
	
	void std_pairLint__floatG_get_0_const(const std::pair<int, float>* instance, int* ocvrs_return) {
			int ret = std::get<0>(*instance);
			*ocvrs_return = ret;
	}
	
	void std_pairLint__floatG_get_1_const(const std::pair<int, float>* instance, float* ocvrs_return) {
			float ret = std::get<1>(*instance);
			*ocvrs_return = ret;
	}
	
	void std_pairLint__floatG_delete(std::pair<int, float>* instance) {
			delete instance;
	}
	
}

extern "C" {
	std::pair<int, double>* std_pairLint__doubleG_new_const_int_double(int arg, double arg_1) {
			std::pair<int, double>* ret = new std::pair<int, double>(arg, arg_1);
			return ret;
	}
	
	void std_pairLint__doubleG_get_0_const(const std::pair<int, double>* instance, int* ocvrs_return) {
			int ret = std::get<0>(*instance);
			*ocvrs_return = ret;
	}
	
	void std_pairLint__doubleG_get_1_const(const std::pair<int, double>* instance, double* ocvrs_return) {
			double ret = std::get<1>(*instance);
			*ocvrs_return = ret;
	}
	
	void std_pairLint__doubleG_delete(std::pair<int, double>* instance) {
			delete instance;
	}
	
}

extern "C" {
	std::vector<cv::DMatch>* std_vectorLcv_DMatchG_new_const() {
			std::vector<cv::DMatch>* ret = new std::vector<cv::DMatch>();
			return ret;
	}
	
	void std_vectorLcv_DMatchG_delete(std::vector<cv::DMatch>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_DMatchG_len_const(const std::vector<cv::DMatch>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_DMatchG_isEmpty_const(const std::vector<cv::DMatch>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_DMatchG_capacity_const(const std::vector<cv::DMatch>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_DMatchG_shrinkToFit(std::vector<cv::DMatch>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_DMatchG_reserve_size_t(std::vector<cv::DMatch>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_DMatchG_remove_size_t(std::vector<cv::DMatch>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_DMatchG_swap_size_t_size_t(std::vector<cv::DMatch>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_DMatchG_clear(std::vector<cv::DMatch>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_DMatchG_push_const_DMatch(std::vector<cv::DMatch>* instance, const cv::DMatch* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_DMatchG_insert_size_t_const_DMatch(std::vector<cv::DMatch>* instance, size_t index, const cv::DMatch* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_DMatchG_get_const_size_t(const std::vector<cv::DMatch>* instance, size_t index, cv::DMatch* ocvrs_return) {
			cv::DMatch ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLcv_DMatchG_set_size_t_const_DMatch(std::vector<cv::DMatch>* instance, size_t index, const cv::DMatch* val) {
			(*instance)[index] = *val;
	}
	
	std::vector<cv::DMatch>* std_vectorLcv_DMatchG_clone_const(const std::vector<cv::DMatch>* instance) {
			std::vector<cv::DMatch> ret = std::vector<cv::DMatch>(*instance);
			return new std::vector<cv::DMatch>(ret);
	}
	
	const cv::DMatch* std_vectorLcv_DMatchG_data_const(const std::vector<cv::DMatch>* instance) {
			const cv::DMatch* ret = instance->data();
			return ret;
	}
	
	cv::DMatch* std_vectorLcv_DMatchG_dataMut(std::vector<cv::DMatch>* instance) {
			cv::DMatch* ret = instance->data();
			return ret;
	}
	
	std::vector<cv::DMatch>* cv_fromSlice_const_const_DMatchX_size_t(const cv::DMatch* data, size_t len) {
			return new std::vector<cv::DMatch>(data, data + len);
	}
	
}


extern "C" {
	std::vector<cv::cuda::GpuMat>* std_vectorLcv_cuda_GpuMatG_new_const() {
			std::vector<cv::cuda::GpuMat>* ret = new std::vector<cv::cuda::GpuMat>();
			return ret;
	}
	
	void std_vectorLcv_cuda_GpuMatG_delete(std::vector<cv::cuda::GpuMat>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_cuda_GpuMatG_len_const(const std::vector<cv::cuda::GpuMat>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_cuda_GpuMatG_isEmpty_const(const std::vector<cv::cuda::GpuMat>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_cuda_GpuMatG_capacity_const(const std::vector<cv::cuda::GpuMat>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_cuda_GpuMatG_shrinkToFit(std::vector<cv::cuda::GpuMat>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_cuda_GpuMatG_reserve_size_t(std::vector<cv::cuda::GpuMat>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_cuda_GpuMatG_remove_size_t(std::vector<cv::cuda::GpuMat>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_cuda_GpuMatG_swap_size_t_size_t(std::vector<cv::cuda::GpuMat>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_cuda_GpuMatG_clear(std::vector<cv::cuda::GpuMat>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_cuda_GpuMatG_push_const_GpuMat(std::vector<cv::cuda::GpuMat>* instance, const cv::cuda::GpuMat* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_cuda_GpuMatG_insert_size_t_const_GpuMat(std::vector<cv::cuda::GpuMat>* instance, size_t index, const cv::cuda::GpuMat* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_cuda_GpuMatG_get_const_size_t(const std::vector<cv::cuda::GpuMat>* instance, size_t index, cv::cuda::GpuMat** ocvrs_return) {
			cv::cuda::GpuMat ret = (*instance)[index];
			*ocvrs_return = new cv::cuda::GpuMat(ret);
	}
	
	void std_vectorLcv_cuda_GpuMatG_set_size_t_const_GpuMat(std::vector<cv::cuda::GpuMat>* instance, size_t index, const cv::cuda::GpuMat* val) {
			(*instance)[index] = *val;
	}
	
}


extern "C" {
	std::vector<cv::KeyPoint>* std_vectorLcv_KeyPointG_new_const() {
			std::vector<cv::KeyPoint>* ret = new std::vector<cv::KeyPoint>();
			return ret;
	}
	
	void std_vectorLcv_KeyPointG_delete(std::vector<cv::KeyPoint>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_KeyPointG_len_const(const std::vector<cv::KeyPoint>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_KeyPointG_isEmpty_const(const std::vector<cv::KeyPoint>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_KeyPointG_capacity_const(const std::vector<cv::KeyPoint>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_KeyPointG_shrinkToFit(std::vector<cv::KeyPoint>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_KeyPointG_reserve_size_t(std::vector<cv::KeyPoint>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_KeyPointG_remove_size_t(std::vector<cv::KeyPoint>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_KeyPointG_swap_size_t_size_t(std::vector<cv::KeyPoint>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_KeyPointG_clear(std::vector<cv::KeyPoint>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_KeyPointG_push_const_KeyPoint(std::vector<cv::KeyPoint>* instance, const cv::KeyPoint* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_KeyPointG_insert_size_t_const_KeyPoint(std::vector<cv::KeyPoint>* instance, size_t index, const cv::KeyPoint* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_KeyPointG_get_const_size_t(const std::vector<cv::KeyPoint>* instance, size_t index, cv::KeyPoint** ocvrs_return) {
			cv::KeyPoint ret = (*instance)[index];
			*ocvrs_return = new cv::KeyPoint(ret);
	}
	
	void std_vectorLcv_KeyPointG_set_size_t_const_KeyPoint(std::vector<cv::KeyPoint>* instance, size_t index, const cv::KeyPoint* val) {
			(*instance)[index] = *val;
	}
	
}


extern "C" {
	std::vector<cv::Mat>* std_vectorLcv_MatG_new_const() {
			std::vector<cv::Mat>* ret = new std::vector<cv::Mat>();
			return ret;
	}
	
	void std_vectorLcv_MatG_delete(std::vector<cv::Mat>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_MatG_len_const(const std::vector<cv::Mat>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_MatG_isEmpty_const(const std::vector<cv::Mat>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_MatG_capacity_const(const std::vector<cv::Mat>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_MatG_shrinkToFit(std::vector<cv::Mat>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_MatG_reserve_size_t(std::vector<cv::Mat>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_MatG_remove_size_t(std::vector<cv::Mat>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_MatG_swap_size_t_size_t(std::vector<cv::Mat>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_MatG_clear(std::vector<cv::Mat>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_MatG_push_const_Mat(std::vector<cv::Mat>* instance, const cv::Mat* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_MatG_insert_size_t_const_Mat(std::vector<cv::Mat>* instance, size_t index, const cv::Mat* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_MatG_get_const_size_t(const std::vector<cv::Mat>* instance, size_t index, cv::Mat** ocvrs_return) {
			cv::Mat ret = (*instance)[index];
			*ocvrs_return = new cv::Mat(ret);
	}
	
	void std_vectorLcv_MatG_set_size_t_const_Mat(std::vector<cv::Mat>* instance, size_t index, const cv::Mat* val) {
			(*instance)[index] = *val;
	}
	
}


extern "C" {
	std::vector<cv::ocl::PlatformInfo>* std_vectorLcv_ocl_PlatformInfoG_new_const() {
			std::vector<cv::ocl::PlatformInfo>* ret = new std::vector<cv::ocl::PlatformInfo>();
			return ret;
	}
	
	void std_vectorLcv_ocl_PlatformInfoG_delete(std::vector<cv::ocl::PlatformInfo>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_ocl_PlatformInfoG_len_const(const std::vector<cv::ocl::PlatformInfo>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_ocl_PlatformInfoG_isEmpty_const(const std::vector<cv::ocl::PlatformInfo>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_ocl_PlatformInfoG_capacity_const(const std::vector<cv::ocl::PlatformInfo>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_ocl_PlatformInfoG_shrinkToFit(std::vector<cv::ocl::PlatformInfo>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_ocl_PlatformInfoG_reserve_size_t(std::vector<cv::ocl::PlatformInfo>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_ocl_PlatformInfoG_remove_size_t(std::vector<cv::ocl::PlatformInfo>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_ocl_PlatformInfoG_swap_size_t_size_t(std::vector<cv::ocl::PlatformInfo>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_ocl_PlatformInfoG_clear(std::vector<cv::ocl::PlatformInfo>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_ocl_PlatformInfoG_push_const_PlatformInfo(std::vector<cv::ocl::PlatformInfo>* instance, const cv::ocl::PlatformInfo* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_ocl_PlatformInfoG_insert_size_t_const_PlatformInfo(std::vector<cv::ocl::PlatformInfo>* instance, size_t index, const cv::ocl::PlatformInfo* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_ocl_PlatformInfoG_get_const_size_t(const std::vector<cv::ocl::PlatformInfo>* instance, size_t index, cv::ocl::PlatformInfo** ocvrs_return) {
			cv::ocl::PlatformInfo ret = (*instance)[index];
			*ocvrs_return = new cv::ocl::PlatformInfo(ret);
	}
	
	void std_vectorLcv_ocl_PlatformInfoG_set_size_t_const_PlatformInfo(std::vector<cv::ocl::PlatformInfo>* instance, size_t index, const cv::ocl::PlatformInfo* val) {
			(*instance)[index] = *val;
	}
	
}


extern "C" {
	std::vector<cv::Point>* std_vectorLcv_PointG_new_const() {
			std::vector<cv::Point>* ret = new std::vector<cv::Point>();
			return ret;
	}
	
	void std_vectorLcv_PointG_delete(std::vector<cv::Point>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_PointG_len_const(const std::vector<cv::Point>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_PointG_isEmpty_const(const std::vector<cv::Point>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_PointG_capacity_const(const std::vector<cv::Point>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_PointG_shrinkToFit(std::vector<cv::Point>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_PointG_reserve_size_t(std::vector<cv::Point>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_PointG_remove_size_t(std::vector<cv::Point>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_PointG_swap_size_t_size_t(std::vector<cv::Point>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_PointG_clear(std::vector<cv::Point>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_PointG_push_const_Point(std::vector<cv::Point>* instance, const cv::Point* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_PointG_insert_size_t_const_Point(std::vector<cv::Point>* instance, size_t index, const cv::Point* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_PointG_get_const_size_t(const std::vector<cv::Point>* instance, size_t index, cv::Point* ocvrs_return) {
			cv::Point ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLcv_PointG_set_size_t_const_Point(std::vector<cv::Point>* instance, size_t index, const cv::Point* val) {
			(*instance)[index] = *val;
	}
	
	std::vector<cv::Point>* std_vectorLcv_PointG_clone_const(const std::vector<cv::Point>* instance) {
			std::vector<cv::Point> ret = std::vector<cv::Point>(*instance);
			return new std::vector<cv::Point>(ret);
	}
	
	const cv::Point* std_vectorLcv_PointG_data_const(const std::vector<cv::Point>* instance) {
			const cv::Point* ret = instance->data();
			return ret;
	}
	
	cv::Point* std_vectorLcv_PointG_dataMut(std::vector<cv::Point>* instance) {
			cv::Point* ret = instance->data();
			return ret;
	}
	
	std::vector<cv::Point>* cv_fromSlice_const_const_PointX_size_t(const cv::Point* data, size_t len) {
			return new std::vector<cv::Point>(data, data + len);
	}
	
	void std_vectorLcv_PointG_inputArray_const(const std::vector<cv::Point>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_PointG_outputArray(std::vector<cv::Point>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_PointG_inputOutputArray(std::vector<cv::Point>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<cv::Point2d>* std_vectorLcv_Point2dG_new_const() {
			std::vector<cv::Point2d>* ret = new std::vector<cv::Point2d>();
			return ret;
	}
	
	void std_vectorLcv_Point2dG_delete(std::vector<cv::Point2d>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_Point2dG_len_const(const std::vector<cv::Point2d>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_Point2dG_isEmpty_const(const std::vector<cv::Point2d>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_Point2dG_capacity_const(const std::vector<cv::Point2d>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_Point2dG_shrinkToFit(std::vector<cv::Point2d>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_Point2dG_reserve_size_t(std::vector<cv::Point2d>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_Point2dG_remove_size_t(std::vector<cv::Point2d>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_Point2dG_swap_size_t_size_t(std::vector<cv::Point2d>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_Point2dG_clear(std::vector<cv::Point2d>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_Point2dG_push_const_Point2d(std::vector<cv::Point2d>* instance, const cv::Point2d* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_Point2dG_insert_size_t_const_Point2d(std::vector<cv::Point2d>* instance, size_t index, const cv::Point2d* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_Point2dG_get_const_size_t(const std::vector<cv::Point2d>* instance, size_t index, cv::Point2d* ocvrs_return) {
			cv::Point2d ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLcv_Point2dG_set_size_t_const_Point2d(std::vector<cv::Point2d>* instance, size_t index, const cv::Point2d* val) {
			(*instance)[index] = *val;
	}
	
	std::vector<cv::Point2d>* std_vectorLcv_Point2dG_clone_const(const std::vector<cv::Point2d>* instance) {
			std::vector<cv::Point2d> ret = std::vector<cv::Point2d>(*instance);
			return new std::vector<cv::Point2d>(ret);
	}
	
	const cv::Point2d* std_vectorLcv_Point2dG_data_const(const std::vector<cv::Point2d>* instance) {
			const cv::Point2d* ret = instance->data();
			return ret;
	}
	
	cv::Point2d* std_vectorLcv_Point2dG_dataMut(std::vector<cv::Point2d>* instance) {
			cv::Point2d* ret = instance->data();
			return ret;
	}
	
	std::vector<cv::Point2d>* cv_fromSlice_const_const_Point2dX_size_t(const cv::Point2d* data, size_t len) {
			return new std::vector<cv::Point2d>(data, data + len);
	}
	
	void std_vectorLcv_Point2dG_inputArray_const(const std::vector<cv::Point2d>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Point2dG_outputArray(std::vector<cv::Point2d>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Point2dG_inputOutputArray(std::vector<cv::Point2d>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<cv::Point2f>* std_vectorLcv_Point2fG_new_const() {
			std::vector<cv::Point2f>* ret = new std::vector<cv::Point2f>();
			return ret;
	}
	
	void std_vectorLcv_Point2fG_delete(std::vector<cv::Point2f>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_Point2fG_len_const(const std::vector<cv::Point2f>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_Point2fG_isEmpty_const(const std::vector<cv::Point2f>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_Point2fG_capacity_const(const std::vector<cv::Point2f>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_Point2fG_shrinkToFit(std::vector<cv::Point2f>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_Point2fG_reserve_size_t(std::vector<cv::Point2f>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_Point2fG_remove_size_t(std::vector<cv::Point2f>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_Point2fG_swap_size_t_size_t(std::vector<cv::Point2f>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_Point2fG_clear(std::vector<cv::Point2f>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_Point2fG_push_const_Point2f(std::vector<cv::Point2f>* instance, const cv::Point2f* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_Point2fG_insert_size_t_const_Point2f(std::vector<cv::Point2f>* instance, size_t index, const cv::Point2f* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_Point2fG_get_const_size_t(const std::vector<cv::Point2f>* instance, size_t index, cv::Point2f* ocvrs_return) {
			cv::Point2f ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLcv_Point2fG_set_size_t_const_Point2f(std::vector<cv::Point2f>* instance, size_t index, const cv::Point2f* val) {
			(*instance)[index] = *val;
	}
	
	std::vector<cv::Point2f>* std_vectorLcv_Point2fG_clone_const(const std::vector<cv::Point2f>* instance) {
			std::vector<cv::Point2f> ret = std::vector<cv::Point2f>(*instance);
			return new std::vector<cv::Point2f>(ret);
	}
	
	const cv::Point2f* std_vectorLcv_Point2fG_data_const(const std::vector<cv::Point2f>* instance) {
			const cv::Point2f* ret = instance->data();
			return ret;
	}
	
	cv::Point2f* std_vectorLcv_Point2fG_dataMut(std::vector<cv::Point2f>* instance) {
			cv::Point2f* ret = instance->data();
			return ret;
	}
	
	std::vector<cv::Point2f>* cv_fromSlice_const_const_Point2fX_size_t(const cv::Point2f* data, size_t len) {
			return new std::vector<cv::Point2f>(data, data + len);
	}
	
	void std_vectorLcv_Point2fG_inputArray_const(const std::vector<cv::Point2f>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Point2fG_outputArray(std::vector<cv::Point2f>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Point2fG_inputOutputArray(std::vector<cv::Point2f>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<cv::Point3d>* std_vectorLcv_Point3dG_new_const() {
			std::vector<cv::Point3d>* ret = new std::vector<cv::Point3d>();
			return ret;
	}
	
	void std_vectorLcv_Point3dG_delete(std::vector<cv::Point3d>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_Point3dG_len_const(const std::vector<cv::Point3d>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_Point3dG_isEmpty_const(const std::vector<cv::Point3d>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_Point3dG_capacity_const(const std::vector<cv::Point3d>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_Point3dG_shrinkToFit(std::vector<cv::Point3d>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_Point3dG_reserve_size_t(std::vector<cv::Point3d>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_Point3dG_remove_size_t(std::vector<cv::Point3d>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_Point3dG_swap_size_t_size_t(std::vector<cv::Point3d>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_Point3dG_clear(std::vector<cv::Point3d>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_Point3dG_push_const_Point3d(std::vector<cv::Point3d>* instance, const cv::Point3d* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_Point3dG_insert_size_t_const_Point3d(std::vector<cv::Point3d>* instance, size_t index, const cv::Point3d* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_Point3dG_get_const_size_t(const std::vector<cv::Point3d>* instance, size_t index, cv::Point3d* ocvrs_return) {
			cv::Point3d ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLcv_Point3dG_set_size_t_const_Point3d(std::vector<cv::Point3d>* instance, size_t index, const cv::Point3d* val) {
			(*instance)[index] = *val;
	}
	
	std::vector<cv::Point3d>* std_vectorLcv_Point3dG_clone_const(const std::vector<cv::Point3d>* instance) {
			std::vector<cv::Point3d> ret = std::vector<cv::Point3d>(*instance);
			return new std::vector<cv::Point3d>(ret);
	}
	
	const cv::Point3d* std_vectorLcv_Point3dG_data_const(const std::vector<cv::Point3d>* instance) {
			const cv::Point3d* ret = instance->data();
			return ret;
	}
	
	cv::Point3d* std_vectorLcv_Point3dG_dataMut(std::vector<cv::Point3d>* instance) {
			cv::Point3d* ret = instance->data();
			return ret;
	}
	
	std::vector<cv::Point3d>* cv_fromSlice_const_const_Point3dX_size_t(const cv::Point3d* data, size_t len) {
			return new std::vector<cv::Point3d>(data, data + len);
	}
	
	void std_vectorLcv_Point3dG_inputArray_const(const std::vector<cv::Point3d>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Point3dG_outputArray(std::vector<cv::Point3d>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Point3dG_inputOutputArray(std::vector<cv::Point3d>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<cv::Point3f>* std_vectorLcv_Point3fG_new_const() {
			std::vector<cv::Point3f>* ret = new std::vector<cv::Point3f>();
			return ret;
	}
	
	void std_vectorLcv_Point3fG_delete(std::vector<cv::Point3f>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_Point3fG_len_const(const std::vector<cv::Point3f>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_Point3fG_isEmpty_const(const std::vector<cv::Point3f>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_Point3fG_capacity_const(const std::vector<cv::Point3f>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_Point3fG_shrinkToFit(std::vector<cv::Point3f>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_Point3fG_reserve_size_t(std::vector<cv::Point3f>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_Point3fG_remove_size_t(std::vector<cv::Point3f>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_Point3fG_swap_size_t_size_t(std::vector<cv::Point3f>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_Point3fG_clear(std::vector<cv::Point3f>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_Point3fG_push_const_Point3f(std::vector<cv::Point3f>* instance, const cv::Point3f* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_Point3fG_insert_size_t_const_Point3f(std::vector<cv::Point3f>* instance, size_t index, const cv::Point3f* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_Point3fG_get_const_size_t(const std::vector<cv::Point3f>* instance, size_t index, cv::Point3f* ocvrs_return) {
			cv::Point3f ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLcv_Point3fG_set_size_t_const_Point3f(std::vector<cv::Point3f>* instance, size_t index, const cv::Point3f* val) {
			(*instance)[index] = *val;
	}
	
	std::vector<cv::Point3f>* std_vectorLcv_Point3fG_clone_const(const std::vector<cv::Point3f>* instance) {
			std::vector<cv::Point3f> ret = std::vector<cv::Point3f>(*instance);
			return new std::vector<cv::Point3f>(ret);
	}
	
	const cv::Point3f* std_vectorLcv_Point3fG_data_const(const std::vector<cv::Point3f>* instance) {
			const cv::Point3f* ret = instance->data();
			return ret;
	}
	
	cv::Point3f* std_vectorLcv_Point3fG_dataMut(std::vector<cv::Point3f>* instance) {
			cv::Point3f* ret = instance->data();
			return ret;
	}
	
	std::vector<cv::Point3f>* cv_fromSlice_const_const_Point3fX_size_t(const cv::Point3f* data, size_t len) {
			return new std::vector<cv::Point3f>(data, data + len);
	}
	
	void std_vectorLcv_Point3fG_inputArray_const(const std::vector<cv::Point3f>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Point3fG_outputArray(std::vector<cv::Point3f>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Point3fG_inputOutputArray(std::vector<cv::Point3f>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<cv::Point3i>* std_vectorLcv_Point3iG_new_const() {
			std::vector<cv::Point3i>* ret = new std::vector<cv::Point3i>();
			return ret;
	}
	
	void std_vectorLcv_Point3iG_delete(std::vector<cv::Point3i>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_Point3iG_len_const(const std::vector<cv::Point3i>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_Point3iG_isEmpty_const(const std::vector<cv::Point3i>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_Point3iG_capacity_const(const std::vector<cv::Point3i>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_Point3iG_shrinkToFit(std::vector<cv::Point3i>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_Point3iG_reserve_size_t(std::vector<cv::Point3i>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_Point3iG_remove_size_t(std::vector<cv::Point3i>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_Point3iG_swap_size_t_size_t(std::vector<cv::Point3i>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_Point3iG_clear(std::vector<cv::Point3i>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_Point3iG_push_const_Point3i(std::vector<cv::Point3i>* instance, const cv::Point3i* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_Point3iG_insert_size_t_const_Point3i(std::vector<cv::Point3i>* instance, size_t index, const cv::Point3i* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_Point3iG_get_const_size_t(const std::vector<cv::Point3i>* instance, size_t index, cv::Point3i* ocvrs_return) {
			cv::Point3i ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLcv_Point3iG_set_size_t_const_Point3i(std::vector<cv::Point3i>* instance, size_t index, const cv::Point3i* val) {
			(*instance)[index] = *val;
	}
	
	std::vector<cv::Point3i>* std_vectorLcv_Point3iG_clone_const(const std::vector<cv::Point3i>* instance) {
			std::vector<cv::Point3i> ret = std::vector<cv::Point3i>(*instance);
			return new std::vector<cv::Point3i>(ret);
	}
	
	const cv::Point3i* std_vectorLcv_Point3iG_data_const(const std::vector<cv::Point3i>* instance) {
			const cv::Point3i* ret = instance->data();
			return ret;
	}
	
	cv::Point3i* std_vectorLcv_Point3iG_dataMut(std::vector<cv::Point3i>* instance) {
			cv::Point3i* ret = instance->data();
			return ret;
	}
	
	std::vector<cv::Point3i>* cv_fromSlice_const_const_Point3iX_size_t(const cv::Point3i* data, size_t len) {
			return new std::vector<cv::Point3i>(data, data + len);
	}
	
	void std_vectorLcv_Point3iG_inputArray_const(const std::vector<cv::Point3i>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Point3iG_outputArray(std::vector<cv::Point3i>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Point3iG_inputOutputArray(std::vector<cv::Point3i>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<cv::Range>* std_vectorLcv_RangeG_new_const() {
			std::vector<cv::Range>* ret = new std::vector<cv::Range>();
			return ret;
	}
	
	void std_vectorLcv_RangeG_delete(std::vector<cv::Range>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_RangeG_len_const(const std::vector<cv::Range>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_RangeG_isEmpty_const(const std::vector<cv::Range>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_RangeG_capacity_const(const std::vector<cv::Range>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_RangeG_shrinkToFit(std::vector<cv::Range>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_RangeG_reserve_size_t(std::vector<cv::Range>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_RangeG_remove_size_t(std::vector<cv::Range>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_RangeG_swap_size_t_size_t(std::vector<cv::Range>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_RangeG_clear(std::vector<cv::Range>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_RangeG_push_const_Range(std::vector<cv::Range>* instance, const cv::Range* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_RangeG_insert_size_t_const_Range(std::vector<cv::Range>* instance, size_t index, const cv::Range* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_RangeG_get_const_size_t(const std::vector<cv::Range>* instance, size_t index, cv::Range** ocvrs_return) {
			cv::Range ret = (*instance)[index];
			*ocvrs_return = new cv::Range(ret);
	}
	
	void std_vectorLcv_RangeG_set_size_t_const_Range(std::vector<cv::Range>* instance, size_t index, const cv::Range* val) {
			(*instance)[index] = *val;
	}
	
}


extern "C" {
	std::vector<cv::Rect>* std_vectorLcv_RectG_new_const() {
			std::vector<cv::Rect>* ret = new std::vector<cv::Rect>();
			return ret;
	}
	
	void std_vectorLcv_RectG_delete(std::vector<cv::Rect>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_RectG_len_const(const std::vector<cv::Rect>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_RectG_isEmpty_const(const std::vector<cv::Rect>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_RectG_capacity_const(const std::vector<cv::Rect>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_RectG_shrinkToFit(std::vector<cv::Rect>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_RectG_reserve_size_t(std::vector<cv::Rect>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_RectG_remove_size_t(std::vector<cv::Rect>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_RectG_swap_size_t_size_t(std::vector<cv::Rect>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_RectG_clear(std::vector<cv::Rect>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_RectG_push_const_Rect(std::vector<cv::Rect>* instance, const cv::Rect* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_RectG_insert_size_t_const_Rect(std::vector<cv::Rect>* instance, size_t index, const cv::Rect* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_RectG_get_const_size_t(const std::vector<cv::Rect>* instance, size_t index, cv::Rect* ocvrs_return) {
			cv::Rect ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLcv_RectG_set_size_t_const_Rect(std::vector<cv::Rect>* instance, size_t index, const cv::Rect* val) {
			(*instance)[index] = *val;
	}
	
	std::vector<cv::Rect>* std_vectorLcv_RectG_clone_const(const std::vector<cv::Rect>* instance) {
			std::vector<cv::Rect> ret = std::vector<cv::Rect>(*instance);
			return new std::vector<cv::Rect>(ret);
	}
	
	const cv::Rect* std_vectorLcv_RectG_data_const(const std::vector<cv::Rect>* instance) {
			const cv::Rect* ret = instance->data();
			return ret;
	}
	
	cv::Rect* std_vectorLcv_RectG_dataMut(std::vector<cv::Rect>* instance) {
			cv::Rect* ret = instance->data();
			return ret;
	}
	
	std::vector<cv::Rect>* cv_fromSlice_const_const_RectX_size_t(const cv::Rect* data, size_t len) {
			return new std::vector<cv::Rect>(data, data + len);
	}
	
	void std_vectorLcv_RectG_inputArray_const(const std::vector<cv::Rect>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_RectG_outputArray(std::vector<cv::Rect>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_RectG_inputOutputArray(std::vector<cv::Rect>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<cv::Rect2d>* std_vectorLcv_Rect2dG_new_const() {
			std::vector<cv::Rect2d>* ret = new std::vector<cv::Rect2d>();
			return ret;
	}
	
	void std_vectorLcv_Rect2dG_delete(std::vector<cv::Rect2d>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_Rect2dG_len_const(const std::vector<cv::Rect2d>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_Rect2dG_isEmpty_const(const std::vector<cv::Rect2d>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_Rect2dG_capacity_const(const std::vector<cv::Rect2d>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_Rect2dG_shrinkToFit(std::vector<cv::Rect2d>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_Rect2dG_reserve_size_t(std::vector<cv::Rect2d>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_Rect2dG_remove_size_t(std::vector<cv::Rect2d>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_Rect2dG_swap_size_t_size_t(std::vector<cv::Rect2d>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_Rect2dG_clear(std::vector<cv::Rect2d>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_Rect2dG_push_const_Rect2d(std::vector<cv::Rect2d>* instance, const cv::Rect2d* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_Rect2dG_insert_size_t_const_Rect2d(std::vector<cv::Rect2d>* instance, size_t index, const cv::Rect2d* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_Rect2dG_get_const_size_t(const std::vector<cv::Rect2d>* instance, size_t index, cv::Rect2d* ocvrs_return) {
			cv::Rect2d ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLcv_Rect2dG_set_size_t_const_Rect2d(std::vector<cv::Rect2d>* instance, size_t index, const cv::Rect2d* val) {
			(*instance)[index] = *val;
	}
	
	std::vector<cv::Rect2d>* std_vectorLcv_Rect2dG_clone_const(const std::vector<cv::Rect2d>* instance) {
			std::vector<cv::Rect2d> ret = std::vector<cv::Rect2d>(*instance);
			return new std::vector<cv::Rect2d>(ret);
	}
	
	const cv::Rect2d* std_vectorLcv_Rect2dG_data_const(const std::vector<cv::Rect2d>* instance) {
			const cv::Rect2d* ret = instance->data();
			return ret;
	}
	
	cv::Rect2d* std_vectorLcv_Rect2dG_dataMut(std::vector<cv::Rect2d>* instance) {
			cv::Rect2d* ret = instance->data();
			return ret;
	}
	
	std::vector<cv::Rect2d>* cv_fromSlice_const_const_Rect2dX_size_t(const cv::Rect2d* data, size_t len) {
			return new std::vector<cv::Rect2d>(data, data + len);
	}
	
	void std_vectorLcv_Rect2dG_inputArray_const(const std::vector<cv::Rect2d>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Rect2dG_outputArray(std::vector<cv::Rect2d>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Rect2dG_inputOutputArray(std::vector<cv::Rect2d>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<cv::RotatedRect>* std_vectorLcv_RotatedRectG_new_const() {
			std::vector<cv::RotatedRect>* ret = new std::vector<cv::RotatedRect>();
			return ret;
	}
	
	void std_vectorLcv_RotatedRectG_delete(std::vector<cv::RotatedRect>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_RotatedRectG_len_const(const std::vector<cv::RotatedRect>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_RotatedRectG_isEmpty_const(const std::vector<cv::RotatedRect>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_RotatedRectG_capacity_const(const std::vector<cv::RotatedRect>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_RotatedRectG_shrinkToFit(std::vector<cv::RotatedRect>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_RotatedRectG_reserve_size_t(std::vector<cv::RotatedRect>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_RotatedRectG_remove_size_t(std::vector<cv::RotatedRect>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_RotatedRectG_swap_size_t_size_t(std::vector<cv::RotatedRect>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_RotatedRectG_clear(std::vector<cv::RotatedRect>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_RotatedRectG_push_const_RotatedRect(std::vector<cv::RotatedRect>* instance, const cv::RotatedRect* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_RotatedRectG_insert_size_t_const_RotatedRect(std::vector<cv::RotatedRect>* instance, size_t index, const cv::RotatedRect* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_RotatedRectG_get_const_size_t(const std::vector<cv::RotatedRect>* instance, size_t index, cv::RotatedRect* ocvrs_return) {
			cv::RotatedRect ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLcv_RotatedRectG_set_size_t_const_RotatedRect(std::vector<cv::RotatedRect>* instance, size_t index, const cv::RotatedRect* val) {
			(*instance)[index] = *val;
	}
	
	std::vector<cv::RotatedRect>* std_vectorLcv_RotatedRectG_clone_const(const std::vector<cv::RotatedRect>* instance) {
			std::vector<cv::RotatedRect> ret = std::vector<cv::RotatedRect>(*instance);
			return new std::vector<cv::RotatedRect>(ret);
	}
	
	const cv::RotatedRect* std_vectorLcv_RotatedRectG_data_const(const std::vector<cv::RotatedRect>* instance) {
			const cv::RotatedRect* ret = instance->data();
			return ret;
	}
	
	cv::RotatedRect* std_vectorLcv_RotatedRectG_dataMut(std::vector<cv::RotatedRect>* instance) {
			cv::RotatedRect* ret = instance->data();
			return ret;
	}
	
	std::vector<cv::RotatedRect>* cv_fromSlice_const_const_RotatedRectX_size_t(const cv::RotatedRect* data, size_t len) {
			return new std::vector<cv::RotatedRect>(data, data + len);
	}
	
}


extern "C" {
	std::vector<cv::Scalar>* std_vectorLcv_ScalarG_new_const() {
			std::vector<cv::Scalar>* ret = new std::vector<cv::Scalar>();
			return ret;
	}
	
	void std_vectorLcv_ScalarG_delete(std::vector<cv::Scalar>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_ScalarG_len_const(const std::vector<cv::Scalar>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_ScalarG_isEmpty_const(const std::vector<cv::Scalar>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_ScalarG_capacity_const(const std::vector<cv::Scalar>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_ScalarG_shrinkToFit(std::vector<cv::Scalar>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_ScalarG_reserve_size_t(std::vector<cv::Scalar>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_ScalarG_remove_size_t(std::vector<cv::Scalar>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_ScalarG_swap_size_t_size_t(std::vector<cv::Scalar>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_ScalarG_clear(std::vector<cv::Scalar>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_ScalarG_push_const_Scalar(std::vector<cv::Scalar>* instance, const cv::Scalar* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_ScalarG_insert_size_t_const_Scalar(std::vector<cv::Scalar>* instance, size_t index, const cv::Scalar* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_ScalarG_get_const_size_t(const std::vector<cv::Scalar>* instance, size_t index, cv::Scalar* ocvrs_return) {
			cv::Scalar ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLcv_ScalarG_set_size_t_const_Scalar(std::vector<cv::Scalar>* instance, size_t index, const cv::Scalar* val) {
			(*instance)[index] = *val;
	}
	
	std::vector<cv::Scalar>* std_vectorLcv_ScalarG_clone_const(const std::vector<cv::Scalar>* instance) {
			std::vector<cv::Scalar> ret = std::vector<cv::Scalar>(*instance);
			return new std::vector<cv::Scalar>(ret);
	}
	
	const cv::Scalar* std_vectorLcv_ScalarG_data_const(const std::vector<cv::Scalar>* instance) {
			const cv::Scalar* ret = instance->data();
			return ret;
	}
	
	cv::Scalar* std_vectorLcv_ScalarG_dataMut(std::vector<cv::Scalar>* instance) {
			cv::Scalar* ret = instance->data();
			return ret;
	}
	
	std::vector<cv::Scalar>* cv_fromSlice_const_const_ScalarX_size_t(const cv::Scalar* data, size_t len) {
			return new std::vector<cv::Scalar>(data, data + len);
	}
	
	void std_vectorLcv_ScalarG_inputArray_const(const std::vector<cv::Scalar>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_ScalarG_outputArray(std::vector<cv::Scalar>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_ScalarG_inputOutputArray(std::vector<cv::Scalar>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<cv::Size>* std_vectorLcv_SizeG_new_const() {
			std::vector<cv::Size>* ret = new std::vector<cv::Size>();
			return ret;
	}
	
	void std_vectorLcv_SizeG_delete(std::vector<cv::Size>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_SizeG_len_const(const std::vector<cv::Size>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_SizeG_isEmpty_const(const std::vector<cv::Size>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_SizeG_capacity_const(const std::vector<cv::Size>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_SizeG_shrinkToFit(std::vector<cv::Size>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_SizeG_reserve_size_t(std::vector<cv::Size>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_SizeG_remove_size_t(std::vector<cv::Size>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_SizeG_swap_size_t_size_t(std::vector<cv::Size>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_SizeG_clear(std::vector<cv::Size>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_SizeG_push_const_Size(std::vector<cv::Size>* instance, const cv::Size* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_SizeG_insert_size_t_const_Size(std::vector<cv::Size>* instance, size_t index, const cv::Size* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_SizeG_get_const_size_t(const std::vector<cv::Size>* instance, size_t index, cv::Size* ocvrs_return) {
			cv::Size ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLcv_SizeG_set_size_t_const_Size(std::vector<cv::Size>* instance, size_t index, const cv::Size* val) {
			(*instance)[index] = *val;
	}
	
	std::vector<cv::Size>* std_vectorLcv_SizeG_clone_const(const std::vector<cv::Size>* instance) {
			std::vector<cv::Size> ret = std::vector<cv::Size>(*instance);
			return new std::vector<cv::Size>(ret);
	}
	
	const cv::Size* std_vectorLcv_SizeG_data_const(const std::vector<cv::Size>* instance) {
			const cv::Size* ret = instance->data();
			return ret;
	}
	
	cv::Size* std_vectorLcv_SizeG_dataMut(std::vector<cv::Size>* instance) {
			cv::Size* ret = instance->data();
			return ret;
	}
	
	std::vector<cv::Size>* cv_fromSlice_const_const_SizeX_size_t(const cv::Size* data, size_t len) {
			return new std::vector<cv::Size>(data, data + len);
	}
	
	void std_vectorLcv_SizeG_inputArray_const(const std::vector<cv::Size>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_SizeG_outputArray(std::vector<cv::Size>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_SizeG_inputOutputArray(std::vector<cv::Size>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<cv::String>* std_vectorLcv_StringG_new_const() {
			std::vector<cv::String>* ret = new std::vector<cv::String>();
			return ret;
	}
	
	void std_vectorLcv_StringG_delete(std::vector<cv::String>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_StringG_len_const(const std::vector<cv::String>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_StringG_isEmpty_const(const std::vector<cv::String>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_StringG_capacity_const(const std::vector<cv::String>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_StringG_shrinkToFit(std::vector<cv::String>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_StringG_reserve_size_t(std::vector<cv::String>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_StringG_remove_size_t(std::vector<cv::String>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_StringG_swap_size_t_size_t(std::vector<cv::String>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_StringG_clear(std::vector<cv::String>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_StringG_push_const_String(std::vector<cv::String>* instance, const char* val) {
			instance->push_back(cv::String(val));
	}
	
	void std_vectorLcv_StringG_insert_size_t_const_String(std::vector<cv::String>* instance, size_t index, const char* val) {
			instance->insert(instance->begin() + index, cv::String(val));
	}
	
	void std_vectorLcv_StringG_get_const_size_t(const std::vector<cv::String>* instance, size_t index, void** ocvrs_return) {
			cv::String ret = (*instance)[index];
			*ocvrs_return = ocvrs_create_string(ret.c_str());
	}
	
	void std_vectorLcv_StringG_set_size_t_const_String(std::vector<cv::String>* instance, size_t index, const char* val) {
			(*instance)[index] = cv::String(val);
	}
	
}


extern "C" {
	std::vector<std::pair<cv::Point2i, cv::Point2i>>* std_vectorLstd_pairLcv_Point2i__cv_Point2iGG_new_const() {
			std::vector<std::pair<cv::Point2i, cv::Point2i>>* ret = new std::vector<std::pair<cv::Point2i, cv::Point2i>>();
			return ret;
	}
	
	void std_vectorLstd_pairLcv_Point2i__cv_Point2iGG_delete(std::vector<std::pair<cv::Point2i, cv::Point2i>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLstd_pairLcv_Point2i__cv_Point2iGG_len_const(const std::vector<std::pair<cv::Point2i, cv::Point2i>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLstd_pairLcv_Point2i__cv_Point2iGG_isEmpty_const(const std::vector<std::pair<cv::Point2i, cv::Point2i>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLstd_pairLcv_Point2i__cv_Point2iGG_capacity_const(const std::vector<std::pair<cv::Point2i, cv::Point2i>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLstd_pairLcv_Point2i__cv_Point2iGG_shrinkToFit(std::vector<std::pair<cv::Point2i, cv::Point2i>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLstd_pairLcv_Point2i__cv_Point2iGG_reserve_size_t(std::vector<std::pair<cv::Point2i, cv::Point2i>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLstd_pairLcv_Point2i__cv_Point2iGG_remove_size_t(std::vector<std::pair<cv::Point2i, cv::Point2i>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLstd_pairLcv_Point2i__cv_Point2iGG_swap_size_t_size_t(std::vector<std::pair<cv::Point2i, cv::Point2i>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLstd_pairLcv_Point2i__cv_Point2iGG_clear(std::vector<std::pair<cv::Point2i, cv::Point2i>>* instance) {
			instance->clear();
	}
	
	void std_vectorLstd_pairLcv_Point2i__cv_Point2iGG_push_const_pairLcv_Point2i__cv_Point2iG(std::vector<std::pair<cv::Point2i, cv::Point2i>>* instance, const std::pair<cv::Point2i, cv::Point2i>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLstd_pairLcv_Point2i__cv_Point2iGG_insert_size_t_const_pairLcv_Point2i__cv_Point2iG(std::vector<std::pair<cv::Point2i, cv::Point2i>>* instance, size_t index, const std::pair<cv::Point2i, cv::Point2i>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLstd_pairLcv_Point2i__cv_Point2iGG_get_const_size_t(const std::vector<std::pair<cv::Point2i, cv::Point2i>>* instance, size_t index, std::pair<cv::Point2i, cv::Point2i>** ocvrs_return) {
			std::pair<cv::Point2i, cv::Point2i> ret = (*instance)[index];
			*ocvrs_return = new std::pair<cv::Point2i, cv::Point2i>(ret);
	}
	
	void std_vectorLstd_pairLcv_Point2i__cv_Point2iGG_set_size_t_const_pairLcv_Point2i__cv_Point2iG(std::vector<std::pair<cv::Point2i, cv::Point2i>>* instance, size_t index, const std::pair<cv::Point2i, cv::Point2i>* val) {
			(*instance)[index] = *val;
	}
	
}


extern "C" {
	std::vector<std::pair<cv::UMat, unsigned char>>* std_vectorLstd_pairLcv_UMat__unsigned_charGG_new_const() {
			std::vector<std::pair<cv::UMat, unsigned char>>* ret = new std::vector<std::pair<cv::UMat, unsigned char>>();
			return ret;
	}
	
	void std_vectorLstd_pairLcv_UMat__unsigned_charGG_delete(std::vector<std::pair<cv::UMat, unsigned char>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLstd_pairLcv_UMat__unsigned_charGG_len_const(const std::vector<std::pair<cv::UMat, unsigned char>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLstd_pairLcv_UMat__unsigned_charGG_isEmpty_const(const std::vector<std::pair<cv::UMat, unsigned char>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLstd_pairLcv_UMat__unsigned_charGG_capacity_const(const std::vector<std::pair<cv::UMat, unsigned char>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLstd_pairLcv_UMat__unsigned_charGG_shrinkToFit(std::vector<std::pair<cv::UMat, unsigned char>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLstd_pairLcv_UMat__unsigned_charGG_reserve_size_t(std::vector<std::pair<cv::UMat, unsigned char>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLstd_pairLcv_UMat__unsigned_charGG_remove_size_t(std::vector<std::pair<cv::UMat, unsigned char>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLstd_pairLcv_UMat__unsigned_charGG_swap_size_t_size_t(std::vector<std::pair<cv::UMat, unsigned char>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLstd_pairLcv_UMat__unsigned_charGG_clear(std::vector<std::pair<cv::UMat, unsigned char>>* instance) {
			instance->clear();
	}
	
	void std_vectorLstd_pairLcv_UMat__unsigned_charGG_push_const_pairLcv_UMat__unsigned_charG(std::vector<std::pair<cv::UMat, unsigned char>>* instance, const std::pair<cv::UMat, unsigned char>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLstd_pairLcv_UMat__unsigned_charGG_insert_size_t_const_pairLcv_UMat__unsigned_charG(std::vector<std::pair<cv::UMat, unsigned char>>* instance, size_t index, const std::pair<cv::UMat, unsigned char>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLstd_pairLcv_UMat__unsigned_charGG_get_const_size_t(const std::vector<std::pair<cv::UMat, unsigned char>>* instance, size_t index, std::pair<cv::UMat, unsigned char>** ocvrs_return) {
			std::pair<cv::UMat, unsigned char> ret = (*instance)[index];
			*ocvrs_return = new std::pair<cv::UMat, unsigned char>(ret);
	}
	
	void std_vectorLstd_pairLcv_UMat__unsigned_charGG_set_size_t_const_pairLcv_UMat__unsigned_charG(std::vector<std::pair<cv::UMat, unsigned char>>* instance, size_t index, const std::pair<cv::UMat, unsigned char>* val) {
			(*instance)[index] = *val;
	}
	
}


extern "C" {
	std::vector<std::pair<int, double>>* std_vectorLstd_pairLint__doubleGG_new_const() {
			std::vector<std::pair<int, double>>* ret = new std::vector<std::pair<int, double>>();
			return ret;
	}
	
	void std_vectorLstd_pairLint__doubleGG_delete(std::vector<std::pair<int, double>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLstd_pairLint__doubleGG_len_const(const std::vector<std::pair<int, double>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLstd_pairLint__doubleGG_isEmpty_const(const std::vector<std::pair<int, double>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLstd_pairLint__doubleGG_capacity_const(const std::vector<std::pair<int, double>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLstd_pairLint__doubleGG_shrinkToFit(std::vector<std::pair<int, double>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLstd_pairLint__doubleGG_reserve_size_t(std::vector<std::pair<int, double>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLstd_pairLint__doubleGG_remove_size_t(std::vector<std::pair<int, double>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLstd_pairLint__doubleGG_swap_size_t_size_t(std::vector<std::pair<int, double>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLstd_pairLint__doubleGG_clear(std::vector<std::pair<int, double>>* instance) {
			instance->clear();
	}
	
	void std_vectorLstd_pairLint__doubleGG_push_const_pairLint__doubleG(std::vector<std::pair<int, double>>* instance, const std::pair<int, double>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLstd_pairLint__doubleGG_insert_size_t_const_pairLint__doubleG(std::vector<std::pair<int, double>>* instance, size_t index, const std::pair<int, double>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLstd_pairLint__doubleGG_get_const_size_t(const std::vector<std::pair<int, double>>* instance, size_t index, std::pair<int, double>** ocvrs_return) {
			std::pair<int, double> ret = (*instance)[index];
			*ocvrs_return = new std::pair<int, double>(ret);
	}
	
	void std_vectorLstd_pairLint__doubleGG_set_size_t_const_pairLint__doubleG(std::vector<std::pair<int, double>>* instance, size_t index, const std::pair<int, double>* val) {
			(*instance)[index] = *val;
	}
	
}


extern "C" {
	std::vector<cv::UMat>* std_vectorLcv_UMatG_new_const() {
			std::vector<cv::UMat>* ret = new std::vector<cv::UMat>();
			return ret;
	}
	
	void std_vectorLcv_UMatG_delete(std::vector<cv::UMat>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_UMatG_len_const(const std::vector<cv::UMat>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_UMatG_isEmpty_const(const std::vector<cv::UMat>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_UMatG_capacity_const(const std::vector<cv::UMat>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_UMatG_shrinkToFit(std::vector<cv::UMat>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_UMatG_reserve_size_t(std::vector<cv::UMat>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_UMatG_remove_size_t(std::vector<cv::UMat>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_UMatG_swap_size_t_size_t(std::vector<cv::UMat>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_UMatG_clear(std::vector<cv::UMat>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_UMatG_push_const_UMat(std::vector<cv::UMat>* instance, const cv::UMat* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_UMatG_insert_size_t_const_UMat(std::vector<cv::UMat>* instance, size_t index, const cv::UMat* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_UMatG_get_const_size_t(const std::vector<cv::UMat>* instance, size_t index, cv::UMat** ocvrs_return) {
			cv::UMat ret = (*instance)[index];
			*ocvrs_return = new cv::UMat(ret);
	}
	
	void std_vectorLcv_UMatG_set_size_t_const_UMat(std::vector<cv::UMat>* instance, size_t index, const cv::UMat* val) {
			(*instance)[index] = *val;
	}
	
}


extern "C" {
	std::vector<cv::Vec2d>* std_vectorLcv_Vec2dG_new_const() {
			std::vector<cv::Vec2d>* ret = new std::vector<cv::Vec2d>();
			return ret;
	}
	
	void std_vectorLcv_Vec2dG_delete(std::vector<cv::Vec2d>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_Vec2dG_len_const(const std::vector<cv::Vec2d>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_Vec2dG_isEmpty_const(const std::vector<cv::Vec2d>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_Vec2dG_capacity_const(const std::vector<cv::Vec2d>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_Vec2dG_shrinkToFit(std::vector<cv::Vec2d>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_Vec2dG_reserve_size_t(std::vector<cv::Vec2d>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_Vec2dG_remove_size_t(std::vector<cv::Vec2d>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_Vec2dG_swap_size_t_size_t(std::vector<cv::Vec2d>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_Vec2dG_clear(std::vector<cv::Vec2d>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_Vec2dG_push_const_Vec2d(std::vector<cv::Vec2d>* instance, const cv::Vec2d* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_Vec2dG_insert_size_t_const_Vec2d(std::vector<cv::Vec2d>* instance, size_t index, const cv::Vec2d* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_Vec2dG_get_const_size_t(const std::vector<cv::Vec2d>* instance, size_t index, cv::Vec2d* ocvrs_return) {
			cv::Vec2d ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLcv_Vec2dG_set_size_t_const_Vec2d(std::vector<cv::Vec2d>* instance, size_t index, const cv::Vec2d* val) {
			(*instance)[index] = *val;
	}
	
	std::vector<cv::Vec2d>* std_vectorLcv_Vec2dG_clone_const(const std::vector<cv::Vec2d>* instance) {
			std::vector<cv::Vec2d> ret = std::vector<cv::Vec2d>(*instance);
			return new std::vector<cv::Vec2d>(ret);
	}
	
	const cv::Vec2d* std_vectorLcv_Vec2dG_data_const(const std::vector<cv::Vec2d>* instance) {
			const cv::Vec2d* ret = instance->data();
			return ret;
	}
	
	cv::Vec2d* std_vectorLcv_Vec2dG_dataMut(std::vector<cv::Vec2d>* instance) {
			cv::Vec2d* ret = instance->data();
			return ret;
	}
	
	std::vector<cv::Vec2d>* cv_fromSlice_const_const_Vec2dX_size_t(const cv::Vec2d* data, size_t len) {
			return new std::vector<cv::Vec2d>(data, data + len);
	}
	
	void std_vectorLcv_Vec2dG_inputArray_const(const std::vector<cv::Vec2d>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Vec2dG_outputArray(std::vector<cv::Vec2d>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Vec2dG_inputOutputArray(std::vector<cv::Vec2d>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<cv::Vec2f>* std_vectorLcv_Vec2fG_new_const() {
			std::vector<cv::Vec2f>* ret = new std::vector<cv::Vec2f>();
			return ret;
	}
	
	void std_vectorLcv_Vec2fG_delete(std::vector<cv::Vec2f>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_Vec2fG_len_const(const std::vector<cv::Vec2f>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_Vec2fG_isEmpty_const(const std::vector<cv::Vec2f>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_Vec2fG_capacity_const(const std::vector<cv::Vec2f>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_Vec2fG_shrinkToFit(std::vector<cv::Vec2f>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_Vec2fG_reserve_size_t(std::vector<cv::Vec2f>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_Vec2fG_remove_size_t(std::vector<cv::Vec2f>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_Vec2fG_swap_size_t_size_t(std::vector<cv::Vec2f>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_Vec2fG_clear(std::vector<cv::Vec2f>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_Vec2fG_push_const_Vec2f(std::vector<cv::Vec2f>* instance, const cv::Vec2f* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_Vec2fG_insert_size_t_const_Vec2f(std::vector<cv::Vec2f>* instance, size_t index, const cv::Vec2f* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_Vec2fG_get_const_size_t(const std::vector<cv::Vec2f>* instance, size_t index, cv::Vec2f* ocvrs_return) {
			cv::Vec2f ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLcv_Vec2fG_set_size_t_const_Vec2f(std::vector<cv::Vec2f>* instance, size_t index, const cv::Vec2f* val) {
			(*instance)[index] = *val;
	}
	
	std::vector<cv::Vec2f>* std_vectorLcv_Vec2fG_clone_const(const std::vector<cv::Vec2f>* instance) {
			std::vector<cv::Vec2f> ret = std::vector<cv::Vec2f>(*instance);
			return new std::vector<cv::Vec2f>(ret);
	}
	
	const cv::Vec2f* std_vectorLcv_Vec2fG_data_const(const std::vector<cv::Vec2f>* instance) {
			const cv::Vec2f* ret = instance->data();
			return ret;
	}
	
	cv::Vec2f* std_vectorLcv_Vec2fG_dataMut(std::vector<cv::Vec2f>* instance) {
			cv::Vec2f* ret = instance->data();
			return ret;
	}
	
	std::vector<cv::Vec2f>* cv_fromSlice_const_const_Vec2fX_size_t(const cv::Vec2f* data, size_t len) {
			return new std::vector<cv::Vec2f>(data, data + len);
	}
	
	void std_vectorLcv_Vec2fG_inputArray_const(const std::vector<cv::Vec2f>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Vec2fG_outputArray(std::vector<cv::Vec2f>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Vec2fG_inputOutputArray(std::vector<cv::Vec2f>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<cv::Vec2i>* std_vectorLcv_Vec2iG_new_const() {
			std::vector<cv::Vec2i>* ret = new std::vector<cv::Vec2i>();
			return ret;
	}
	
	void std_vectorLcv_Vec2iG_delete(std::vector<cv::Vec2i>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_Vec2iG_len_const(const std::vector<cv::Vec2i>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_Vec2iG_isEmpty_const(const std::vector<cv::Vec2i>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_Vec2iG_capacity_const(const std::vector<cv::Vec2i>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_Vec2iG_shrinkToFit(std::vector<cv::Vec2i>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_Vec2iG_reserve_size_t(std::vector<cv::Vec2i>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_Vec2iG_remove_size_t(std::vector<cv::Vec2i>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_Vec2iG_swap_size_t_size_t(std::vector<cv::Vec2i>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_Vec2iG_clear(std::vector<cv::Vec2i>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_Vec2iG_push_const_Vec2i(std::vector<cv::Vec2i>* instance, const cv::Vec2i* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_Vec2iG_insert_size_t_const_Vec2i(std::vector<cv::Vec2i>* instance, size_t index, const cv::Vec2i* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_Vec2iG_get_const_size_t(const std::vector<cv::Vec2i>* instance, size_t index, cv::Vec2i* ocvrs_return) {
			cv::Vec2i ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLcv_Vec2iG_set_size_t_const_Vec2i(std::vector<cv::Vec2i>* instance, size_t index, const cv::Vec2i* val) {
			(*instance)[index] = *val;
	}
	
	std::vector<cv::Vec2i>* std_vectorLcv_Vec2iG_clone_const(const std::vector<cv::Vec2i>* instance) {
			std::vector<cv::Vec2i> ret = std::vector<cv::Vec2i>(*instance);
			return new std::vector<cv::Vec2i>(ret);
	}
	
	const cv::Vec2i* std_vectorLcv_Vec2iG_data_const(const std::vector<cv::Vec2i>* instance) {
			const cv::Vec2i* ret = instance->data();
			return ret;
	}
	
	cv::Vec2i* std_vectorLcv_Vec2iG_dataMut(std::vector<cv::Vec2i>* instance) {
			cv::Vec2i* ret = instance->data();
			return ret;
	}
	
	std::vector<cv::Vec2i>* cv_fromSlice_const_const_Vec2iX_size_t(const cv::Vec2i* data, size_t len) {
			return new std::vector<cv::Vec2i>(data, data + len);
	}
	
	void std_vectorLcv_Vec2iG_inputArray_const(const std::vector<cv::Vec2i>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Vec2iG_outputArray(std::vector<cv::Vec2i>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Vec2iG_inputOutputArray(std::vector<cv::Vec2i>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<cv::Vec3d>* std_vectorLcv_Vec3dG_new_const() {
			std::vector<cv::Vec3d>* ret = new std::vector<cv::Vec3d>();
			return ret;
	}
	
	void std_vectorLcv_Vec3dG_delete(std::vector<cv::Vec3d>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_Vec3dG_len_const(const std::vector<cv::Vec3d>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_Vec3dG_isEmpty_const(const std::vector<cv::Vec3d>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_Vec3dG_capacity_const(const std::vector<cv::Vec3d>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_Vec3dG_shrinkToFit(std::vector<cv::Vec3d>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_Vec3dG_reserve_size_t(std::vector<cv::Vec3d>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_Vec3dG_remove_size_t(std::vector<cv::Vec3d>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_Vec3dG_swap_size_t_size_t(std::vector<cv::Vec3d>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_Vec3dG_clear(std::vector<cv::Vec3d>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_Vec3dG_push_const_Vec3d(std::vector<cv::Vec3d>* instance, const cv::Vec3d* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_Vec3dG_insert_size_t_const_Vec3d(std::vector<cv::Vec3d>* instance, size_t index, const cv::Vec3d* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_Vec3dG_get_const_size_t(const std::vector<cv::Vec3d>* instance, size_t index, cv::Vec3d* ocvrs_return) {
			cv::Vec3d ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLcv_Vec3dG_set_size_t_const_Vec3d(std::vector<cv::Vec3d>* instance, size_t index, const cv::Vec3d* val) {
			(*instance)[index] = *val;
	}
	
	std::vector<cv::Vec3d>* std_vectorLcv_Vec3dG_clone_const(const std::vector<cv::Vec3d>* instance) {
			std::vector<cv::Vec3d> ret = std::vector<cv::Vec3d>(*instance);
			return new std::vector<cv::Vec3d>(ret);
	}
	
	const cv::Vec3d* std_vectorLcv_Vec3dG_data_const(const std::vector<cv::Vec3d>* instance) {
			const cv::Vec3d* ret = instance->data();
			return ret;
	}
	
	cv::Vec3d* std_vectorLcv_Vec3dG_dataMut(std::vector<cv::Vec3d>* instance) {
			cv::Vec3d* ret = instance->data();
			return ret;
	}
	
	std::vector<cv::Vec3d>* cv_fromSlice_const_const_Vec3dX_size_t(const cv::Vec3d* data, size_t len) {
			return new std::vector<cv::Vec3d>(data, data + len);
	}
	
	void std_vectorLcv_Vec3dG_inputArray_const(const std::vector<cv::Vec3d>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Vec3dG_outputArray(std::vector<cv::Vec3d>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Vec3dG_inputOutputArray(std::vector<cv::Vec3d>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<cv::Vec3f>* std_vectorLcv_Vec3fG_new_const() {
			std::vector<cv::Vec3f>* ret = new std::vector<cv::Vec3f>();
			return ret;
	}
	
	void std_vectorLcv_Vec3fG_delete(std::vector<cv::Vec3f>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_Vec3fG_len_const(const std::vector<cv::Vec3f>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_Vec3fG_isEmpty_const(const std::vector<cv::Vec3f>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_Vec3fG_capacity_const(const std::vector<cv::Vec3f>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_Vec3fG_shrinkToFit(std::vector<cv::Vec3f>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_Vec3fG_reserve_size_t(std::vector<cv::Vec3f>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_Vec3fG_remove_size_t(std::vector<cv::Vec3f>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_Vec3fG_swap_size_t_size_t(std::vector<cv::Vec3f>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_Vec3fG_clear(std::vector<cv::Vec3f>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_Vec3fG_push_const_Vec3f(std::vector<cv::Vec3f>* instance, const cv::Vec3f* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_Vec3fG_insert_size_t_const_Vec3f(std::vector<cv::Vec3f>* instance, size_t index, const cv::Vec3f* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_Vec3fG_get_const_size_t(const std::vector<cv::Vec3f>* instance, size_t index, cv::Vec3f* ocvrs_return) {
			cv::Vec3f ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLcv_Vec3fG_set_size_t_const_Vec3f(std::vector<cv::Vec3f>* instance, size_t index, const cv::Vec3f* val) {
			(*instance)[index] = *val;
	}
	
	std::vector<cv::Vec3f>* std_vectorLcv_Vec3fG_clone_const(const std::vector<cv::Vec3f>* instance) {
			std::vector<cv::Vec3f> ret = std::vector<cv::Vec3f>(*instance);
			return new std::vector<cv::Vec3f>(ret);
	}
	
	const cv::Vec3f* std_vectorLcv_Vec3fG_data_const(const std::vector<cv::Vec3f>* instance) {
			const cv::Vec3f* ret = instance->data();
			return ret;
	}
	
	cv::Vec3f* std_vectorLcv_Vec3fG_dataMut(std::vector<cv::Vec3f>* instance) {
			cv::Vec3f* ret = instance->data();
			return ret;
	}
	
	std::vector<cv::Vec3f>* cv_fromSlice_const_const_Vec3fX_size_t(const cv::Vec3f* data, size_t len) {
			return new std::vector<cv::Vec3f>(data, data + len);
	}
	
	void std_vectorLcv_Vec3fG_inputArray_const(const std::vector<cv::Vec3f>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Vec3fG_outputArray(std::vector<cv::Vec3f>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Vec3fG_inputOutputArray(std::vector<cv::Vec3f>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<cv::Vec3i>* std_vectorLcv_Vec3iG_new_const() {
			std::vector<cv::Vec3i>* ret = new std::vector<cv::Vec3i>();
			return ret;
	}
	
	void std_vectorLcv_Vec3iG_delete(std::vector<cv::Vec3i>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_Vec3iG_len_const(const std::vector<cv::Vec3i>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_Vec3iG_isEmpty_const(const std::vector<cv::Vec3i>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_Vec3iG_capacity_const(const std::vector<cv::Vec3i>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_Vec3iG_shrinkToFit(std::vector<cv::Vec3i>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_Vec3iG_reserve_size_t(std::vector<cv::Vec3i>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_Vec3iG_remove_size_t(std::vector<cv::Vec3i>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_Vec3iG_swap_size_t_size_t(std::vector<cv::Vec3i>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_Vec3iG_clear(std::vector<cv::Vec3i>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_Vec3iG_push_const_Vec3i(std::vector<cv::Vec3i>* instance, const cv::Vec3i* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_Vec3iG_insert_size_t_const_Vec3i(std::vector<cv::Vec3i>* instance, size_t index, const cv::Vec3i* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_Vec3iG_get_const_size_t(const std::vector<cv::Vec3i>* instance, size_t index, cv::Vec3i* ocvrs_return) {
			cv::Vec3i ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLcv_Vec3iG_set_size_t_const_Vec3i(std::vector<cv::Vec3i>* instance, size_t index, const cv::Vec3i* val) {
			(*instance)[index] = *val;
	}
	
	std::vector<cv::Vec3i>* std_vectorLcv_Vec3iG_clone_const(const std::vector<cv::Vec3i>* instance) {
			std::vector<cv::Vec3i> ret = std::vector<cv::Vec3i>(*instance);
			return new std::vector<cv::Vec3i>(ret);
	}
	
	const cv::Vec3i* std_vectorLcv_Vec3iG_data_const(const std::vector<cv::Vec3i>* instance) {
			const cv::Vec3i* ret = instance->data();
			return ret;
	}
	
	cv::Vec3i* std_vectorLcv_Vec3iG_dataMut(std::vector<cv::Vec3i>* instance) {
			cv::Vec3i* ret = instance->data();
			return ret;
	}
	
	std::vector<cv::Vec3i>* cv_fromSlice_const_const_Vec3iX_size_t(const cv::Vec3i* data, size_t len) {
			return new std::vector<cv::Vec3i>(data, data + len);
	}
	
	void std_vectorLcv_Vec3iG_inputArray_const(const std::vector<cv::Vec3i>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Vec3iG_outputArray(std::vector<cv::Vec3i>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Vec3iG_inputOutputArray(std::vector<cv::Vec3i>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<cv::Vec4f>* std_vectorLcv_Vec4fG_new_const() {
			std::vector<cv::Vec4f>* ret = new std::vector<cv::Vec4f>();
			return ret;
	}
	
	void std_vectorLcv_Vec4fG_delete(std::vector<cv::Vec4f>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_Vec4fG_len_const(const std::vector<cv::Vec4f>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_Vec4fG_isEmpty_const(const std::vector<cv::Vec4f>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_Vec4fG_capacity_const(const std::vector<cv::Vec4f>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_Vec4fG_shrinkToFit(std::vector<cv::Vec4f>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_Vec4fG_reserve_size_t(std::vector<cv::Vec4f>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_Vec4fG_remove_size_t(std::vector<cv::Vec4f>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_Vec4fG_swap_size_t_size_t(std::vector<cv::Vec4f>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_Vec4fG_clear(std::vector<cv::Vec4f>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_Vec4fG_push_const_Vec4f(std::vector<cv::Vec4f>* instance, const cv::Vec4f* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_Vec4fG_insert_size_t_const_Vec4f(std::vector<cv::Vec4f>* instance, size_t index, const cv::Vec4f* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_Vec4fG_get_const_size_t(const std::vector<cv::Vec4f>* instance, size_t index, cv::Vec4f* ocvrs_return) {
			cv::Vec4f ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLcv_Vec4fG_set_size_t_const_Vec4f(std::vector<cv::Vec4f>* instance, size_t index, const cv::Vec4f* val) {
			(*instance)[index] = *val;
	}
	
	std::vector<cv::Vec4f>* std_vectorLcv_Vec4fG_clone_const(const std::vector<cv::Vec4f>* instance) {
			std::vector<cv::Vec4f> ret = std::vector<cv::Vec4f>(*instance);
			return new std::vector<cv::Vec4f>(ret);
	}
	
	const cv::Vec4f* std_vectorLcv_Vec4fG_data_const(const std::vector<cv::Vec4f>* instance) {
			const cv::Vec4f* ret = instance->data();
			return ret;
	}
	
	cv::Vec4f* std_vectorLcv_Vec4fG_dataMut(std::vector<cv::Vec4f>* instance) {
			cv::Vec4f* ret = instance->data();
			return ret;
	}
	
	std::vector<cv::Vec4f>* cv_fromSlice_const_const_Vec4fX_size_t(const cv::Vec4f* data, size_t len) {
			return new std::vector<cv::Vec4f>(data, data + len);
	}
	
	void std_vectorLcv_Vec4fG_inputArray_const(const std::vector<cv::Vec4f>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Vec4fG_outputArray(std::vector<cv::Vec4f>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Vec4fG_inputOutputArray(std::vector<cv::Vec4f>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<cv::Vec4i>* std_vectorLcv_Vec4iG_new_const() {
			std::vector<cv::Vec4i>* ret = new std::vector<cv::Vec4i>();
			return ret;
	}
	
	void std_vectorLcv_Vec4iG_delete(std::vector<cv::Vec4i>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_Vec4iG_len_const(const std::vector<cv::Vec4i>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_Vec4iG_isEmpty_const(const std::vector<cv::Vec4i>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_Vec4iG_capacity_const(const std::vector<cv::Vec4i>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_Vec4iG_shrinkToFit(std::vector<cv::Vec4i>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_Vec4iG_reserve_size_t(std::vector<cv::Vec4i>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_Vec4iG_remove_size_t(std::vector<cv::Vec4i>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_Vec4iG_swap_size_t_size_t(std::vector<cv::Vec4i>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_Vec4iG_clear(std::vector<cv::Vec4i>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_Vec4iG_push_const_Vec4i(std::vector<cv::Vec4i>* instance, const cv::Vec4i* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_Vec4iG_insert_size_t_const_Vec4i(std::vector<cv::Vec4i>* instance, size_t index, const cv::Vec4i* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_Vec4iG_get_const_size_t(const std::vector<cv::Vec4i>* instance, size_t index, cv::Vec4i* ocvrs_return) {
			cv::Vec4i ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLcv_Vec4iG_set_size_t_const_Vec4i(std::vector<cv::Vec4i>* instance, size_t index, const cv::Vec4i* val) {
			(*instance)[index] = *val;
	}
	
	std::vector<cv::Vec4i>* std_vectorLcv_Vec4iG_clone_const(const std::vector<cv::Vec4i>* instance) {
			std::vector<cv::Vec4i> ret = std::vector<cv::Vec4i>(*instance);
			return new std::vector<cv::Vec4i>(ret);
	}
	
	const cv::Vec4i* std_vectorLcv_Vec4iG_data_const(const std::vector<cv::Vec4i>* instance) {
			const cv::Vec4i* ret = instance->data();
			return ret;
	}
	
	cv::Vec4i* std_vectorLcv_Vec4iG_dataMut(std::vector<cv::Vec4i>* instance) {
			cv::Vec4i* ret = instance->data();
			return ret;
	}
	
	std::vector<cv::Vec4i>* cv_fromSlice_const_const_Vec4iX_size_t(const cv::Vec4i* data, size_t len) {
			return new std::vector<cv::Vec4i>(data, data + len);
	}
	
	void std_vectorLcv_Vec4iG_inputArray_const(const std::vector<cv::Vec4i>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Vec4iG_outputArray(std::vector<cv::Vec4i>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Vec4iG_inputOutputArray(std::vector<cv::Vec4i>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<cv::Vec6f>* std_vectorLcv_Vec6fG_new_const() {
			std::vector<cv::Vec6f>* ret = new std::vector<cv::Vec6f>();
			return ret;
	}
	
	void std_vectorLcv_Vec6fG_delete(std::vector<cv::Vec6f>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_Vec6fG_len_const(const std::vector<cv::Vec6f>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_Vec6fG_isEmpty_const(const std::vector<cv::Vec6f>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_Vec6fG_capacity_const(const std::vector<cv::Vec6f>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_Vec6fG_shrinkToFit(std::vector<cv::Vec6f>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_Vec6fG_reserve_size_t(std::vector<cv::Vec6f>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_Vec6fG_remove_size_t(std::vector<cv::Vec6f>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_Vec6fG_swap_size_t_size_t(std::vector<cv::Vec6f>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_Vec6fG_clear(std::vector<cv::Vec6f>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_Vec6fG_push_const_Vec6f(std::vector<cv::Vec6f>* instance, const cv::Vec6f* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_Vec6fG_insert_size_t_const_Vec6f(std::vector<cv::Vec6f>* instance, size_t index, const cv::Vec6f* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_Vec6fG_get_const_size_t(const std::vector<cv::Vec6f>* instance, size_t index, cv::Vec6f* ocvrs_return) {
			cv::Vec6f ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLcv_Vec6fG_set_size_t_const_Vec6f(std::vector<cv::Vec6f>* instance, size_t index, const cv::Vec6f* val) {
			(*instance)[index] = *val;
	}
	
	std::vector<cv::Vec6f>* std_vectorLcv_Vec6fG_clone_const(const std::vector<cv::Vec6f>* instance) {
			std::vector<cv::Vec6f> ret = std::vector<cv::Vec6f>(*instance);
			return new std::vector<cv::Vec6f>(ret);
	}
	
	const cv::Vec6f* std_vectorLcv_Vec6fG_data_const(const std::vector<cv::Vec6f>* instance) {
			const cv::Vec6f* ret = instance->data();
			return ret;
	}
	
	cv::Vec6f* std_vectorLcv_Vec6fG_dataMut(std::vector<cv::Vec6f>* instance) {
			cv::Vec6f* ret = instance->data();
			return ret;
	}
	
	std::vector<cv::Vec6f>* cv_fromSlice_const_const_Vec6fX_size_t(const cv::Vec6f* data, size_t len) {
			return new std::vector<cv::Vec6f>(data, data + len);
	}
	
	void std_vectorLcv_Vec6fG_inputArray_const(const std::vector<cv::Vec6f>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Vec6fG_outputArray(std::vector<cv::Vec6f>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_Vec6fG_inputOutputArray(std::vector<cv::Vec6f>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<std::vector<cv::DMatch>>* std_vectorLstd_vectorLcv_DMatchGG_new_const() {
			std::vector<std::vector<cv::DMatch>>* ret = new std::vector<std::vector<cv::DMatch>>();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_DMatchGG_delete(std::vector<std::vector<cv::DMatch>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLstd_vectorLcv_DMatchGG_len_const(const std::vector<std::vector<cv::DMatch>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLstd_vectorLcv_DMatchGG_isEmpty_const(const std::vector<std::vector<cv::DMatch>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLstd_vectorLcv_DMatchGG_capacity_const(const std::vector<std::vector<cv::DMatch>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_DMatchGG_shrinkToFit(std::vector<std::vector<cv::DMatch>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLstd_vectorLcv_DMatchGG_reserve_size_t(std::vector<std::vector<cv::DMatch>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLstd_vectorLcv_DMatchGG_remove_size_t(std::vector<std::vector<cv::DMatch>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLstd_vectorLcv_DMatchGG_swap_size_t_size_t(std::vector<std::vector<cv::DMatch>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLstd_vectorLcv_DMatchGG_clear(std::vector<std::vector<cv::DMatch>>* instance) {
			instance->clear();
	}
	
	void std_vectorLstd_vectorLcv_DMatchGG_push_const_vectorLDMatchG(std::vector<std::vector<cv::DMatch>>* instance, const std::vector<cv::DMatch>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLstd_vectorLcv_DMatchGG_insert_size_t_const_vectorLDMatchG(std::vector<std::vector<cv::DMatch>>* instance, size_t index, const std::vector<cv::DMatch>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLstd_vectorLcv_DMatchGG_get_const_size_t(const std::vector<std::vector<cv::DMatch>>* instance, size_t index, std::vector<cv::DMatch>** ocvrs_return) {
			std::vector<cv::DMatch> ret = (*instance)[index];
			*ocvrs_return = new std::vector<cv::DMatch>(ret);
	}
	
	void std_vectorLstd_vectorLcv_DMatchGG_set_size_t_const_vectorLDMatchG(std::vector<std::vector<cv::DMatch>>* instance, size_t index, const std::vector<cv::DMatch>* val) {
			(*instance)[index] = *val;
	}
	
}


extern "C" {
	std::vector<std::vector<cv::KeyPoint>>* std_vectorLstd_vectorLcv_KeyPointGG_new_const() {
			std::vector<std::vector<cv::KeyPoint>>* ret = new std::vector<std::vector<cv::KeyPoint>>();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_KeyPointGG_delete(std::vector<std::vector<cv::KeyPoint>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLstd_vectorLcv_KeyPointGG_len_const(const std::vector<std::vector<cv::KeyPoint>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLstd_vectorLcv_KeyPointGG_isEmpty_const(const std::vector<std::vector<cv::KeyPoint>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLstd_vectorLcv_KeyPointGG_capacity_const(const std::vector<std::vector<cv::KeyPoint>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_KeyPointGG_shrinkToFit(std::vector<std::vector<cv::KeyPoint>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLstd_vectorLcv_KeyPointGG_reserve_size_t(std::vector<std::vector<cv::KeyPoint>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLstd_vectorLcv_KeyPointGG_remove_size_t(std::vector<std::vector<cv::KeyPoint>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLstd_vectorLcv_KeyPointGG_swap_size_t_size_t(std::vector<std::vector<cv::KeyPoint>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLstd_vectorLcv_KeyPointGG_clear(std::vector<std::vector<cv::KeyPoint>>* instance) {
			instance->clear();
	}
	
	void std_vectorLstd_vectorLcv_KeyPointGG_push_const_vectorLKeyPointG(std::vector<std::vector<cv::KeyPoint>>* instance, const std::vector<cv::KeyPoint>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLstd_vectorLcv_KeyPointGG_insert_size_t_const_vectorLKeyPointG(std::vector<std::vector<cv::KeyPoint>>* instance, size_t index, const std::vector<cv::KeyPoint>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLstd_vectorLcv_KeyPointGG_get_const_size_t(const std::vector<std::vector<cv::KeyPoint>>* instance, size_t index, std::vector<cv::KeyPoint>** ocvrs_return) {
			std::vector<cv::KeyPoint> ret = (*instance)[index];
			*ocvrs_return = new std::vector<cv::KeyPoint>(ret);
	}
	
	void std_vectorLstd_vectorLcv_KeyPointGG_set_size_t_const_vectorLKeyPointG(std::vector<std::vector<cv::KeyPoint>>* instance, size_t index, const std::vector<cv::KeyPoint>* val) {
			(*instance)[index] = *val;
	}
	
}


extern "C" {
	std::vector<std::vector<cv::Mat>>* std_vectorLstd_vectorLcv_MatGG_new_const() {
			std::vector<std::vector<cv::Mat>>* ret = new std::vector<std::vector<cv::Mat>>();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_MatGG_delete(std::vector<std::vector<cv::Mat>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLstd_vectorLcv_MatGG_len_const(const std::vector<std::vector<cv::Mat>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLstd_vectorLcv_MatGG_isEmpty_const(const std::vector<std::vector<cv::Mat>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLstd_vectorLcv_MatGG_capacity_const(const std::vector<std::vector<cv::Mat>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_MatGG_shrinkToFit(std::vector<std::vector<cv::Mat>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLstd_vectorLcv_MatGG_reserve_size_t(std::vector<std::vector<cv::Mat>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLstd_vectorLcv_MatGG_remove_size_t(std::vector<std::vector<cv::Mat>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLstd_vectorLcv_MatGG_swap_size_t_size_t(std::vector<std::vector<cv::Mat>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLstd_vectorLcv_MatGG_clear(std::vector<std::vector<cv::Mat>>* instance) {
			instance->clear();
	}
	
	void std_vectorLstd_vectorLcv_MatGG_push_const_vectorLMatG(std::vector<std::vector<cv::Mat>>* instance, const std::vector<cv::Mat>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLstd_vectorLcv_MatGG_insert_size_t_const_vectorLMatG(std::vector<std::vector<cv::Mat>>* instance, size_t index, const std::vector<cv::Mat>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLstd_vectorLcv_MatGG_get_const_size_t(const std::vector<std::vector<cv::Mat>>* instance, size_t index, std::vector<cv::Mat>** ocvrs_return) {
			std::vector<cv::Mat> ret = (*instance)[index];
			*ocvrs_return = new std::vector<cv::Mat>(ret);
	}
	
	void std_vectorLstd_vectorLcv_MatGG_set_size_t_const_vectorLMatG(std::vector<std::vector<cv::Mat>>* instance, size_t index, const std::vector<cv::Mat>* val) {
			(*instance)[index] = *val;
	}
	
}


extern "C" {
	std::vector<std::vector<cv::Point>>* std_vectorLstd_vectorLcv_PointGG_new_const() {
			std::vector<std::vector<cv::Point>>* ret = new std::vector<std::vector<cv::Point>>();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_PointGG_delete(std::vector<std::vector<cv::Point>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLstd_vectorLcv_PointGG_len_const(const std::vector<std::vector<cv::Point>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLstd_vectorLcv_PointGG_isEmpty_const(const std::vector<std::vector<cv::Point>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLstd_vectorLcv_PointGG_capacity_const(const std::vector<std::vector<cv::Point>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_PointGG_shrinkToFit(std::vector<std::vector<cv::Point>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLstd_vectorLcv_PointGG_reserve_size_t(std::vector<std::vector<cv::Point>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLstd_vectorLcv_PointGG_remove_size_t(std::vector<std::vector<cv::Point>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLstd_vectorLcv_PointGG_swap_size_t_size_t(std::vector<std::vector<cv::Point>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLstd_vectorLcv_PointGG_clear(std::vector<std::vector<cv::Point>>* instance) {
			instance->clear();
	}
	
	void std_vectorLstd_vectorLcv_PointGG_push_const_vectorLPointG(std::vector<std::vector<cv::Point>>* instance, const std::vector<cv::Point>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLstd_vectorLcv_PointGG_insert_size_t_const_vectorLPointG(std::vector<std::vector<cv::Point>>* instance, size_t index, const std::vector<cv::Point>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLstd_vectorLcv_PointGG_get_const_size_t(const std::vector<std::vector<cv::Point>>* instance, size_t index, std::vector<cv::Point>** ocvrs_return) {
			std::vector<cv::Point> ret = (*instance)[index];
			*ocvrs_return = new std::vector<cv::Point>(ret);
	}
	
	void std_vectorLstd_vectorLcv_PointGG_set_size_t_const_vectorLPointG(std::vector<std::vector<cv::Point>>* instance, size_t index, const std::vector<cv::Point>* val) {
			(*instance)[index] = *val;
	}
	
	void std_vectorLstd_vectorLcv_PointGG_inputArray_const(const std::vector<std::vector<cv::Point>>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLcv_PointGG_outputArray(std::vector<std::vector<cv::Point>>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLcv_PointGG_inputOutputArray(std::vector<std::vector<cv::Point>>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<std::vector<cv::Point2d>>* std_vectorLstd_vectorLcv_Point2dGG_new_const() {
			std::vector<std::vector<cv::Point2d>>* ret = new std::vector<std::vector<cv::Point2d>>();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_Point2dGG_delete(std::vector<std::vector<cv::Point2d>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLstd_vectorLcv_Point2dGG_len_const(const std::vector<std::vector<cv::Point2d>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLstd_vectorLcv_Point2dGG_isEmpty_const(const std::vector<std::vector<cv::Point2d>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLstd_vectorLcv_Point2dGG_capacity_const(const std::vector<std::vector<cv::Point2d>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_Point2dGG_shrinkToFit(std::vector<std::vector<cv::Point2d>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLstd_vectorLcv_Point2dGG_reserve_size_t(std::vector<std::vector<cv::Point2d>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLstd_vectorLcv_Point2dGG_remove_size_t(std::vector<std::vector<cv::Point2d>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLstd_vectorLcv_Point2dGG_swap_size_t_size_t(std::vector<std::vector<cv::Point2d>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLstd_vectorLcv_Point2dGG_clear(std::vector<std::vector<cv::Point2d>>* instance) {
			instance->clear();
	}
	
	void std_vectorLstd_vectorLcv_Point2dGG_push_const_vectorLPoint2dG(std::vector<std::vector<cv::Point2d>>* instance, const std::vector<cv::Point2d>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLstd_vectorLcv_Point2dGG_insert_size_t_const_vectorLPoint2dG(std::vector<std::vector<cv::Point2d>>* instance, size_t index, const std::vector<cv::Point2d>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLstd_vectorLcv_Point2dGG_get_const_size_t(const std::vector<std::vector<cv::Point2d>>* instance, size_t index, std::vector<cv::Point2d>** ocvrs_return) {
			std::vector<cv::Point2d> ret = (*instance)[index];
			*ocvrs_return = new std::vector<cv::Point2d>(ret);
	}
	
	void std_vectorLstd_vectorLcv_Point2dGG_set_size_t_const_vectorLPoint2dG(std::vector<std::vector<cv::Point2d>>* instance, size_t index, const std::vector<cv::Point2d>* val) {
			(*instance)[index] = *val;
	}
	
	void std_vectorLstd_vectorLcv_Point2dGG_inputArray_const(const std::vector<std::vector<cv::Point2d>>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLcv_Point2dGG_outputArray(std::vector<std::vector<cv::Point2d>>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLcv_Point2dGG_inputOutputArray(std::vector<std::vector<cv::Point2d>>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<std::vector<cv::Point2f>>* std_vectorLstd_vectorLcv_Point2fGG_new_const() {
			std::vector<std::vector<cv::Point2f>>* ret = new std::vector<std::vector<cv::Point2f>>();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_Point2fGG_delete(std::vector<std::vector<cv::Point2f>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLstd_vectorLcv_Point2fGG_len_const(const std::vector<std::vector<cv::Point2f>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLstd_vectorLcv_Point2fGG_isEmpty_const(const std::vector<std::vector<cv::Point2f>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLstd_vectorLcv_Point2fGG_capacity_const(const std::vector<std::vector<cv::Point2f>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_Point2fGG_shrinkToFit(std::vector<std::vector<cv::Point2f>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLstd_vectorLcv_Point2fGG_reserve_size_t(std::vector<std::vector<cv::Point2f>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLstd_vectorLcv_Point2fGG_remove_size_t(std::vector<std::vector<cv::Point2f>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLstd_vectorLcv_Point2fGG_swap_size_t_size_t(std::vector<std::vector<cv::Point2f>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLstd_vectorLcv_Point2fGG_clear(std::vector<std::vector<cv::Point2f>>* instance) {
			instance->clear();
	}
	
	void std_vectorLstd_vectorLcv_Point2fGG_push_const_vectorLPoint2fG(std::vector<std::vector<cv::Point2f>>* instance, const std::vector<cv::Point2f>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLstd_vectorLcv_Point2fGG_insert_size_t_const_vectorLPoint2fG(std::vector<std::vector<cv::Point2f>>* instance, size_t index, const std::vector<cv::Point2f>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLstd_vectorLcv_Point2fGG_get_const_size_t(const std::vector<std::vector<cv::Point2f>>* instance, size_t index, std::vector<cv::Point2f>** ocvrs_return) {
			std::vector<cv::Point2f> ret = (*instance)[index];
			*ocvrs_return = new std::vector<cv::Point2f>(ret);
	}
	
	void std_vectorLstd_vectorLcv_Point2fGG_set_size_t_const_vectorLPoint2fG(std::vector<std::vector<cv::Point2f>>* instance, size_t index, const std::vector<cv::Point2f>* val) {
			(*instance)[index] = *val;
	}
	
	void std_vectorLstd_vectorLcv_Point2fGG_inputArray_const(const std::vector<std::vector<cv::Point2f>>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLcv_Point2fGG_outputArray(std::vector<std::vector<cv::Point2f>>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLcv_Point2fGG_inputOutputArray(std::vector<std::vector<cv::Point2f>>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<std::vector<cv::Point3d>>* std_vectorLstd_vectorLcv_Point3dGG_new_const() {
			std::vector<std::vector<cv::Point3d>>* ret = new std::vector<std::vector<cv::Point3d>>();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_Point3dGG_delete(std::vector<std::vector<cv::Point3d>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLstd_vectorLcv_Point3dGG_len_const(const std::vector<std::vector<cv::Point3d>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLstd_vectorLcv_Point3dGG_isEmpty_const(const std::vector<std::vector<cv::Point3d>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLstd_vectorLcv_Point3dGG_capacity_const(const std::vector<std::vector<cv::Point3d>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_Point3dGG_shrinkToFit(std::vector<std::vector<cv::Point3d>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLstd_vectorLcv_Point3dGG_reserve_size_t(std::vector<std::vector<cv::Point3d>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLstd_vectorLcv_Point3dGG_remove_size_t(std::vector<std::vector<cv::Point3d>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLstd_vectorLcv_Point3dGG_swap_size_t_size_t(std::vector<std::vector<cv::Point3d>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLstd_vectorLcv_Point3dGG_clear(std::vector<std::vector<cv::Point3d>>* instance) {
			instance->clear();
	}
	
	void std_vectorLstd_vectorLcv_Point3dGG_push_const_vectorLPoint3dG(std::vector<std::vector<cv::Point3d>>* instance, const std::vector<cv::Point3d>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLstd_vectorLcv_Point3dGG_insert_size_t_const_vectorLPoint3dG(std::vector<std::vector<cv::Point3d>>* instance, size_t index, const std::vector<cv::Point3d>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLstd_vectorLcv_Point3dGG_get_const_size_t(const std::vector<std::vector<cv::Point3d>>* instance, size_t index, std::vector<cv::Point3d>** ocvrs_return) {
			std::vector<cv::Point3d> ret = (*instance)[index];
			*ocvrs_return = new std::vector<cv::Point3d>(ret);
	}
	
	void std_vectorLstd_vectorLcv_Point3dGG_set_size_t_const_vectorLPoint3dG(std::vector<std::vector<cv::Point3d>>* instance, size_t index, const std::vector<cv::Point3d>* val) {
			(*instance)[index] = *val;
	}
	
	void std_vectorLstd_vectorLcv_Point3dGG_inputArray_const(const std::vector<std::vector<cv::Point3d>>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLcv_Point3dGG_outputArray(std::vector<std::vector<cv::Point3d>>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLcv_Point3dGG_inputOutputArray(std::vector<std::vector<cv::Point3d>>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<std::vector<cv::Point3f>>* std_vectorLstd_vectorLcv_Point3fGG_new_const() {
			std::vector<std::vector<cv::Point3f>>* ret = new std::vector<std::vector<cv::Point3f>>();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_Point3fGG_delete(std::vector<std::vector<cv::Point3f>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLstd_vectorLcv_Point3fGG_len_const(const std::vector<std::vector<cv::Point3f>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLstd_vectorLcv_Point3fGG_isEmpty_const(const std::vector<std::vector<cv::Point3f>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLstd_vectorLcv_Point3fGG_capacity_const(const std::vector<std::vector<cv::Point3f>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_Point3fGG_shrinkToFit(std::vector<std::vector<cv::Point3f>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLstd_vectorLcv_Point3fGG_reserve_size_t(std::vector<std::vector<cv::Point3f>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLstd_vectorLcv_Point3fGG_remove_size_t(std::vector<std::vector<cv::Point3f>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLstd_vectorLcv_Point3fGG_swap_size_t_size_t(std::vector<std::vector<cv::Point3f>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLstd_vectorLcv_Point3fGG_clear(std::vector<std::vector<cv::Point3f>>* instance) {
			instance->clear();
	}
	
	void std_vectorLstd_vectorLcv_Point3fGG_push_const_vectorLPoint3fG(std::vector<std::vector<cv::Point3f>>* instance, const std::vector<cv::Point3f>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLstd_vectorLcv_Point3fGG_insert_size_t_const_vectorLPoint3fG(std::vector<std::vector<cv::Point3f>>* instance, size_t index, const std::vector<cv::Point3f>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLstd_vectorLcv_Point3fGG_get_const_size_t(const std::vector<std::vector<cv::Point3f>>* instance, size_t index, std::vector<cv::Point3f>** ocvrs_return) {
			std::vector<cv::Point3f> ret = (*instance)[index];
			*ocvrs_return = new std::vector<cv::Point3f>(ret);
	}
	
	void std_vectorLstd_vectorLcv_Point3fGG_set_size_t_const_vectorLPoint3fG(std::vector<std::vector<cv::Point3f>>* instance, size_t index, const std::vector<cv::Point3f>* val) {
			(*instance)[index] = *val;
	}
	
	void std_vectorLstd_vectorLcv_Point3fGG_inputArray_const(const std::vector<std::vector<cv::Point3f>>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLcv_Point3fGG_outputArray(std::vector<std::vector<cv::Point3f>>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLcv_Point3fGG_inputOutputArray(std::vector<std::vector<cv::Point3f>>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<std::vector<cv::Point3i>>* std_vectorLstd_vectorLcv_Point3iGG_new_const() {
			std::vector<std::vector<cv::Point3i>>* ret = new std::vector<std::vector<cv::Point3i>>();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_Point3iGG_delete(std::vector<std::vector<cv::Point3i>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLstd_vectorLcv_Point3iGG_len_const(const std::vector<std::vector<cv::Point3i>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLstd_vectorLcv_Point3iGG_isEmpty_const(const std::vector<std::vector<cv::Point3i>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLstd_vectorLcv_Point3iGG_capacity_const(const std::vector<std::vector<cv::Point3i>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_Point3iGG_shrinkToFit(std::vector<std::vector<cv::Point3i>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLstd_vectorLcv_Point3iGG_reserve_size_t(std::vector<std::vector<cv::Point3i>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLstd_vectorLcv_Point3iGG_remove_size_t(std::vector<std::vector<cv::Point3i>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLstd_vectorLcv_Point3iGG_swap_size_t_size_t(std::vector<std::vector<cv::Point3i>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLstd_vectorLcv_Point3iGG_clear(std::vector<std::vector<cv::Point3i>>* instance) {
			instance->clear();
	}
	
	void std_vectorLstd_vectorLcv_Point3iGG_push_const_vectorLPoint3iG(std::vector<std::vector<cv::Point3i>>* instance, const std::vector<cv::Point3i>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLstd_vectorLcv_Point3iGG_insert_size_t_const_vectorLPoint3iG(std::vector<std::vector<cv::Point3i>>* instance, size_t index, const std::vector<cv::Point3i>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLstd_vectorLcv_Point3iGG_get_const_size_t(const std::vector<std::vector<cv::Point3i>>* instance, size_t index, std::vector<cv::Point3i>** ocvrs_return) {
			std::vector<cv::Point3i> ret = (*instance)[index];
			*ocvrs_return = new std::vector<cv::Point3i>(ret);
	}
	
	void std_vectorLstd_vectorLcv_Point3iGG_set_size_t_const_vectorLPoint3iG(std::vector<std::vector<cv::Point3i>>* instance, size_t index, const std::vector<cv::Point3i>* val) {
			(*instance)[index] = *val;
	}
	
	void std_vectorLstd_vectorLcv_Point3iGG_inputArray_const(const std::vector<std::vector<cv::Point3i>>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLcv_Point3iGG_outputArray(std::vector<std::vector<cv::Point3i>>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLcv_Point3iGG_inputOutputArray(std::vector<std::vector<cv::Point3i>>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<std::vector<cv::Range>>* std_vectorLstd_vectorLcv_RangeGG_new_const() {
			std::vector<std::vector<cv::Range>>* ret = new std::vector<std::vector<cv::Range>>();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_RangeGG_delete(std::vector<std::vector<cv::Range>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLstd_vectorLcv_RangeGG_len_const(const std::vector<std::vector<cv::Range>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLstd_vectorLcv_RangeGG_isEmpty_const(const std::vector<std::vector<cv::Range>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLstd_vectorLcv_RangeGG_capacity_const(const std::vector<std::vector<cv::Range>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_RangeGG_shrinkToFit(std::vector<std::vector<cv::Range>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLstd_vectorLcv_RangeGG_reserve_size_t(std::vector<std::vector<cv::Range>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLstd_vectorLcv_RangeGG_remove_size_t(std::vector<std::vector<cv::Range>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLstd_vectorLcv_RangeGG_swap_size_t_size_t(std::vector<std::vector<cv::Range>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLstd_vectorLcv_RangeGG_clear(std::vector<std::vector<cv::Range>>* instance) {
			instance->clear();
	}
	
	void std_vectorLstd_vectorLcv_RangeGG_push_const_vectorLRangeG(std::vector<std::vector<cv::Range>>* instance, const std::vector<cv::Range>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLstd_vectorLcv_RangeGG_insert_size_t_const_vectorLRangeG(std::vector<std::vector<cv::Range>>* instance, size_t index, const std::vector<cv::Range>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLstd_vectorLcv_RangeGG_get_const_size_t(const std::vector<std::vector<cv::Range>>* instance, size_t index, std::vector<cv::Range>** ocvrs_return) {
			std::vector<cv::Range> ret = (*instance)[index];
			*ocvrs_return = new std::vector<cv::Range>(ret);
	}
	
	void std_vectorLstd_vectorLcv_RangeGG_set_size_t_const_vectorLRangeG(std::vector<std::vector<cv::Range>>* instance, size_t index, const std::vector<cv::Range>* val) {
			(*instance)[index] = *val;
	}
	
}


extern "C" {
	std::vector<std::vector<cv::Vec2d>>* std_vectorLstd_vectorLcv_Vec2dGG_new_const() {
			std::vector<std::vector<cv::Vec2d>>* ret = new std::vector<std::vector<cv::Vec2d>>();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_Vec2dGG_delete(std::vector<std::vector<cv::Vec2d>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLstd_vectorLcv_Vec2dGG_len_const(const std::vector<std::vector<cv::Vec2d>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLstd_vectorLcv_Vec2dGG_isEmpty_const(const std::vector<std::vector<cv::Vec2d>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLstd_vectorLcv_Vec2dGG_capacity_const(const std::vector<std::vector<cv::Vec2d>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_Vec2dGG_shrinkToFit(std::vector<std::vector<cv::Vec2d>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLstd_vectorLcv_Vec2dGG_reserve_size_t(std::vector<std::vector<cv::Vec2d>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLstd_vectorLcv_Vec2dGG_remove_size_t(std::vector<std::vector<cv::Vec2d>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLstd_vectorLcv_Vec2dGG_swap_size_t_size_t(std::vector<std::vector<cv::Vec2d>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLstd_vectorLcv_Vec2dGG_clear(std::vector<std::vector<cv::Vec2d>>* instance) {
			instance->clear();
	}
	
	void std_vectorLstd_vectorLcv_Vec2dGG_push_const_vectorLVec2dG(std::vector<std::vector<cv::Vec2d>>* instance, const std::vector<cv::Vec2d>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLstd_vectorLcv_Vec2dGG_insert_size_t_const_vectorLVec2dG(std::vector<std::vector<cv::Vec2d>>* instance, size_t index, const std::vector<cv::Vec2d>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLstd_vectorLcv_Vec2dGG_get_const_size_t(const std::vector<std::vector<cv::Vec2d>>* instance, size_t index, std::vector<cv::Vec2d>** ocvrs_return) {
			std::vector<cv::Vec2d> ret = (*instance)[index];
			*ocvrs_return = new std::vector<cv::Vec2d>(ret);
	}
	
	void std_vectorLstd_vectorLcv_Vec2dGG_set_size_t_const_vectorLVec2dG(std::vector<std::vector<cv::Vec2d>>* instance, size_t index, const std::vector<cv::Vec2d>* val) {
			(*instance)[index] = *val;
	}
	
	void std_vectorLstd_vectorLcv_Vec2dGG_inputArray_const(const std::vector<std::vector<cv::Vec2d>>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLcv_Vec2dGG_outputArray(std::vector<std::vector<cv::Vec2d>>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLcv_Vec2dGG_inputOutputArray(std::vector<std::vector<cv::Vec2d>>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<std::vector<cv::Vec2f>>* std_vectorLstd_vectorLcv_Vec2fGG_new_const() {
			std::vector<std::vector<cv::Vec2f>>* ret = new std::vector<std::vector<cv::Vec2f>>();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_Vec2fGG_delete(std::vector<std::vector<cv::Vec2f>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLstd_vectorLcv_Vec2fGG_len_const(const std::vector<std::vector<cv::Vec2f>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLstd_vectorLcv_Vec2fGG_isEmpty_const(const std::vector<std::vector<cv::Vec2f>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLstd_vectorLcv_Vec2fGG_capacity_const(const std::vector<std::vector<cv::Vec2f>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_Vec2fGG_shrinkToFit(std::vector<std::vector<cv::Vec2f>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLstd_vectorLcv_Vec2fGG_reserve_size_t(std::vector<std::vector<cv::Vec2f>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLstd_vectorLcv_Vec2fGG_remove_size_t(std::vector<std::vector<cv::Vec2f>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLstd_vectorLcv_Vec2fGG_swap_size_t_size_t(std::vector<std::vector<cv::Vec2f>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLstd_vectorLcv_Vec2fGG_clear(std::vector<std::vector<cv::Vec2f>>* instance) {
			instance->clear();
	}
	
	void std_vectorLstd_vectorLcv_Vec2fGG_push_const_vectorLVec2fG(std::vector<std::vector<cv::Vec2f>>* instance, const std::vector<cv::Vec2f>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLstd_vectorLcv_Vec2fGG_insert_size_t_const_vectorLVec2fG(std::vector<std::vector<cv::Vec2f>>* instance, size_t index, const std::vector<cv::Vec2f>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLstd_vectorLcv_Vec2fGG_get_const_size_t(const std::vector<std::vector<cv::Vec2f>>* instance, size_t index, std::vector<cv::Vec2f>** ocvrs_return) {
			std::vector<cv::Vec2f> ret = (*instance)[index];
			*ocvrs_return = new std::vector<cv::Vec2f>(ret);
	}
	
	void std_vectorLstd_vectorLcv_Vec2fGG_set_size_t_const_vectorLVec2fG(std::vector<std::vector<cv::Vec2f>>* instance, size_t index, const std::vector<cv::Vec2f>* val) {
			(*instance)[index] = *val;
	}
	
	void std_vectorLstd_vectorLcv_Vec2fGG_inputArray_const(const std::vector<std::vector<cv::Vec2f>>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLcv_Vec2fGG_outputArray(std::vector<std::vector<cv::Vec2f>>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLcv_Vec2fGG_inputOutputArray(std::vector<std::vector<cv::Vec2f>>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<std::vector<cv::Vec2i>>* std_vectorLstd_vectorLcv_Vec2iGG_new_const() {
			std::vector<std::vector<cv::Vec2i>>* ret = new std::vector<std::vector<cv::Vec2i>>();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_Vec2iGG_delete(std::vector<std::vector<cv::Vec2i>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLstd_vectorLcv_Vec2iGG_len_const(const std::vector<std::vector<cv::Vec2i>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLstd_vectorLcv_Vec2iGG_isEmpty_const(const std::vector<std::vector<cv::Vec2i>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLstd_vectorLcv_Vec2iGG_capacity_const(const std::vector<std::vector<cv::Vec2i>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_Vec2iGG_shrinkToFit(std::vector<std::vector<cv::Vec2i>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLstd_vectorLcv_Vec2iGG_reserve_size_t(std::vector<std::vector<cv::Vec2i>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLstd_vectorLcv_Vec2iGG_remove_size_t(std::vector<std::vector<cv::Vec2i>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLstd_vectorLcv_Vec2iGG_swap_size_t_size_t(std::vector<std::vector<cv::Vec2i>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLstd_vectorLcv_Vec2iGG_clear(std::vector<std::vector<cv::Vec2i>>* instance) {
			instance->clear();
	}
	
	void std_vectorLstd_vectorLcv_Vec2iGG_push_const_vectorLVec2iG(std::vector<std::vector<cv::Vec2i>>* instance, const std::vector<cv::Vec2i>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLstd_vectorLcv_Vec2iGG_insert_size_t_const_vectorLVec2iG(std::vector<std::vector<cv::Vec2i>>* instance, size_t index, const std::vector<cv::Vec2i>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLstd_vectorLcv_Vec2iGG_get_const_size_t(const std::vector<std::vector<cv::Vec2i>>* instance, size_t index, std::vector<cv::Vec2i>** ocvrs_return) {
			std::vector<cv::Vec2i> ret = (*instance)[index];
			*ocvrs_return = new std::vector<cv::Vec2i>(ret);
	}
	
	void std_vectorLstd_vectorLcv_Vec2iGG_set_size_t_const_vectorLVec2iG(std::vector<std::vector<cv::Vec2i>>* instance, size_t index, const std::vector<cv::Vec2i>* val) {
			(*instance)[index] = *val;
	}
	
	void std_vectorLstd_vectorLcv_Vec2iGG_inputArray_const(const std::vector<std::vector<cv::Vec2i>>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLcv_Vec2iGG_outputArray(std::vector<std::vector<cv::Vec2i>>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLcv_Vec2iGG_inputOutputArray(std::vector<std::vector<cv::Vec2i>>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<std::vector<cv::Vec3d>>* std_vectorLstd_vectorLcv_Vec3dGG_new_const() {
			std::vector<std::vector<cv::Vec3d>>* ret = new std::vector<std::vector<cv::Vec3d>>();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_Vec3dGG_delete(std::vector<std::vector<cv::Vec3d>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLstd_vectorLcv_Vec3dGG_len_const(const std::vector<std::vector<cv::Vec3d>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLstd_vectorLcv_Vec3dGG_isEmpty_const(const std::vector<std::vector<cv::Vec3d>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLstd_vectorLcv_Vec3dGG_capacity_const(const std::vector<std::vector<cv::Vec3d>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_Vec3dGG_shrinkToFit(std::vector<std::vector<cv::Vec3d>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLstd_vectorLcv_Vec3dGG_reserve_size_t(std::vector<std::vector<cv::Vec3d>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLstd_vectorLcv_Vec3dGG_remove_size_t(std::vector<std::vector<cv::Vec3d>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLstd_vectorLcv_Vec3dGG_swap_size_t_size_t(std::vector<std::vector<cv::Vec3d>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLstd_vectorLcv_Vec3dGG_clear(std::vector<std::vector<cv::Vec3d>>* instance) {
			instance->clear();
	}
	
	void std_vectorLstd_vectorLcv_Vec3dGG_push_const_vectorLVec3dG(std::vector<std::vector<cv::Vec3d>>* instance, const std::vector<cv::Vec3d>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLstd_vectorLcv_Vec3dGG_insert_size_t_const_vectorLVec3dG(std::vector<std::vector<cv::Vec3d>>* instance, size_t index, const std::vector<cv::Vec3d>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLstd_vectorLcv_Vec3dGG_get_const_size_t(const std::vector<std::vector<cv::Vec3d>>* instance, size_t index, std::vector<cv::Vec3d>** ocvrs_return) {
			std::vector<cv::Vec3d> ret = (*instance)[index];
			*ocvrs_return = new std::vector<cv::Vec3d>(ret);
	}
	
	void std_vectorLstd_vectorLcv_Vec3dGG_set_size_t_const_vectorLVec3dG(std::vector<std::vector<cv::Vec3d>>* instance, size_t index, const std::vector<cv::Vec3d>* val) {
			(*instance)[index] = *val;
	}
	
	void std_vectorLstd_vectorLcv_Vec3dGG_inputArray_const(const std::vector<std::vector<cv::Vec3d>>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLcv_Vec3dGG_outputArray(std::vector<std::vector<cv::Vec3d>>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLcv_Vec3dGG_inputOutputArray(std::vector<std::vector<cv::Vec3d>>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<std::vector<cv::Vec3f>>* std_vectorLstd_vectorLcv_Vec3fGG_new_const() {
			std::vector<std::vector<cv::Vec3f>>* ret = new std::vector<std::vector<cv::Vec3f>>();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_Vec3fGG_delete(std::vector<std::vector<cv::Vec3f>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLstd_vectorLcv_Vec3fGG_len_const(const std::vector<std::vector<cv::Vec3f>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLstd_vectorLcv_Vec3fGG_isEmpty_const(const std::vector<std::vector<cv::Vec3f>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLstd_vectorLcv_Vec3fGG_capacity_const(const std::vector<std::vector<cv::Vec3f>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_Vec3fGG_shrinkToFit(std::vector<std::vector<cv::Vec3f>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLstd_vectorLcv_Vec3fGG_reserve_size_t(std::vector<std::vector<cv::Vec3f>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLstd_vectorLcv_Vec3fGG_remove_size_t(std::vector<std::vector<cv::Vec3f>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLstd_vectorLcv_Vec3fGG_swap_size_t_size_t(std::vector<std::vector<cv::Vec3f>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLstd_vectorLcv_Vec3fGG_clear(std::vector<std::vector<cv::Vec3f>>* instance) {
			instance->clear();
	}
	
	void std_vectorLstd_vectorLcv_Vec3fGG_push_const_vectorLVec3fG(std::vector<std::vector<cv::Vec3f>>* instance, const std::vector<cv::Vec3f>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLstd_vectorLcv_Vec3fGG_insert_size_t_const_vectorLVec3fG(std::vector<std::vector<cv::Vec3f>>* instance, size_t index, const std::vector<cv::Vec3f>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLstd_vectorLcv_Vec3fGG_get_const_size_t(const std::vector<std::vector<cv::Vec3f>>* instance, size_t index, std::vector<cv::Vec3f>** ocvrs_return) {
			std::vector<cv::Vec3f> ret = (*instance)[index];
			*ocvrs_return = new std::vector<cv::Vec3f>(ret);
	}
	
	void std_vectorLstd_vectorLcv_Vec3fGG_set_size_t_const_vectorLVec3fG(std::vector<std::vector<cv::Vec3f>>* instance, size_t index, const std::vector<cv::Vec3f>* val) {
			(*instance)[index] = *val;
	}
	
	void std_vectorLstd_vectorLcv_Vec3fGG_inputArray_const(const std::vector<std::vector<cv::Vec3f>>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLcv_Vec3fGG_outputArray(std::vector<std::vector<cv::Vec3f>>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLcv_Vec3fGG_inputOutputArray(std::vector<std::vector<cv::Vec3f>>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
}


extern "C" {
	std::vector<std::vector<float>>* std_vectorLstd_vectorLfloatGG_new_const() {
			std::vector<std::vector<float>>* ret = new std::vector<std::vector<float>>();
			return ret;
	}
	
	void std_vectorLstd_vectorLfloatGG_delete(std::vector<std::vector<float>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLstd_vectorLfloatGG_len_const(const std::vector<std::vector<float>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLstd_vectorLfloatGG_isEmpty_const(const std::vector<std::vector<float>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLstd_vectorLfloatGG_capacity_const(const std::vector<std::vector<float>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLstd_vectorLfloatGG_shrinkToFit(std::vector<std::vector<float>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLstd_vectorLfloatGG_reserve_size_t(std::vector<std::vector<float>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLstd_vectorLfloatGG_remove_size_t(std::vector<std::vector<float>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLstd_vectorLfloatGG_swap_size_t_size_t(std::vector<std::vector<float>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLstd_vectorLfloatGG_clear(std::vector<std::vector<float>>* instance) {
			instance->clear();
	}
	
	void std_vectorLstd_vectorLfloatGG_push_const_vectorLfloatG(std::vector<std::vector<float>>* instance, const std::vector<float>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLstd_vectorLfloatGG_insert_size_t_const_vectorLfloatG(std::vector<std::vector<float>>* instance, size_t index, const std::vector<float>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLstd_vectorLfloatGG_get_const_size_t(const std::vector<std::vector<float>>* instance, size_t index, std::vector<float>** ocvrs_return) {
			std::vector<float> ret = (*instance)[index];
			*ocvrs_return = new std::vector<float>(ret);
	}
	
	void std_vectorLstd_vectorLfloatGG_set_size_t_const_vectorLfloatG(std::vector<std::vector<float>>* instance, size_t index, const std::vector<float>* val) {
			(*instance)[index] = *val;
	}
	
	void std_vectorLstd_vectorLfloatGG_inputArray_const(const std::vector<std::vector<float>>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLfloatGG_outputArray(std::vector<std::vector<float>>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLfloatGG_inputOutputArray(std::vector<std::vector<float>>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<std::vector<double>>* std_vectorLstd_vectorLdoubleGG_new_const() {
			std::vector<std::vector<double>>* ret = new std::vector<std::vector<double>>();
			return ret;
	}
	
	void std_vectorLstd_vectorLdoubleGG_delete(std::vector<std::vector<double>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLstd_vectorLdoubleGG_len_const(const std::vector<std::vector<double>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLstd_vectorLdoubleGG_isEmpty_const(const std::vector<std::vector<double>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLstd_vectorLdoubleGG_capacity_const(const std::vector<std::vector<double>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLstd_vectorLdoubleGG_shrinkToFit(std::vector<std::vector<double>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLstd_vectorLdoubleGG_reserve_size_t(std::vector<std::vector<double>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLstd_vectorLdoubleGG_remove_size_t(std::vector<std::vector<double>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLstd_vectorLdoubleGG_swap_size_t_size_t(std::vector<std::vector<double>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLstd_vectorLdoubleGG_clear(std::vector<std::vector<double>>* instance) {
			instance->clear();
	}
	
	void std_vectorLstd_vectorLdoubleGG_push_const_vectorLdoubleG(std::vector<std::vector<double>>* instance, const std::vector<double>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLstd_vectorLdoubleGG_insert_size_t_const_vectorLdoubleG(std::vector<std::vector<double>>* instance, size_t index, const std::vector<double>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLstd_vectorLdoubleGG_get_const_size_t(const std::vector<std::vector<double>>* instance, size_t index, std::vector<double>** ocvrs_return) {
			std::vector<double> ret = (*instance)[index];
			*ocvrs_return = new std::vector<double>(ret);
	}
	
	void std_vectorLstd_vectorLdoubleGG_set_size_t_const_vectorLdoubleG(std::vector<std::vector<double>>* instance, size_t index, const std::vector<double>* val) {
			(*instance)[index] = *val;
	}
	
	void std_vectorLstd_vectorLdoubleGG_inputArray_const(const std::vector<std::vector<double>>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLdoubleGG_outputArray(std::vector<std::vector<double>>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLdoubleGG_inputOutputArray(std::vector<std::vector<double>>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<std::vector<int>>* std_vectorLstd_vectorLintGG_new_const() {
			std::vector<std::vector<int>>* ret = new std::vector<std::vector<int>>();
			return ret;
	}
	
	void std_vectorLstd_vectorLintGG_delete(std::vector<std::vector<int>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLstd_vectorLintGG_len_const(const std::vector<std::vector<int>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLstd_vectorLintGG_isEmpty_const(const std::vector<std::vector<int>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLstd_vectorLintGG_capacity_const(const std::vector<std::vector<int>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLstd_vectorLintGG_shrinkToFit(std::vector<std::vector<int>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLstd_vectorLintGG_reserve_size_t(std::vector<std::vector<int>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLstd_vectorLintGG_remove_size_t(std::vector<std::vector<int>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLstd_vectorLintGG_swap_size_t_size_t(std::vector<std::vector<int>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLstd_vectorLintGG_clear(std::vector<std::vector<int>>* instance) {
			instance->clear();
	}
	
	void std_vectorLstd_vectorLintGG_push_const_vectorLintG(std::vector<std::vector<int>>* instance, const std::vector<int>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLstd_vectorLintGG_insert_size_t_const_vectorLintG(std::vector<std::vector<int>>* instance, size_t index, const std::vector<int>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLstd_vectorLintGG_get_const_size_t(const std::vector<std::vector<int>>* instance, size_t index, std::vector<int>** ocvrs_return) {
			std::vector<int> ret = (*instance)[index];
			*ocvrs_return = new std::vector<int>(ret);
	}
	
	void std_vectorLstd_vectorLintGG_set_size_t_const_vectorLintG(std::vector<std::vector<int>>* instance, size_t index, const std::vector<int>* val) {
			(*instance)[index] = *val;
	}
	
	void std_vectorLstd_vectorLintGG_inputArray_const(const std::vector<std::vector<int>>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLintGG_outputArray(std::vector<std::vector<int>>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLintGG_inputOutputArray(std::vector<std::vector<int>>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<std::vector<signed char>>* std_vectorLstd_vectorLsigned_charGG_new_const() {
			std::vector<std::vector<signed char>>* ret = new std::vector<std::vector<signed char>>();
			return ret;
	}
	
	void std_vectorLstd_vectorLsigned_charGG_delete(std::vector<std::vector<signed char>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLstd_vectorLsigned_charGG_len_const(const std::vector<std::vector<signed char>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLstd_vectorLsigned_charGG_isEmpty_const(const std::vector<std::vector<signed char>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLstd_vectorLsigned_charGG_capacity_const(const std::vector<std::vector<signed char>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLstd_vectorLsigned_charGG_shrinkToFit(std::vector<std::vector<signed char>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLstd_vectorLsigned_charGG_reserve_size_t(std::vector<std::vector<signed char>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLstd_vectorLsigned_charGG_remove_size_t(std::vector<std::vector<signed char>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLstd_vectorLsigned_charGG_swap_size_t_size_t(std::vector<std::vector<signed char>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLstd_vectorLsigned_charGG_clear(std::vector<std::vector<signed char>>* instance) {
			instance->clear();
	}
	
	void std_vectorLstd_vectorLsigned_charGG_push_const_vectorLsigned_charG(std::vector<std::vector<signed char>>* instance, const std::vector<signed char>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLstd_vectorLsigned_charGG_insert_size_t_const_vectorLsigned_charG(std::vector<std::vector<signed char>>* instance, size_t index, const std::vector<signed char>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLstd_vectorLsigned_charGG_get_const_size_t(const std::vector<std::vector<signed char>>* instance, size_t index, std::vector<signed char>** ocvrs_return) {
			std::vector<signed char> ret = (*instance)[index];
			*ocvrs_return = new std::vector<signed char>(ret);
	}
	
	void std_vectorLstd_vectorLsigned_charGG_set_size_t_const_vectorLsigned_charG(std::vector<std::vector<signed char>>* instance, size_t index, const std::vector<signed char>* val) {
			(*instance)[index] = *val;
	}
	
}


extern "C" {
	std::vector<std::vector<unsigned char>>* std_vectorLstd_vectorLunsigned_charGG_new_const() {
			std::vector<std::vector<unsigned char>>* ret = new std::vector<std::vector<unsigned char>>();
			return ret;
	}
	
	void std_vectorLstd_vectorLunsigned_charGG_delete(std::vector<std::vector<unsigned char>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLstd_vectorLunsigned_charGG_len_const(const std::vector<std::vector<unsigned char>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLstd_vectorLunsigned_charGG_isEmpty_const(const std::vector<std::vector<unsigned char>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLstd_vectorLunsigned_charGG_capacity_const(const std::vector<std::vector<unsigned char>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLstd_vectorLunsigned_charGG_shrinkToFit(std::vector<std::vector<unsigned char>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLstd_vectorLunsigned_charGG_reserve_size_t(std::vector<std::vector<unsigned char>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLstd_vectorLunsigned_charGG_remove_size_t(std::vector<std::vector<unsigned char>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLstd_vectorLunsigned_charGG_swap_size_t_size_t(std::vector<std::vector<unsigned char>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLstd_vectorLunsigned_charGG_clear(std::vector<std::vector<unsigned char>>* instance) {
			instance->clear();
	}
	
	void std_vectorLstd_vectorLunsigned_charGG_push_const_vectorLunsigned_charG(std::vector<std::vector<unsigned char>>* instance, const std::vector<unsigned char>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLstd_vectorLunsigned_charGG_insert_size_t_const_vectorLunsigned_charG(std::vector<std::vector<unsigned char>>* instance, size_t index, const std::vector<unsigned char>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLstd_vectorLunsigned_charGG_get_const_size_t(const std::vector<std::vector<unsigned char>>* instance, size_t index, std::vector<unsigned char>** ocvrs_return) {
			std::vector<unsigned char> ret = (*instance)[index];
			*ocvrs_return = new std::vector<unsigned char>(ret);
	}
	
	void std_vectorLstd_vectorLunsigned_charGG_set_size_t_const_vectorLunsigned_charG(std::vector<std::vector<unsigned char>>* instance, size_t index, const std::vector<unsigned char>* val) {
			(*instance)[index] = *val;
	}
	
	void std_vectorLstd_vectorLunsigned_charGG_inputArray_const(const std::vector<std::vector<unsigned char>>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLunsigned_charGG_outputArray(std::vector<std::vector<unsigned char>>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLstd_vectorLunsigned_charGG_inputOutputArray(std::vector<std::vector<unsigned char>>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<bool>* std_vectorLboolG_new_const() {
			std::vector<bool>* ret = new std::vector<bool>();
			return ret;
	}
	
	void std_vectorLboolG_delete(std::vector<bool>* instance) {
			delete instance;
	}
	
	size_t std_vectorLboolG_len_const(const std::vector<bool>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLboolG_isEmpty_const(const std::vector<bool>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLboolG_capacity_const(const std::vector<bool>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLboolG_shrinkToFit(std::vector<bool>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLboolG_reserve_size_t(std::vector<bool>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLboolG_remove_size_t(std::vector<bool>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLboolG_swap_size_t_size_t(std::vector<bool>* instance, size_t index1, size_t index2) {
			instance->swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLboolG_clear(std::vector<bool>* instance) {
			instance->clear();
	}
	
	void std_vectorLboolG_push_const_bool(std::vector<bool>* instance, const bool val) {
			instance->push_back(val);
	}
	
	void std_vectorLboolG_insert_size_t_const_bool(std::vector<bool>* instance, size_t index, const bool val) {
			instance->insert(instance->begin() + index, val);
	}
	
	void std_vectorLboolG_get_const_size_t(const std::vector<bool>* instance, size_t index, bool* ocvrs_return) {
			bool ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLboolG_set_size_t_const_bool(std::vector<bool>* instance, size_t index, const bool val) {
			(*instance)[index] = val;
	}
	
}


extern "C" {
}


extern "C" {
	std::vector<float>* std_vectorLfloatG_new_const() {
			std::vector<float>* ret = new std::vector<float>();
			return ret;
	}
	
	void std_vectorLfloatG_delete(std::vector<float>* instance) {
			delete instance;
	}
	
	size_t std_vectorLfloatG_len_const(const std::vector<float>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLfloatG_isEmpty_const(const std::vector<float>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLfloatG_capacity_const(const std::vector<float>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLfloatG_shrinkToFit(std::vector<float>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLfloatG_reserve_size_t(std::vector<float>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLfloatG_remove_size_t(std::vector<float>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLfloatG_swap_size_t_size_t(std::vector<float>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLfloatG_clear(std::vector<float>* instance) {
			instance->clear();
	}
	
	void std_vectorLfloatG_push_const_float(std::vector<float>* instance, const float val) {
			instance->push_back(val);
	}
	
	void std_vectorLfloatG_insert_size_t_const_float(std::vector<float>* instance, size_t index, const float val) {
			instance->insert(instance->begin() + index, val);
	}
	
	void std_vectorLfloatG_get_const_size_t(const std::vector<float>* instance, size_t index, float* ocvrs_return) {
			float ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLfloatG_set_size_t_const_float(std::vector<float>* instance, size_t index, const float val) {
			(*instance)[index] = val;
	}
	
	std::vector<float>* std_vectorLfloatG_clone_const(const std::vector<float>* instance) {
			std::vector<float> ret = std::vector<float>(*instance);
			return new std::vector<float>(ret);
	}
	
	const float* std_vectorLfloatG_data_const(const std::vector<float>* instance) {
			const float* ret = instance->data();
			return ret;
	}
	
	float* std_vectorLfloatG_dataMut(std::vector<float>* instance) {
			float* ret = instance->data();
			return ret;
	}
	
	std::vector<float>* cv_fromSlice_const_const_floatX_size_t(const float* data, size_t len) {
			return new std::vector<float>(data, data + len);
	}
	
	void std_vectorLfloatG_inputArray_const(const std::vector<float>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLfloatG_outputArray(std::vector<float>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLfloatG_inputOutputArray(std::vector<float>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<double>* std_vectorLdoubleG_new_const() {
			std::vector<double>* ret = new std::vector<double>();
			return ret;
	}
	
	void std_vectorLdoubleG_delete(std::vector<double>* instance) {
			delete instance;
	}
	
	size_t std_vectorLdoubleG_len_const(const std::vector<double>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLdoubleG_isEmpty_const(const std::vector<double>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLdoubleG_capacity_const(const std::vector<double>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLdoubleG_shrinkToFit(std::vector<double>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLdoubleG_reserve_size_t(std::vector<double>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLdoubleG_remove_size_t(std::vector<double>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLdoubleG_swap_size_t_size_t(std::vector<double>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLdoubleG_clear(std::vector<double>* instance) {
			instance->clear();
	}
	
	void std_vectorLdoubleG_push_const_double(std::vector<double>* instance, const double val) {
			instance->push_back(val);
	}
	
	void std_vectorLdoubleG_insert_size_t_const_double(std::vector<double>* instance, size_t index, const double val) {
			instance->insert(instance->begin() + index, val);
	}
	
	void std_vectorLdoubleG_get_const_size_t(const std::vector<double>* instance, size_t index, double* ocvrs_return) {
			double ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLdoubleG_set_size_t_const_double(std::vector<double>* instance, size_t index, const double val) {
			(*instance)[index] = val;
	}
	
	std::vector<double>* std_vectorLdoubleG_clone_const(const std::vector<double>* instance) {
			std::vector<double> ret = std::vector<double>(*instance);
			return new std::vector<double>(ret);
	}
	
	const double* std_vectorLdoubleG_data_const(const std::vector<double>* instance) {
			const double* ret = instance->data();
			return ret;
	}
	
	double* std_vectorLdoubleG_dataMut(std::vector<double>* instance) {
			double* ret = instance->data();
			return ret;
	}
	
	std::vector<double>* cv_fromSlice_const_const_doubleX_size_t(const double* data, size_t len) {
			return new std::vector<double>(data, data + len);
	}
	
	void std_vectorLdoubleG_inputArray_const(const std::vector<double>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLdoubleG_outputArray(std::vector<double>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLdoubleG_inputOutputArray(std::vector<double>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<int>* std_vectorLintG_new_const() {
			std::vector<int>* ret = new std::vector<int>();
			return ret;
	}
	
	void std_vectorLintG_delete(std::vector<int>* instance) {
			delete instance;
	}
	
	size_t std_vectorLintG_len_const(const std::vector<int>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLintG_isEmpty_const(const std::vector<int>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLintG_capacity_const(const std::vector<int>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLintG_shrinkToFit(std::vector<int>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLintG_reserve_size_t(std::vector<int>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLintG_remove_size_t(std::vector<int>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLintG_swap_size_t_size_t(std::vector<int>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLintG_clear(std::vector<int>* instance) {
			instance->clear();
	}
	
	void std_vectorLintG_push_const_int(std::vector<int>* instance, const int val) {
			instance->push_back(val);
	}
	
	void std_vectorLintG_insert_size_t_const_int(std::vector<int>* instance, size_t index, const int val) {
			instance->insert(instance->begin() + index, val);
	}
	
	void std_vectorLintG_get_const_size_t(const std::vector<int>* instance, size_t index, int* ocvrs_return) {
			int ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLintG_set_size_t_const_int(std::vector<int>* instance, size_t index, const int val) {
			(*instance)[index] = val;
	}
	
	std::vector<int>* std_vectorLintG_clone_const(const std::vector<int>* instance) {
			std::vector<int> ret = std::vector<int>(*instance);
			return new std::vector<int>(ret);
	}
	
	const int* std_vectorLintG_data_const(const std::vector<int>* instance) {
			const int* ret = instance->data();
			return ret;
	}
	
	int* std_vectorLintG_dataMut(std::vector<int>* instance) {
			int* ret = instance->data();
			return ret;
	}
	
	std::vector<int>* cv_fromSlice_const_const_intX_size_t(const int* data, size_t len) {
			return new std::vector<int>(data, data + len);
	}
	
	void std_vectorLintG_inputArray_const(const std::vector<int>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLintG_outputArray(std::vector<int>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLintG_inputOutputArray(std::vector<int>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<signed char>* std_vectorLsigned_charG_new_const() {
			std::vector<signed char>* ret = new std::vector<signed char>();
			return ret;
	}
	
	void std_vectorLsigned_charG_delete(std::vector<signed char>* instance) {
			delete instance;
	}
	
	size_t std_vectorLsigned_charG_len_const(const std::vector<signed char>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLsigned_charG_isEmpty_const(const std::vector<signed char>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLsigned_charG_capacity_const(const std::vector<signed char>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLsigned_charG_shrinkToFit(std::vector<signed char>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLsigned_charG_reserve_size_t(std::vector<signed char>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLsigned_charG_remove_size_t(std::vector<signed char>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLsigned_charG_swap_size_t_size_t(std::vector<signed char>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLsigned_charG_clear(std::vector<signed char>* instance) {
			instance->clear();
	}
	
	void std_vectorLsigned_charG_push_const_signed_char(std::vector<signed char>* instance, const signed char val) {
			instance->push_back(val);
	}
	
	void std_vectorLsigned_charG_insert_size_t_const_signed_char(std::vector<signed char>* instance, size_t index, const signed char val) {
			instance->insert(instance->begin() + index, val);
	}
	
	void std_vectorLsigned_charG_get_const_size_t(const std::vector<signed char>* instance, size_t index, signed char* ocvrs_return) {
			signed char ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLsigned_charG_set_size_t_const_signed_char(std::vector<signed char>* instance, size_t index, const signed char val) {
			(*instance)[index] = val;
	}
	
	std::vector<signed char>* std_vectorLsigned_charG_clone_const(const std::vector<signed char>* instance) {
			std::vector<signed char> ret = std::vector<signed char>(*instance);
			return new std::vector<signed char>(ret);
	}
	
	const signed char* std_vectorLsigned_charG_data_const(const std::vector<signed char>* instance) {
			const signed char* ret = instance->data();
			return ret;
	}
	
	signed char* std_vectorLsigned_charG_dataMut(std::vector<signed char>* instance) {
			signed char* ret = instance->data();
			return ret;
	}
	
	std::vector<signed char>* cv_fromSlice_const_const_signed_charX_size_t(const signed char* data, size_t len) {
			return new std::vector<signed char>(data, data + len);
	}
	
}


extern "C" {
	std::vector<size_t>* std_vectorLsize_tG_new_const() {
			std::vector<size_t>* ret = new std::vector<size_t>();
			return ret;
	}
	
	void std_vectorLsize_tG_delete(std::vector<size_t>* instance) {
			delete instance;
	}
	
	size_t std_vectorLsize_tG_len_const(const std::vector<size_t>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLsize_tG_isEmpty_const(const std::vector<size_t>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLsize_tG_capacity_const(const std::vector<size_t>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLsize_tG_shrinkToFit(std::vector<size_t>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLsize_tG_reserve_size_t(std::vector<size_t>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLsize_tG_remove_size_t(std::vector<size_t>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLsize_tG_swap_size_t_size_t(std::vector<size_t>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLsize_tG_clear(std::vector<size_t>* instance) {
			instance->clear();
	}
	
	void std_vectorLsize_tG_push_const_size_t(std::vector<size_t>* instance, const size_t val) {
			instance->push_back(val);
	}
	
	void std_vectorLsize_tG_insert_size_t_const_size_t(std::vector<size_t>* instance, size_t index, const size_t val) {
			instance->insert(instance->begin() + index, val);
	}
	
	void std_vectorLsize_tG_get_const_size_t(const std::vector<size_t>* instance, size_t index, size_t* ocvrs_return) {
			size_t ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLsize_tG_set_size_t_const_size_t(std::vector<size_t>* instance, size_t index, const size_t val) {
			(*instance)[index] = val;
	}
	
	std::vector<size_t>* std_vectorLsize_tG_clone_const(const std::vector<size_t>* instance) {
			std::vector<size_t> ret = std::vector<size_t>(*instance);
			return new std::vector<size_t>(ret);
	}
	
	const size_t* std_vectorLsize_tG_data_const(const std::vector<size_t>* instance) {
			const size_t* ret = instance->data();
			return ret;
	}
	
	size_t* std_vectorLsize_tG_dataMut(std::vector<size_t>* instance) {
			size_t* ret = instance->data();
			return ret;
	}
	
	std::vector<size_t>* cv_fromSlice_const_const_size_tX_size_t(const size_t* data, size_t len) {
			return new std::vector<size_t>(data, data + len);
	}
	
}


extern "C" {
	std::vector<unsigned char>* std_vectorLunsigned_charG_new_const() {
			std::vector<unsigned char>* ret = new std::vector<unsigned char>();
			return ret;
	}
	
	void std_vectorLunsigned_charG_delete(std::vector<unsigned char>* instance) {
			delete instance;
	}
	
	size_t std_vectorLunsigned_charG_len_const(const std::vector<unsigned char>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLunsigned_charG_isEmpty_const(const std::vector<unsigned char>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLunsigned_charG_capacity_const(const std::vector<unsigned char>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLunsigned_charG_shrinkToFit(std::vector<unsigned char>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLunsigned_charG_reserve_size_t(std::vector<unsigned char>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLunsigned_charG_remove_size_t(std::vector<unsigned char>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLunsigned_charG_swap_size_t_size_t(std::vector<unsigned char>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLunsigned_charG_clear(std::vector<unsigned char>* instance) {
			instance->clear();
	}
	
	void std_vectorLunsigned_charG_push_const_unsigned_char(std::vector<unsigned char>* instance, const unsigned char val) {
			instance->push_back(val);
	}
	
	void std_vectorLunsigned_charG_insert_size_t_const_unsigned_char(std::vector<unsigned char>* instance, size_t index, const unsigned char val) {
			instance->insert(instance->begin() + index, val);
	}
	
	void std_vectorLunsigned_charG_get_const_size_t(const std::vector<unsigned char>* instance, size_t index, unsigned char* ocvrs_return) {
			unsigned char ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLunsigned_charG_set_size_t_const_unsigned_char(std::vector<unsigned char>* instance, size_t index, const unsigned char val) {
			(*instance)[index] = val;
	}
	
	std::vector<unsigned char>* std_vectorLunsigned_charG_clone_const(const std::vector<unsigned char>* instance) {
			std::vector<unsigned char> ret = std::vector<unsigned char>(*instance);
			return new std::vector<unsigned char>(ret);
	}
	
	const unsigned char* std_vectorLunsigned_charG_data_const(const std::vector<unsigned char>* instance) {
			const unsigned char* ret = instance->data();
			return ret;
	}
	
	unsigned char* std_vectorLunsigned_charG_dataMut(std::vector<unsigned char>* instance) {
			unsigned char* ret = instance->data();
			return ret;
	}
	
	std::vector<unsigned char>* cv_fromSlice_const_const_unsigned_charX_size_t(const unsigned char* data, size_t len) {
			return new std::vector<unsigned char>(data, data + len);
	}
	
	void std_vectorLunsigned_charG_inputArray_const(const std::vector<unsigned char>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLunsigned_charG_outputArray(std::vector<unsigned char>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLunsigned_charG_inputOutputArray(std::vector<unsigned char>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}



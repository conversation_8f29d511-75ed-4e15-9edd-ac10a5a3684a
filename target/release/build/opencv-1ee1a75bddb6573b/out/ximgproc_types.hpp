extern "C" {
	const cv::ximgproc::AdaptiveManifoldFilter* cv_PtrLcv_ximgproc_AdaptiveManifoldFilterG_getInnerPtr_const(const cv::Ptr<cv::ximgproc::AdaptiveManifoldFilter>* instance) {
			return instance->get();
	}
	
	cv::ximgproc::AdaptiveManifoldFilter* cv_PtrLcv_ximgproc_AdaptiveManifoldFilterG_getInnerPtrMut(cv::Ptr<cv::ximgproc::AdaptiveManifoldFilter>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ximgproc_AdaptiveManifoldFilterG_delete(cv::Ptr<cv::ximgproc::AdaptiveManifoldFilter>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ximgproc_AdaptiveManifoldFilterG_to_PtrOfAlgorithm(cv::Ptr<cv::ximgproc::AdaptiveManifoldFilter>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::ximgproc::ContourFitting* cv_PtrLcv_ximgproc_ContourFittingG_getInnerPtr_const(const cv::Ptr<cv::ximgproc::ContourFitting>* instance) {
			return instance->get();
	}
	
	cv::ximgproc::ContourFitting* cv_PtrLcv_ximgproc_ContourFittingG_getInnerPtrMut(cv::Ptr<cv::ximgproc::ContourFitting>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ximgproc_ContourFittingG_delete(cv::Ptr<cv::ximgproc::ContourFitting>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ximgproc_ContourFittingG_to_PtrOfAlgorithm(cv::Ptr<cv::ximgproc::ContourFitting>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::ximgproc::ContourFitting>* cv_PtrLcv_ximgproc_ContourFittingG_new_const_ContourFitting(cv::ximgproc::ContourFitting* val) {
			return new cv::Ptr<cv::ximgproc::ContourFitting>(val);
	}
	
}

extern "C" {
	const cv::ximgproc::DTFilter* cv_PtrLcv_ximgproc_DTFilterG_getInnerPtr_const(const cv::Ptr<cv::ximgproc::DTFilter>* instance) {
			return instance->get();
	}
	
	cv::ximgproc::DTFilter* cv_PtrLcv_ximgproc_DTFilterG_getInnerPtrMut(cv::Ptr<cv::ximgproc::DTFilter>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ximgproc_DTFilterG_delete(cv::Ptr<cv::ximgproc::DTFilter>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ximgproc_DTFilterG_to_PtrOfAlgorithm(cv::Ptr<cv::ximgproc::DTFilter>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::ximgproc::DisparityFilter* cv_PtrLcv_ximgproc_DisparityFilterG_getInnerPtr_const(const cv::Ptr<cv::ximgproc::DisparityFilter>* instance) {
			return instance->get();
	}
	
	cv::ximgproc::DisparityFilter* cv_PtrLcv_ximgproc_DisparityFilterG_getInnerPtrMut(cv::Ptr<cv::ximgproc::DisparityFilter>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ximgproc_DisparityFilterG_delete(cv::Ptr<cv::ximgproc::DisparityFilter>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ximgproc_DisparityFilterG_to_PtrOfAlgorithm(cv::Ptr<cv::ximgproc::DisparityFilter>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::ximgproc::DisparityWLSFilter* cv_PtrLcv_ximgproc_DisparityWLSFilterG_getInnerPtr_const(const cv::Ptr<cv::ximgproc::DisparityWLSFilter>* instance) {
			return instance->get();
	}
	
	cv::ximgproc::DisparityWLSFilter* cv_PtrLcv_ximgproc_DisparityWLSFilterG_getInnerPtrMut(cv::Ptr<cv::ximgproc::DisparityWLSFilter>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ximgproc_DisparityWLSFilterG_delete(cv::Ptr<cv::ximgproc::DisparityWLSFilter>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ximgproc_DisparityWLSFilterG_to_PtrOfAlgorithm(cv::Ptr<cv::ximgproc::DisparityWLSFilter>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::ximgproc::DisparityFilter>* cv_PtrLcv_ximgproc_DisparityWLSFilterG_to_PtrOfDisparityFilter(cv::Ptr<cv::ximgproc::DisparityWLSFilter>* instance) {
			return new cv::Ptr<cv::ximgproc::DisparityFilter>(instance->dynamicCast<cv::ximgproc::DisparityFilter>());
	}
	
}

extern "C" {
	const cv::ximgproc::EdgeAwareInterpolator* cv_PtrLcv_ximgproc_EdgeAwareInterpolatorG_getInnerPtr_const(const cv::Ptr<cv::ximgproc::EdgeAwareInterpolator>* instance) {
			return instance->get();
	}
	
	cv::ximgproc::EdgeAwareInterpolator* cv_PtrLcv_ximgproc_EdgeAwareInterpolatorG_getInnerPtrMut(cv::Ptr<cv::ximgproc::EdgeAwareInterpolator>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ximgproc_EdgeAwareInterpolatorG_delete(cv::Ptr<cv::ximgproc::EdgeAwareInterpolator>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ximgproc_EdgeAwareInterpolatorG_to_PtrOfAlgorithm(cv::Ptr<cv::ximgproc::EdgeAwareInterpolator>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::ximgproc::SparseMatchInterpolator>* cv_PtrLcv_ximgproc_EdgeAwareInterpolatorG_to_PtrOfSparseMatchInterpolator(cv::Ptr<cv::ximgproc::EdgeAwareInterpolator>* instance) {
			return new cv::Ptr<cv::ximgproc::SparseMatchInterpolator>(instance->dynamicCast<cv::ximgproc::SparseMatchInterpolator>());
	}
	
}

extern "C" {
	const cv::ximgproc::EdgeBoxes* cv_PtrLcv_ximgproc_EdgeBoxesG_getInnerPtr_const(const cv::Ptr<cv::ximgproc::EdgeBoxes>* instance) {
			return instance->get();
	}
	
	cv::ximgproc::EdgeBoxes* cv_PtrLcv_ximgproc_EdgeBoxesG_getInnerPtrMut(cv::Ptr<cv::ximgproc::EdgeBoxes>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ximgproc_EdgeBoxesG_delete(cv::Ptr<cv::ximgproc::EdgeBoxes>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ximgproc_EdgeBoxesG_to_PtrOfAlgorithm(cv::Ptr<cv::ximgproc::EdgeBoxes>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::ximgproc::EdgeDrawing* cv_PtrLcv_ximgproc_EdgeDrawingG_getInnerPtr_const(const cv::Ptr<cv::ximgproc::EdgeDrawing>* instance) {
			return instance->get();
	}
	
	cv::ximgproc::EdgeDrawing* cv_PtrLcv_ximgproc_EdgeDrawingG_getInnerPtrMut(cv::Ptr<cv::ximgproc::EdgeDrawing>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ximgproc_EdgeDrawingG_delete(cv::Ptr<cv::ximgproc::EdgeDrawing>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ximgproc_EdgeDrawingG_to_PtrOfAlgorithm(cv::Ptr<cv::ximgproc::EdgeDrawing>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::ximgproc::FastBilateralSolverFilter* cv_PtrLcv_ximgproc_FastBilateralSolverFilterG_getInnerPtr_const(const cv::Ptr<cv::ximgproc::FastBilateralSolverFilter>* instance) {
			return instance->get();
	}
	
	cv::ximgproc::FastBilateralSolverFilter* cv_PtrLcv_ximgproc_FastBilateralSolverFilterG_getInnerPtrMut(cv::Ptr<cv::ximgproc::FastBilateralSolverFilter>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ximgproc_FastBilateralSolverFilterG_delete(cv::Ptr<cv::ximgproc::FastBilateralSolverFilter>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ximgproc_FastBilateralSolverFilterG_to_PtrOfAlgorithm(cv::Ptr<cv::ximgproc::FastBilateralSolverFilter>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::ximgproc::FastGlobalSmootherFilter* cv_PtrLcv_ximgproc_FastGlobalSmootherFilterG_getInnerPtr_const(const cv::Ptr<cv::ximgproc::FastGlobalSmootherFilter>* instance) {
			return instance->get();
	}
	
	cv::ximgproc::FastGlobalSmootherFilter* cv_PtrLcv_ximgproc_FastGlobalSmootherFilterG_getInnerPtrMut(cv::Ptr<cv::ximgproc::FastGlobalSmootherFilter>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ximgproc_FastGlobalSmootherFilterG_delete(cv::Ptr<cv::ximgproc::FastGlobalSmootherFilter>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ximgproc_FastGlobalSmootherFilterG_to_PtrOfAlgorithm(cv::Ptr<cv::ximgproc::FastGlobalSmootherFilter>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::ximgproc::FastLineDetector* cv_PtrLcv_ximgproc_FastLineDetectorG_getInnerPtr_const(const cv::Ptr<cv::ximgproc::FastLineDetector>* instance) {
			return instance->get();
	}
	
	cv::ximgproc::FastLineDetector* cv_PtrLcv_ximgproc_FastLineDetectorG_getInnerPtrMut(cv::Ptr<cv::ximgproc::FastLineDetector>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ximgproc_FastLineDetectorG_delete(cv::Ptr<cv::ximgproc::FastLineDetector>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ximgproc_FastLineDetectorG_to_PtrOfAlgorithm(cv::Ptr<cv::ximgproc::FastLineDetector>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::ximgproc::segmentation::GraphSegmentation* cv_PtrLcv_ximgproc_segmentation_GraphSegmentationG_getInnerPtr_const(const cv::Ptr<cv::ximgproc::segmentation::GraphSegmentation>* instance) {
			return instance->get();
	}
	
	cv::ximgproc::segmentation::GraphSegmentation* cv_PtrLcv_ximgproc_segmentation_GraphSegmentationG_getInnerPtrMut(cv::Ptr<cv::ximgproc::segmentation::GraphSegmentation>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ximgproc_segmentation_GraphSegmentationG_delete(cv::Ptr<cv::ximgproc::segmentation::GraphSegmentation>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ximgproc_segmentation_GraphSegmentationG_to_PtrOfAlgorithm(cv::Ptr<cv::ximgproc::segmentation::GraphSegmentation>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::ximgproc::GuidedFilter* cv_PtrLcv_ximgproc_GuidedFilterG_getInnerPtr_const(const cv::Ptr<cv::ximgproc::GuidedFilter>* instance) {
			return instance->get();
	}
	
	cv::ximgproc::GuidedFilter* cv_PtrLcv_ximgproc_GuidedFilterG_getInnerPtrMut(cv::Ptr<cv::ximgproc::GuidedFilter>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ximgproc_GuidedFilterG_delete(cv::Ptr<cv::ximgproc::GuidedFilter>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ximgproc_GuidedFilterG_to_PtrOfAlgorithm(cv::Ptr<cv::ximgproc::GuidedFilter>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::ximgproc::RFFeatureGetter* cv_PtrLcv_ximgproc_RFFeatureGetterG_getInnerPtr_const(const cv::Ptr<cv::ximgproc::RFFeatureGetter>* instance) {
			return instance->get();
	}
	
	cv::ximgproc::RFFeatureGetter* cv_PtrLcv_ximgproc_RFFeatureGetterG_getInnerPtrMut(cv::Ptr<cv::ximgproc::RFFeatureGetter>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ximgproc_RFFeatureGetterG_delete(cv::Ptr<cv::ximgproc::RFFeatureGetter>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ximgproc_RFFeatureGetterG_to_PtrOfAlgorithm(cv::Ptr<cv::ximgproc::RFFeatureGetter>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::ximgproc::RICInterpolator* cv_PtrLcv_ximgproc_RICInterpolatorG_getInnerPtr_const(const cv::Ptr<cv::ximgproc::RICInterpolator>* instance) {
			return instance->get();
	}
	
	cv::ximgproc::RICInterpolator* cv_PtrLcv_ximgproc_RICInterpolatorG_getInnerPtrMut(cv::Ptr<cv::ximgproc::RICInterpolator>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ximgproc_RICInterpolatorG_delete(cv::Ptr<cv::ximgproc::RICInterpolator>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ximgproc_RICInterpolatorG_to_PtrOfAlgorithm(cv::Ptr<cv::ximgproc::RICInterpolator>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::ximgproc::SparseMatchInterpolator>* cv_PtrLcv_ximgproc_RICInterpolatorG_to_PtrOfSparseMatchInterpolator(cv::Ptr<cv::ximgproc::RICInterpolator>* instance) {
			return new cv::Ptr<cv::ximgproc::SparseMatchInterpolator>(instance->dynamicCast<cv::ximgproc::SparseMatchInterpolator>());
	}
	
}

extern "C" {
	const cv::ximgproc::RidgeDetectionFilter* cv_PtrLcv_ximgproc_RidgeDetectionFilterG_getInnerPtr_const(const cv::Ptr<cv::ximgproc::RidgeDetectionFilter>* instance) {
			return instance->get();
	}
	
	cv::ximgproc::RidgeDetectionFilter* cv_PtrLcv_ximgproc_RidgeDetectionFilterG_getInnerPtrMut(cv::Ptr<cv::ximgproc::RidgeDetectionFilter>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ximgproc_RidgeDetectionFilterG_delete(cv::Ptr<cv::ximgproc::RidgeDetectionFilter>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ximgproc_RidgeDetectionFilterG_to_PtrOfAlgorithm(cv::Ptr<cv::ximgproc::RidgeDetectionFilter>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::ximgproc::segmentation::SelectiveSearchSegmentation* cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationG_getInnerPtr_const(const cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentation>* instance) {
			return instance->get();
	}
	
	cv::ximgproc::segmentation::SelectiveSearchSegmentation* cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationG_getInnerPtrMut(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentation>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationG_delete(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentation>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationG_to_PtrOfAlgorithm(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentation>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategy* cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyG_getInnerPtr_const(const cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategy>* instance) {
			return instance->get();
	}
	
	cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategy* cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyG_getInnerPtrMut(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategy>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyG_delete(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategy>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyG_to_PtrOfAlgorithm(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategy>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyColor* cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyColorG_getInnerPtr_const(const cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyColor>* instance) {
			return instance->get();
	}
	
	cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyColor* cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyColorG_getInnerPtrMut(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyColor>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyColorG_delete(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyColor>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyColorG_to_PtrOfAlgorithm(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyColor>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategy>* cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyColorG_to_PtrOfSelectiveSearchSegmentationStrategy(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyColor>* instance) {
			return new cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategy>(instance->dynamicCast<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategy>());
	}
	
}

extern "C" {
	const cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyFill* cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyFillG_getInnerPtr_const(const cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyFill>* instance) {
			return instance->get();
	}
	
	cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyFill* cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyFillG_getInnerPtrMut(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyFill>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyFillG_delete(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyFill>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyFillG_to_PtrOfAlgorithm(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyFill>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategy>* cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyFillG_to_PtrOfSelectiveSearchSegmentationStrategy(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyFill>* instance) {
			return new cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategy>(instance->dynamicCast<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategy>());
	}
	
}

extern "C" {
	const cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyMultiple* cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyMultipleG_getInnerPtr_const(const cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyMultiple>* instance) {
			return instance->get();
	}
	
	cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyMultiple* cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyMultipleG_getInnerPtrMut(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyMultiple>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyMultipleG_delete(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyMultiple>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyMultipleG_to_PtrOfAlgorithm(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyMultiple>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategy>* cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyMultipleG_to_PtrOfSelectiveSearchSegmentationStrategy(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyMultiple>* instance) {
			return new cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategy>(instance->dynamicCast<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategy>());
	}
	
}

extern "C" {
	const cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategySize* cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategySizeG_getInnerPtr_const(const cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategySize>* instance) {
			return instance->get();
	}
	
	cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategySize* cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategySizeG_getInnerPtrMut(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategySize>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategySizeG_delete(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategySize>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategySizeG_to_PtrOfAlgorithm(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategySize>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategy>* cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategySizeG_to_PtrOfSelectiveSearchSegmentationStrategy(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategySize>* instance) {
			return new cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategy>(instance->dynamicCast<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategy>());
	}
	
}

extern "C" {
	const cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyTexture* cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyTextureG_getInnerPtr_const(const cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyTexture>* instance) {
			return instance->get();
	}
	
	cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyTexture* cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyTextureG_getInnerPtrMut(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyTexture>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyTextureG_delete(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyTexture>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyTextureG_to_PtrOfAlgorithm(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyTexture>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategy>* cv_PtrLcv_ximgproc_segmentation_SelectiveSearchSegmentationStrategyTextureG_to_PtrOfSelectiveSearchSegmentationStrategy(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyTexture>* instance) {
			return new cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategy>(instance->dynamicCast<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategy>());
	}
	
}

extern "C" {
	const cv::ximgproc::SparseMatchInterpolator* cv_PtrLcv_ximgproc_SparseMatchInterpolatorG_getInnerPtr_const(const cv::Ptr<cv::ximgproc::SparseMatchInterpolator>* instance) {
			return instance->get();
	}
	
	cv::ximgproc::SparseMatchInterpolator* cv_PtrLcv_ximgproc_SparseMatchInterpolatorG_getInnerPtrMut(cv::Ptr<cv::ximgproc::SparseMatchInterpolator>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ximgproc_SparseMatchInterpolatorG_delete(cv::Ptr<cv::ximgproc::SparseMatchInterpolator>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ximgproc_SparseMatchInterpolatorG_to_PtrOfAlgorithm(cv::Ptr<cv::ximgproc::SparseMatchInterpolator>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::ximgproc::StructuredEdgeDetection* cv_PtrLcv_ximgproc_StructuredEdgeDetectionG_getInnerPtr_const(const cv::Ptr<cv::ximgproc::StructuredEdgeDetection>* instance) {
			return instance->get();
	}
	
	cv::ximgproc::StructuredEdgeDetection* cv_PtrLcv_ximgproc_StructuredEdgeDetectionG_getInnerPtrMut(cv::Ptr<cv::ximgproc::StructuredEdgeDetection>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ximgproc_StructuredEdgeDetectionG_delete(cv::Ptr<cv::ximgproc::StructuredEdgeDetection>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ximgproc_StructuredEdgeDetectionG_to_PtrOfAlgorithm(cv::Ptr<cv::ximgproc::StructuredEdgeDetection>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::ximgproc::SuperpixelLSC* cv_PtrLcv_ximgproc_SuperpixelLSCG_getInnerPtr_const(const cv::Ptr<cv::ximgproc::SuperpixelLSC>* instance) {
			return instance->get();
	}
	
	cv::ximgproc::SuperpixelLSC* cv_PtrLcv_ximgproc_SuperpixelLSCG_getInnerPtrMut(cv::Ptr<cv::ximgproc::SuperpixelLSC>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ximgproc_SuperpixelLSCG_delete(cv::Ptr<cv::ximgproc::SuperpixelLSC>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ximgproc_SuperpixelLSCG_to_PtrOfAlgorithm(cv::Ptr<cv::ximgproc::SuperpixelLSC>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::ximgproc::SuperpixelSEEDS* cv_PtrLcv_ximgproc_SuperpixelSEEDSG_getInnerPtr_const(const cv::Ptr<cv::ximgproc::SuperpixelSEEDS>* instance) {
			return instance->get();
	}
	
	cv::ximgproc::SuperpixelSEEDS* cv_PtrLcv_ximgproc_SuperpixelSEEDSG_getInnerPtrMut(cv::Ptr<cv::ximgproc::SuperpixelSEEDS>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ximgproc_SuperpixelSEEDSG_delete(cv::Ptr<cv::ximgproc::SuperpixelSEEDS>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ximgproc_SuperpixelSEEDSG_to_PtrOfAlgorithm(cv::Ptr<cv::ximgproc::SuperpixelSEEDS>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::ximgproc::SuperpixelSLIC* cv_PtrLcv_ximgproc_SuperpixelSLICG_getInnerPtr_const(const cv::Ptr<cv::ximgproc::SuperpixelSLIC>* instance) {
			return instance->get();
	}
	
	cv::ximgproc::SuperpixelSLIC* cv_PtrLcv_ximgproc_SuperpixelSLICG_getInnerPtrMut(cv::Ptr<cv::ximgproc::SuperpixelSLIC>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ximgproc_SuperpixelSLICG_delete(cv::Ptr<cv::ximgproc::SuperpixelSLIC>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ximgproc_SuperpixelSLICG_to_PtrOfAlgorithm(cv::Ptr<cv::ximgproc::SuperpixelSLIC>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	std::vector<cv::ximgproc::Box>* std_vectorLcv_ximgproc_BoxG_new_const() {
			std::vector<cv::ximgproc::Box>* ret = new std::vector<cv::ximgproc::Box>();
			return ret;
	}
	
	void std_vectorLcv_ximgproc_BoxG_delete(std::vector<cv::ximgproc::Box>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_ximgproc_BoxG_len_const(const std::vector<cv::ximgproc::Box>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_ximgproc_BoxG_isEmpty_const(const std::vector<cv::ximgproc::Box>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_ximgproc_BoxG_capacity_const(const std::vector<cv::ximgproc::Box>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_ximgproc_BoxG_shrinkToFit(std::vector<cv::ximgproc::Box>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_ximgproc_BoxG_reserve_size_t(std::vector<cv::ximgproc::Box>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_ximgproc_BoxG_remove_size_t(std::vector<cv::ximgproc::Box>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_ximgproc_BoxG_swap_size_t_size_t(std::vector<cv::ximgproc::Box>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_ximgproc_BoxG_clear(std::vector<cv::ximgproc::Box>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_ximgproc_BoxG_push_const_Box(std::vector<cv::ximgproc::Box>* instance, const cv::ximgproc::Box* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_ximgproc_BoxG_insert_size_t_const_Box(std::vector<cv::ximgproc::Box>* instance, size_t index, const cv::ximgproc::Box* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_ximgproc_BoxG_get_const_size_t(const std::vector<cv::ximgproc::Box>* instance, size_t index, cv::ximgproc::Box* ocvrs_return) {
			cv::ximgproc::Box ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLcv_ximgproc_BoxG_set_size_t_const_Box(std::vector<cv::ximgproc::Box>* instance, size_t index, const cv::ximgproc::Box* val) {
			(*instance)[index] = *val;
	}
	
	std::vector<cv::ximgproc::Box>* std_vectorLcv_ximgproc_BoxG_clone_const(const std::vector<cv::ximgproc::Box>* instance) {
			std::vector<cv::ximgproc::Box> ret = std::vector<cv::ximgproc::Box>(*instance);
			return new std::vector<cv::ximgproc::Box>(ret);
	}
	
	const cv::ximgproc::Box* std_vectorLcv_ximgproc_BoxG_data_const(const std::vector<cv::ximgproc::Box>* instance) {
			const cv::ximgproc::Box* ret = instance->data();
			return ret;
	}
	
	cv::ximgproc::Box* std_vectorLcv_ximgproc_BoxG_dataMut(std::vector<cv::ximgproc::Box>* instance) {
			cv::ximgproc::Box* ret = instance->data();
			return ret;
	}
	
	std::vector<cv::ximgproc::Box>* cv_fromSlice_const_const_BoxX_size_t(const cv::ximgproc::Box* data, size_t len) {
			return new std::vector<cv::ximgproc::Box>(data, data + len);
	}
	
}



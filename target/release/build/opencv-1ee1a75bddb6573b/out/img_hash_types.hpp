extern "C" {
	const cv::img_hash::AverageHash* cv_PtrLcv_img_hash_AverageHashG_getInnerPtr_const(const cv::Ptr<cv::img_hash::AverageHash>* instance) {
			return instance->get();
	}
	
	cv::img_hash::AverageHash* cv_PtrLcv_img_hash_AverageHashG_getInnerPtrMut(cv::Ptr<cv::img_hash::AverageHash>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_img_hash_AverageHashG_delete(cv::Ptr<cv::img_hash::AverageHash>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_img_hash_AverageHashG_to_PtrOfAlgorithm(cv::Ptr<cv::img_hash::AverageHash>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::img_hash::ImgHashBase>* cv_PtrLcv_img_hash_AverageHashG_to_PtrOfImgHashBase(cv::Ptr<cv::img_hash::AverageHash>* instance) {
			return new cv::Ptr<cv::img_hash::ImgHashBase>(instance->dynamicCast<cv::img_hash::ImgHashBase>());
	}
	
	cv::Ptr<cv::img_hash::AverageHash>* cv_PtrLcv_img_hash_AverageHashG_new_const_AverageHash(cv::img_hash::AverageHash* val) {
			return new cv::Ptr<cv::img_hash::AverageHash>(val);
	}
	
}

extern "C" {
	const cv::img_hash::BlockMeanHash* cv_PtrLcv_img_hash_BlockMeanHashG_getInnerPtr_const(const cv::Ptr<cv::img_hash::BlockMeanHash>* instance) {
			return instance->get();
	}
	
	cv::img_hash::BlockMeanHash* cv_PtrLcv_img_hash_BlockMeanHashG_getInnerPtrMut(cv::Ptr<cv::img_hash::BlockMeanHash>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_img_hash_BlockMeanHashG_delete(cv::Ptr<cv::img_hash::BlockMeanHash>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_img_hash_BlockMeanHashG_to_PtrOfAlgorithm(cv::Ptr<cv::img_hash::BlockMeanHash>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::img_hash::ImgHashBase>* cv_PtrLcv_img_hash_BlockMeanHashG_to_PtrOfImgHashBase(cv::Ptr<cv::img_hash::BlockMeanHash>* instance) {
			return new cv::Ptr<cv::img_hash::ImgHashBase>(instance->dynamicCast<cv::img_hash::ImgHashBase>());
	}
	
	cv::Ptr<cv::img_hash::BlockMeanHash>* cv_PtrLcv_img_hash_BlockMeanHashG_new_const_BlockMeanHash(cv::img_hash::BlockMeanHash* val) {
			return new cv::Ptr<cv::img_hash::BlockMeanHash>(val);
	}
	
}

extern "C" {
	const cv::img_hash::ColorMomentHash* cv_PtrLcv_img_hash_ColorMomentHashG_getInnerPtr_const(const cv::Ptr<cv::img_hash::ColorMomentHash>* instance) {
			return instance->get();
	}
	
	cv::img_hash::ColorMomentHash* cv_PtrLcv_img_hash_ColorMomentHashG_getInnerPtrMut(cv::Ptr<cv::img_hash::ColorMomentHash>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_img_hash_ColorMomentHashG_delete(cv::Ptr<cv::img_hash::ColorMomentHash>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_img_hash_ColorMomentHashG_to_PtrOfAlgorithm(cv::Ptr<cv::img_hash::ColorMomentHash>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::img_hash::ImgHashBase>* cv_PtrLcv_img_hash_ColorMomentHashG_to_PtrOfImgHashBase(cv::Ptr<cv::img_hash::ColorMomentHash>* instance) {
			return new cv::Ptr<cv::img_hash::ImgHashBase>(instance->dynamicCast<cv::img_hash::ImgHashBase>());
	}
	
	cv::Ptr<cv::img_hash::ColorMomentHash>* cv_PtrLcv_img_hash_ColorMomentHashG_new_const_ColorMomentHash(cv::img_hash::ColorMomentHash* val) {
			return new cv::Ptr<cv::img_hash::ColorMomentHash>(val);
	}
	
}

extern "C" {
	const cv::img_hash::ImgHashBase* cv_PtrLcv_img_hash_ImgHashBaseG_getInnerPtr_const(const cv::Ptr<cv::img_hash::ImgHashBase>* instance) {
			return instance->get();
	}
	
	cv::img_hash::ImgHashBase* cv_PtrLcv_img_hash_ImgHashBaseG_getInnerPtrMut(cv::Ptr<cv::img_hash::ImgHashBase>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_img_hash_ImgHashBaseG_delete(cv::Ptr<cv::img_hash::ImgHashBase>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_img_hash_ImgHashBaseG_to_PtrOfAlgorithm(cv::Ptr<cv::img_hash::ImgHashBase>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::img_hash::ImgHashBase>* cv_PtrLcv_img_hash_ImgHashBaseG_new_const_ImgHashBase(cv::img_hash::ImgHashBase* val) {
			return new cv::Ptr<cv::img_hash::ImgHashBase>(val);
	}
	
}

extern "C" {
	const cv::img_hash::MarrHildrethHash* cv_PtrLcv_img_hash_MarrHildrethHashG_getInnerPtr_const(const cv::Ptr<cv::img_hash::MarrHildrethHash>* instance) {
			return instance->get();
	}
	
	cv::img_hash::MarrHildrethHash* cv_PtrLcv_img_hash_MarrHildrethHashG_getInnerPtrMut(cv::Ptr<cv::img_hash::MarrHildrethHash>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_img_hash_MarrHildrethHashG_delete(cv::Ptr<cv::img_hash::MarrHildrethHash>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_img_hash_MarrHildrethHashG_to_PtrOfAlgorithm(cv::Ptr<cv::img_hash::MarrHildrethHash>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::img_hash::ImgHashBase>* cv_PtrLcv_img_hash_MarrHildrethHashG_to_PtrOfImgHashBase(cv::Ptr<cv::img_hash::MarrHildrethHash>* instance) {
			return new cv::Ptr<cv::img_hash::ImgHashBase>(instance->dynamicCast<cv::img_hash::ImgHashBase>());
	}
	
	cv::Ptr<cv::img_hash::MarrHildrethHash>* cv_PtrLcv_img_hash_MarrHildrethHashG_new_const_MarrHildrethHash(cv::img_hash::MarrHildrethHash* val) {
			return new cv::Ptr<cv::img_hash::MarrHildrethHash>(val);
	}
	
}

extern "C" {
	const cv::img_hash::PHash* cv_PtrLcv_img_hash_PHashG_getInnerPtr_const(const cv::Ptr<cv::img_hash::PHash>* instance) {
			return instance->get();
	}
	
	cv::img_hash::PHash* cv_PtrLcv_img_hash_PHashG_getInnerPtrMut(cv::Ptr<cv::img_hash::PHash>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_img_hash_PHashG_delete(cv::Ptr<cv::img_hash::PHash>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_img_hash_PHashG_to_PtrOfAlgorithm(cv::Ptr<cv::img_hash::PHash>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::img_hash::ImgHashBase>* cv_PtrLcv_img_hash_PHashG_to_PtrOfImgHashBase(cv::Ptr<cv::img_hash::PHash>* instance) {
			return new cv::Ptr<cv::img_hash::ImgHashBase>(instance->dynamicCast<cv::img_hash::ImgHashBase>());
	}
	
	cv::Ptr<cv::img_hash::PHash>* cv_PtrLcv_img_hash_PHashG_new_const_PHash(cv::img_hash::PHash* val) {
			return new cv::Ptr<cv::img_hash::PHash>(val);
	}
	
}

extern "C" {
	const cv::img_hash::RadialVarianceHash* cv_PtrLcv_img_hash_RadialVarianceHashG_getInnerPtr_const(const cv::Ptr<cv::img_hash::RadialVarianceHash>* instance) {
			return instance->get();
	}
	
	cv::img_hash::RadialVarianceHash* cv_PtrLcv_img_hash_RadialVarianceHashG_getInnerPtrMut(cv::Ptr<cv::img_hash::RadialVarianceHash>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_img_hash_RadialVarianceHashG_delete(cv::Ptr<cv::img_hash::RadialVarianceHash>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_img_hash_RadialVarianceHashG_to_PtrOfAlgorithm(cv::Ptr<cv::img_hash::RadialVarianceHash>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::img_hash::ImgHashBase>* cv_PtrLcv_img_hash_RadialVarianceHashG_to_PtrOfImgHashBase(cv::Ptr<cv::img_hash::RadialVarianceHash>* instance) {
			return new cv::Ptr<cv::img_hash::ImgHashBase>(instance->dynamicCast<cv::img_hash::ImgHashBase>());
	}
	
	cv::Ptr<cv::img_hash::RadialVarianceHash>* cv_PtrLcv_img_hash_RadialVarianceHashG_new_const_RadialVarianceHash(cv::img_hash::RadialVarianceHash* val) {
			return new cv::Ptr<cv::img_hash::RadialVarianceHash>(val);
	}
	
}


extern "C" {
	const cv::structured_light::GrayCodePattern* cv_PtrLcv_structured_light_GrayCodePatternG_getInnerPtr_const(const cv::Ptr<cv::structured_light::GrayCodePattern>* instance) {
			return instance->get();
	}
	
	cv::structured_light::GrayCodePattern* cv_PtrLcv_structured_light_GrayCodePatternG_getInnerPtrMut(cv::Ptr<cv::structured_light::GrayCodePattern>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_structured_light_GrayCodePatternG_delete(cv::Ptr<cv::structured_light::GrayCodePattern>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_structured_light_GrayCodePatternG_to_PtrOfAlgorithm(cv::Ptr<cv::structured_light::GrayCodePattern>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::structured_light::StructuredLightPattern>* cv_PtrLcv_structured_light_GrayCodePatternG_to_PtrOfStructuredLightPattern(cv::Ptr<cv::structured_light::GrayCodePattern>* instance) {
			return new cv::Ptr<cv::structured_light::StructuredLightPattern>(instance->dynamicCast<cv::structured_light::StructuredLightPattern>());
	}
	
}

extern "C" {
	const cv::structured_light::SinusoidalPattern* cv_PtrLcv_structured_light_SinusoidalPatternG_getInnerPtr_const(const cv::Ptr<cv::structured_light::SinusoidalPattern>* instance) {
			return instance->get();
	}
	
	cv::structured_light::SinusoidalPattern* cv_PtrLcv_structured_light_SinusoidalPatternG_getInnerPtrMut(cv::Ptr<cv::structured_light::SinusoidalPattern>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_structured_light_SinusoidalPatternG_delete(cv::Ptr<cv::structured_light::SinusoidalPattern>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_structured_light_SinusoidalPatternG_to_PtrOfAlgorithm(cv::Ptr<cv::structured_light::SinusoidalPattern>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::structured_light::StructuredLightPattern>* cv_PtrLcv_structured_light_SinusoidalPatternG_to_PtrOfStructuredLightPattern(cv::Ptr<cv::structured_light::SinusoidalPattern>* instance) {
			return new cv::Ptr<cv::structured_light::StructuredLightPattern>(instance->dynamicCast<cv::structured_light::StructuredLightPattern>());
	}
	
}

extern "C" {
	const cv::structured_light::SinusoidalPattern::Params* cv_PtrLcv_structured_light_SinusoidalPattern_ParamsG_getInnerPtr_const(const cv::Ptr<cv::structured_light::SinusoidalPattern::Params>* instance) {
			return instance->get();
	}
	
	cv::structured_light::SinusoidalPattern::Params* cv_PtrLcv_structured_light_SinusoidalPattern_ParamsG_getInnerPtrMut(cv::Ptr<cv::structured_light::SinusoidalPattern::Params>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_structured_light_SinusoidalPattern_ParamsG_delete(cv::Ptr<cv::structured_light::SinusoidalPattern::Params>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::structured_light::SinusoidalPattern::Params>* cv_PtrLcv_structured_light_SinusoidalPattern_ParamsG_new_const_Params(cv::structured_light::SinusoidalPattern::Params* val) {
			return new cv::Ptr<cv::structured_light::SinusoidalPattern::Params>(val);
	}
	
}

extern "C" {
	const cv::structured_light::StructuredLightPattern* cv_PtrLcv_structured_light_StructuredLightPatternG_getInnerPtr_const(const cv::Ptr<cv::structured_light::StructuredLightPattern>* instance) {
			return instance->get();
	}
	
	cv::structured_light::StructuredLightPattern* cv_PtrLcv_structured_light_StructuredLightPatternG_getInnerPtrMut(cv::Ptr<cv::structured_light::StructuredLightPattern>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_structured_light_StructuredLightPatternG_delete(cv::Ptr<cv::structured_light::StructuredLightPattern>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_structured_light_StructuredLightPatternG_to_PtrOfAlgorithm(cv::Ptr<cv::structured_light::StructuredLightPattern>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}


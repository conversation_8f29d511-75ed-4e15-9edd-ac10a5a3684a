extern "C" {
	const cv::flann::AutotunedIndexParams* cv_PtrLcv_flann_AutotunedIndexParamsG_getInnerPtr_const(const cv::Ptr<cv::flann::AutotunedIndexParams>* instance) {
			return instance->get();
	}
	
	cv::flann::AutotunedIndexParams* cv_PtrLcv_flann_AutotunedIndexParamsG_getInnerPtrMut(cv::Ptr<cv::flann::AutotunedIndexParams>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_flann_AutotunedIndexParamsG_delete(cv::Ptr<cv::flann::AutotunedIndexParams>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::flann::IndexParams>* cv_PtrLcv_flann_AutotunedIndexParamsG_to_PtrOfIndexParams(cv::Ptr<cv::flann::AutotunedIndexParams>* instance) {
			return new cv::Ptr<cv::flann::IndexParams>(instance->dynamicCast<cv::flann::IndexParams>());
	}
	
	cv::Ptr<cv::flann::AutotunedIndexParams>* cv_PtrLcv_flann_AutotunedIndexParamsG_new_const_AutotunedIndexParams(cv::flann::AutotunedIndexParams* val) {
			return new cv::Ptr<cv::flann::AutotunedIndexParams>(val);
	}
	
}

extern "C" {
	const cv::flann::CompositeIndexParams* cv_PtrLcv_flann_CompositeIndexParamsG_getInnerPtr_const(const cv::Ptr<cv::flann::CompositeIndexParams>* instance) {
			return instance->get();
	}
	
	cv::flann::CompositeIndexParams* cv_PtrLcv_flann_CompositeIndexParamsG_getInnerPtrMut(cv::Ptr<cv::flann::CompositeIndexParams>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_flann_CompositeIndexParamsG_delete(cv::Ptr<cv::flann::CompositeIndexParams>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::flann::IndexParams>* cv_PtrLcv_flann_CompositeIndexParamsG_to_PtrOfIndexParams(cv::Ptr<cv::flann::CompositeIndexParams>* instance) {
			return new cv::Ptr<cv::flann::IndexParams>(instance->dynamicCast<cv::flann::IndexParams>());
	}
	
	cv::Ptr<cv::flann::CompositeIndexParams>* cv_PtrLcv_flann_CompositeIndexParamsG_new_const_CompositeIndexParams(cv::flann::CompositeIndexParams* val) {
			return new cv::Ptr<cv::flann::CompositeIndexParams>(val);
	}
	
}

extern "C" {
	const cv::flann::HierarchicalClusteringIndexParams* cv_PtrLcv_flann_HierarchicalClusteringIndexParamsG_getInnerPtr_const(const cv::Ptr<cv::flann::HierarchicalClusteringIndexParams>* instance) {
			return instance->get();
	}
	
	cv::flann::HierarchicalClusteringIndexParams* cv_PtrLcv_flann_HierarchicalClusteringIndexParamsG_getInnerPtrMut(cv::Ptr<cv::flann::HierarchicalClusteringIndexParams>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_flann_HierarchicalClusteringIndexParamsG_delete(cv::Ptr<cv::flann::HierarchicalClusteringIndexParams>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::flann::IndexParams>* cv_PtrLcv_flann_HierarchicalClusteringIndexParamsG_to_PtrOfIndexParams(cv::Ptr<cv::flann::HierarchicalClusteringIndexParams>* instance) {
			return new cv::Ptr<cv::flann::IndexParams>(instance->dynamicCast<cv::flann::IndexParams>());
	}
	
	cv::Ptr<cv::flann::HierarchicalClusteringIndexParams>* cv_PtrLcv_flann_HierarchicalClusteringIndexParamsG_new_const_HierarchicalClusteringIndexParams(cv::flann::HierarchicalClusteringIndexParams* val) {
			return new cv::Ptr<cv::flann::HierarchicalClusteringIndexParams>(val);
	}
	
}

extern "C" {
	const cv::flann::IndexParams* cv_PtrLcv_flann_IndexParamsG_getInnerPtr_const(const cv::Ptr<cv::flann::IndexParams>* instance) {
			return instance->get();
	}
	
	cv::flann::IndexParams* cv_PtrLcv_flann_IndexParamsG_getInnerPtrMut(cv::Ptr<cv::flann::IndexParams>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_flann_IndexParamsG_delete(cv::Ptr<cv::flann::IndexParams>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::flann::IndexParams>* cv_PtrLcv_flann_IndexParamsG_new_const_IndexParams(cv::flann::IndexParams* val) {
			return new cv::Ptr<cv::flann::IndexParams>(val);
	}
	
}

extern "C" {
	const cv::flann::KDTreeIndexParams* cv_PtrLcv_flann_KDTreeIndexParamsG_getInnerPtr_const(const cv::Ptr<cv::flann::KDTreeIndexParams>* instance) {
			return instance->get();
	}
	
	cv::flann::KDTreeIndexParams* cv_PtrLcv_flann_KDTreeIndexParamsG_getInnerPtrMut(cv::Ptr<cv::flann::KDTreeIndexParams>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_flann_KDTreeIndexParamsG_delete(cv::Ptr<cv::flann::KDTreeIndexParams>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::flann::IndexParams>* cv_PtrLcv_flann_KDTreeIndexParamsG_to_PtrOfIndexParams(cv::Ptr<cv::flann::KDTreeIndexParams>* instance) {
			return new cv::Ptr<cv::flann::IndexParams>(instance->dynamicCast<cv::flann::IndexParams>());
	}
	
	cv::Ptr<cv::flann::KDTreeIndexParams>* cv_PtrLcv_flann_KDTreeIndexParamsG_new_const_KDTreeIndexParams(cv::flann::KDTreeIndexParams* val) {
			return new cv::Ptr<cv::flann::KDTreeIndexParams>(val);
	}
	
}

extern "C" {
	const cv::flann::KMeansIndexParams* cv_PtrLcv_flann_KMeansIndexParamsG_getInnerPtr_const(const cv::Ptr<cv::flann::KMeansIndexParams>* instance) {
			return instance->get();
	}
	
	cv::flann::KMeansIndexParams* cv_PtrLcv_flann_KMeansIndexParamsG_getInnerPtrMut(cv::Ptr<cv::flann::KMeansIndexParams>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_flann_KMeansIndexParamsG_delete(cv::Ptr<cv::flann::KMeansIndexParams>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::flann::IndexParams>* cv_PtrLcv_flann_KMeansIndexParamsG_to_PtrOfIndexParams(cv::Ptr<cv::flann::KMeansIndexParams>* instance) {
			return new cv::Ptr<cv::flann::IndexParams>(instance->dynamicCast<cv::flann::IndexParams>());
	}
	
	cv::Ptr<cv::flann::KMeansIndexParams>* cv_PtrLcv_flann_KMeansIndexParamsG_new_const_KMeansIndexParams(cv::flann::KMeansIndexParams* val) {
			return new cv::Ptr<cv::flann::KMeansIndexParams>(val);
	}
	
}

extern "C" {
	const cv::flann::LinearIndexParams* cv_PtrLcv_flann_LinearIndexParamsG_getInnerPtr_const(const cv::Ptr<cv::flann::LinearIndexParams>* instance) {
			return instance->get();
	}
	
	cv::flann::LinearIndexParams* cv_PtrLcv_flann_LinearIndexParamsG_getInnerPtrMut(cv::Ptr<cv::flann::LinearIndexParams>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_flann_LinearIndexParamsG_delete(cv::Ptr<cv::flann::LinearIndexParams>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::flann::IndexParams>* cv_PtrLcv_flann_LinearIndexParamsG_to_PtrOfIndexParams(cv::Ptr<cv::flann::LinearIndexParams>* instance) {
			return new cv::Ptr<cv::flann::IndexParams>(instance->dynamicCast<cv::flann::IndexParams>());
	}
	
	cv::Ptr<cv::flann::LinearIndexParams>* cv_PtrLcv_flann_LinearIndexParamsG_new_const_LinearIndexParams(cv::flann::LinearIndexParams* val) {
			return new cv::Ptr<cv::flann::LinearIndexParams>(val);
	}
	
}

extern "C" {
	const cv::flann::LshIndexParams* cv_PtrLcv_flann_LshIndexParamsG_getInnerPtr_const(const cv::Ptr<cv::flann::LshIndexParams>* instance) {
			return instance->get();
	}
	
	cv::flann::LshIndexParams* cv_PtrLcv_flann_LshIndexParamsG_getInnerPtrMut(cv::Ptr<cv::flann::LshIndexParams>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_flann_LshIndexParamsG_delete(cv::Ptr<cv::flann::LshIndexParams>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::flann::IndexParams>* cv_PtrLcv_flann_LshIndexParamsG_to_PtrOfIndexParams(cv::Ptr<cv::flann::LshIndexParams>* instance) {
			return new cv::Ptr<cv::flann::IndexParams>(instance->dynamicCast<cv::flann::IndexParams>());
	}
	
	cv::Ptr<cv::flann::LshIndexParams>* cv_PtrLcv_flann_LshIndexParamsG_new_const_LshIndexParams(cv::flann::LshIndexParams* val) {
			return new cv::Ptr<cv::flann::LshIndexParams>(val);
	}
	
}

extern "C" {
	const cv::flann::SavedIndexParams* cv_PtrLcv_flann_SavedIndexParamsG_getInnerPtr_const(const cv::Ptr<cv::flann::SavedIndexParams>* instance) {
			return instance->get();
	}
	
	cv::flann::SavedIndexParams* cv_PtrLcv_flann_SavedIndexParamsG_getInnerPtrMut(cv::Ptr<cv::flann::SavedIndexParams>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_flann_SavedIndexParamsG_delete(cv::Ptr<cv::flann::SavedIndexParams>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::flann::IndexParams>* cv_PtrLcv_flann_SavedIndexParamsG_to_PtrOfIndexParams(cv::Ptr<cv::flann::SavedIndexParams>* instance) {
			return new cv::Ptr<cv::flann::IndexParams>(instance->dynamicCast<cv::flann::IndexParams>());
	}
	
	cv::Ptr<cv::flann::SavedIndexParams>* cv_PtrLcv_flann_SavedIndexParamsG_new_const_SavedIndexParams(cv::flann::SavedIndexParams* val) {
			return new cv::Ptr<cv::flann::SavedIndexParams>(val);
	}
	
}

extern "C" {
	const cv::flann::SearchParams* cv_PtrLcv_flann_SearchParamsG_getInnerPtr_const(const cv::Ptr<cv::flann::SearchParams>* instance) {
			return instance->get();
	}
	
	cv::flann::SearchParams* cv_PtrLcv_flann_SearchParamsG_getInnerPtrMut(cv::Ptr<cv::flann::SearchParams>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_flann_SearchParamsG_delete(cv::Ptr<cv::flann::SearchParams>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::flann::IndexParams>* cv_PtrLcv_flann_SearchParamsG_to_PtrOfIndexParams(cv::Ptr<cv::flann::SearchParams>* instance) {
			return new cv::Ptr<cv::flann::IndexParams>(instance->dynamicCast<cv::flann::IndexParams>());
	}
	
	cv::Ptr<cv::flann::SearchParams>* cv_PtrLcv_flann_SearchParamsG_new_const_SearchParams(cv::flann::SearchParams* val) {
			return new cv::Ptr<cv::flann::SearchParams>(val);
	}
	
}

extern "C" {
	std::vector<cv::flann::FlannIndexType>* std_vectorLcv_flann_FlannIndexTypeG_new_const() {
			std::vector<cv::flann::FlannIndexType>* ret = new std::vector<cv::flann::FlannIndexType>();
			return ret;
	}
	
	void std_vectorLcv_flann_FlannIndexTypeG_delete(std::vector<cv::flann::FlannIndexType>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_flann_FlannIndexTypeG_len_const(const std::vector<cv::flann::FlannIndexType>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_flann_FlannIndexTypeG_isEmpty_const(const std::vector<cv::flann::FlannIndexType>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_flann_FlannIndexTypeG_capacity_const(const std::vector<cv::flann::FlannIndexType>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_flann_FlannIndexTypeG_shrinkToFit(std::vector<cv::flann::FlannIndexType>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_flann_FlannIndexTypeG_reserve_size_t(std::vector<cv::flann::FlannIndexType>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_flann_FlannIndexTypeG_remove_size_t(std::vector<cv::flann::FlannIndexType>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_flann_FlannIndexTypeG_swap_size_t_size_t(std::vector<cv::flann::FlannIndexType>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_flann_FlannIndexTypeG_clear(std::vector<cv::flann::FlannIndexType>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_flann_FlannIndexTypeG_push_const_FlannIndexType(std::vector<cv::flann::FlannIndexType>* instance, const cv::flann::FlannIndexType val) {
			instance->push_back(val);
	}
	
	void std_vectorLcv_flann_FlannIndexTypeG_insert_size_t_const_FlannIndexType(std::vector<cv::flann::FlannIndexType>* instance, size_t index, const cv::flann::FlannIndexType val) {
			instance->insert(instance->begin() + index, val);
	}
	
	void std_vectorLcv_flann_FlannIndexTypeG_get_const_size_t(const std::vector<cv::flann::FlannIndexType>* instance, size_t index, cv::flann::FlannIndexType* ocvrs_return) {
			cv::flann::FlannIndexType ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLcv_flann_FlannIndexTypeG_set_size_t_const_FlannIndexType(std::vector<cv::flann::FlannIndexType>* instance, size_t index, const cv::flann::FlannIndexType val) {
			(*instance)[index] = val;
	}
	
	std::vector<cv::flann::FlannIndexType>* std_vectorLcv_flann_FlannIndexTypeG_clone_const(const std::vector<cv::flann::FlannIndexType>* instance) {
			std::vector<cv::flann::FlannIndexType> ret = std::vector<cv::flann::FlannIndexType>(*instance);
			return new std::vector<cv::flann::FlannIndexType>(ret);
	}
	
	const cv::flann::FlannIndexType* std_vectorLcv_flann_FlannIndexTypeG_data_const(const std::vector<cv::flann::FlannIndexType>* instance) {
			const cv::flann::FlannIndexType* ret = instance->data();
			return ret;
	}
	
	cv::flann::FlannIndexType* std_vectorLcv_flann_FlannIndexTypeG_dataMut(std::vector<cv::flann::FlannIndexType>* instance) {
			cv::flann::FlannIndexType* ret = instance->data();
			return ret;
	}
	
	std::vector<cv::flann::FlannIndexType>* cv_fromSlice_const_const_FlannIndexTypeX_size_t(const cv::flann::FlannIndexType* data, size_t len) {
			return new std::vector<cv::flann::FlannIndexType>(data, data + len);
	}
	
}


extern "C" {
	std::vector<cvflann::lsh::FeatureIndex>* std_vectorLcvflann_lsh_FeatureIndexG_new_const() {
			std::vector<cvflann::lsh::FeatureIndex>* ret = new std::vector<cvflann::lsh::FeatureIndex>();
			return ret;
	}
	
	void std_vectorLcvflann_lsh_FeatureIndexG_delete(std::vector<cvflann::lsh::FeatureIndex>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcvflann_lsh_FeatureIndexG_len_const(const std::vector<cvflann::lsh::FeatureIndex>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcvflann_lsh_FeatureIndexG_isEmpty_const(const std::vector<cvflann::lsh::FeatureIndex>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcvflann_lsh_FeatureIndexG_capacity_const(const std::vector<cvflann::lsh::FeatureIndex>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcvflann_lsh_FeatureIndexG_shrinkToFit(std::vector<cvflann::lsh::FeatureIndex>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcvflann_lsh_FeatureIndexG_reserve_size_t(std::vector<cvflann::lsh::FeatureIndex>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcvflann_lsh_FeatureIndexG_remove_size_t(std::vector<cvflann::lsh::FeatureIndex>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcvflann_lsh_FeatureIndexG_swap_size_t_size_t(std::vector<cvflann::lsh::FeatureIndex>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcvflann_lsh_FeatureIndexG_clear(std::vector<cvflann::lsh::FeatureIndex>* instance) {
			instance->clear();
	}
	
	void std_vectorLcvflann_lsh_FeatureIndexG_push_const_FeatureIndex(std::vector<cvflann::lsh::FeatureIndex>* instance, const cvflann::lsh::FeatureIndex val) {
			instance->push_back(val);
	}
	
	void std_vectorLcvflann_lsh_FeatureIndexG_insert_size_t_const_FeatureIndex(std::vector<cvflann::lsh::FeatureIndex>* instance, size_t index, const cvflann::lsh::FeatureIndex val) {
			instance->insert(instance->begin() + index, val);
	}
	
	void std_vectorLcvflann_lsh_FeatureIndexG_get_const_size_t(const std::vector<cvflann::lsh::FeatureIndex>* instance, size_t index, cvflann::lsh::FeatureIndex* ocvrs_return) {
			cvflann::lsh::FeatureIndex ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLcvflann_lsh_FeatureIndexG_set_size_t_const_FeatureIndex(std::vector<cvflann::lsh::FeatureIndex>* instance, size_t index, const cvflann::lsh::FeatureIndex val) {
			(*instance)[index] = val;
	}
	
	std::vector<cvflann::lsh::FeatureIndex>* std_vectorLcvflann_lsh_FeatureIndexG_clone_const(const std::vector<cvflann::lsh::FeatureIndex>* instance) {
			std::vector<cvflann::lsh::FeatureIndex> ret = std::vector<cvflann::lsh::FeatureIndex>(*instance);
			return new std::vector<cvflann::lsh::FeatureIndex>(ret);
	}
	
	const cvflann::lsh::FeatureIndex* std_vectorLcvflann_lsh_FeatureIndexG_data_const(const std::vector<cvflann::lsh::FeatureIndex>* instance) {
			const cvflann::lsh::FeatureIndex* ret = instance->data();
			return ret;
	}
	
	cvflann::lsh::FeatureIndex* std_vectorLcvflann_lsh_FeatureIndexG_dataMut(std::vector<cvflann::lsh::FeatureIndex>* instance) {
			cvflann::lsh::FeatureIndex* ret = instance->data();
			return ret;
	}
	
	std::vector<cvflann::lsh::FeatureIndex>* cv_fromSlice_const_const_FeatureIndexX_size_t(const cvflann::lsh::FeatureIndex* data, size_t len) {
			return new std::vector<cvflann::lsh::FeatureIndex>(data, data + len);
	}
	
}



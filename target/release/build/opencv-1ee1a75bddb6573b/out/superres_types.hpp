extern "C" {
	const cv::superres::BroxOpticalFlow* cv_PtrLcv_superres_BroxOpticalFlowG_getInnerPtr_const(const cv::Ptr<cv::superres::BroxOpticalFlow>* instance) {
			return instance->get();
	}
	
	cv::superres::BroxOpticalFlow* cv_PtrLcv_superres_BroxOpticalFlowG_getInnerPtrMut(cv::Ptr<cv::superres::BroxOpticalFlow>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_superres_BroxOpticalFlowG_delete(cv::Ptr<cv::superres::BroxOpticalFlow>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_superres_BroxOpticalFlowG_to_PtrOfAlgorithm(cv::Ptr<cv::superres::BroxOpticalFlow>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::superres::DenseOpticalFlowExt>* cv_PtrLcv_superres_BroxOpticalFlowG_to_PtrOfSuperRes_DenseOpticalFlowExt(cv::Ptr<cv::superres::BroxOpticalFlow>* instance) {
			return new cv::Ptr<cv::superres::DenseOpticalFlowExt>(instance->dynamicCast<cv::superres::DenseOpticalFlowExt>());
	}
	
}

extern "C" {
	const cv::superres::DenseOpticalFlowExt* cv_PtrLcv_superres_DenseOpticalFlowExtG_getInnerPtr_const(const cv::Ptr<cv::superres::DenseOpticalFlowExt>* instance) {
			return instance->get();
	}
	
	cv::superres::DenseOpticalFlowExt* cv_PtrLcv_superres_DenseOpticalFlowExtG_getInnerPtrMut(cv::Ptr<cv::superres::DenseOpticalFlowExt>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_superres_DenseOpticalFlowExtG_delete(cv::Ptr<cv::superres::DenseOpticalFlowExt>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_superres_DenseOpticalFlowExtG_to_PtrOfAlgorithm(cv::Ptr<cv::superres::DenseOpticalFlowExt>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::superres::DualTVL1OpticalFlow* cv_PtrLcv_superres_DualTVL1OpticalFlowG_getInnerPtr_const(const cv::Ptr<cv::superres::DualTVL1OpticalFlow>* instance) {
			return instance->get();
	}
	
	cv::superres::DualTVL1OpticalFlow* cv_PtrLcv_superres_DualTVL1OpticalFlowG_getInnerPtrMut(cv::Ptr<cv::superres::DualTVL1OpticalFlow>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_superres_DualTVL1OpticalFlowG_delete(cv::Ptr<cv::superres::DualTVL1OpticalFlow>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_superres_DualTVL1OpticalFlowG_to_PtrOfAlgorithm(cv::Ptr<cv::superres::DualTVL1OpticalFlow>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::superres::DenseOpticalFlowExt>* cv_PtrLcv_superres_DualTVL1OpticalFlowG_to_PtrOfSuperRes_DenseOpticalFlowExt(cv::Ptr<cv::superres::DualTVL1OpticalFlow>* instance) {
			return new cv::Ptr<cv::superres::DenseOpticalFlowExt>(instance->dynamicCast<cv::superres::DenseOpticalFlowExt>());
	}
	
}

extern "C" {
	const cv::superres::FarnebackOpticalFlow* cv_PtrLcv_superres_FarnebackOpticalFlowG_getInnerPtr_const(const cv::Ptr<cv::superres::FarnebackOpticalFlow>* instance) {
			return instance->get();
	}
	
	cv::superres::FarnebackOpticalFlow* cv_PtrLcv_superres_FarnebackOpticalFlowG_getInnerPtrMut(cv::Ptr<cv::superres::FarnebackOpticalFlow>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_superres_FarnebackOpticalFlowG_delete(cv::Ptr<cv::superres::FarnebackOpticalFlow>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_superres_FarnebackOpticalFlowG_to_PtrOfAlgorithm(cv::Ptr<cv::superres::FarnebackOpticalFlow>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::superres::DenseOpticalFlowExt>* cv_PtrLcv_superres_FarnebackOpticalFlowG_to_PtrOfSuperRes_DenseOpticalFlowExt(cv::Ptr<cv::superres::FarnebackOpticalFlow>* instance) {
			return new cv::Ptr<cv::superres::DenseOpticalFlowExt>(instance->dynamicCast<cv::superres::DenseOpticalFlowExt>());
	}
	
}

extern "C" {
	const cv::superres::FrameSource* cv_PtrLcv_superres_FrameSourceG_getInnerPtr_const(const cv::Ptr<cv::superres::FrameSource>* instance) {
			return instance->get();
	}
	
	cv::superres::FrameSource* cv_PtrLcv_superres_FrameSourceG_getInnerPtrMut(cv::Ptr<cv::superres::FrameSource>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_superres_FrameSourceG_delete(cv::Ptr<cv::superres::FrameSource>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::superres::PyrLKOpticalFlow* cv_PtrLcv_superres_PyrLKOpticalFlowG_getInnerPtr_const(const cv::Ptr<cv::superres::PyrLKOpticalFlow>* instance) {
			return instance->get();
	}
	
	cv::superres::PyrLKOpticalFlow* cv_PtrLcv_superres_PyrLKOpticalFlowG_getInnerPtrMut(cv::Ptr<cv::superres::PyrLKOpticalFlow>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_superres_PyrLKOpticalFlowG_delete(cv::Ptr<cv::superres::PyrLKOpticalFlow>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_superres_PyrLKOpticalFlowG_to_PtrOfAlgorithm(cv::Ptr<cv::superres::PyrLKOpticalFlow>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::superres::DenseOpticalFlowExt>* cv_PtrLcv_superres_PyrLKOpticalFlowG_to_PtrOfSuperRes_DenseOpticalFlowExt(cv::Ptr<cv::superres::PyrLKOpticalFlow>* instance) {
			return new cv::Ptr<cv::superres::DenseOpticalFlowExt>(instance->dynamicCast<cv::superres::DenseOpticalFlowExt>());
	}
	
}

extern "C" {
	const cv::superres::SuperResolution* cv_PtrLcv_superres_SuperResolutionG_getInnerPtr_const(const cv::Ptr<cv::superres::SuperResolution>* instance) {
			return instance->get();
	}
	
	cv::superres::SuperResolution* cv_PtrLcv_superres_SuperResolutionG_getInnerPtrMut(cv::Ptr<cv::superres::SuperResolution>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_superres_SuperResolutionG_delete(cv::Ptr<cv::superres::SuperResolution>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_superres_SuperResolutionG_to_PtrOfAlgorithm(cv::Ptr<cv::superres::SuperResolution>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::superres::FrameSource>* cv_PtrLcv_superres_SuperResolutionG_to_PtrOfSuperRes_FrameSource(cv::Ptr<cv::superres::SuperResolution>* instance) {
			return new cv::Ptr<cv::superres::FrameSource>(instance->dynamicCast<cv::superres::FrameSource>());
	}
	
}


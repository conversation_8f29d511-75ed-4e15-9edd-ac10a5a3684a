extern "C" {
	const cv::CLAHE* cv_PtrLcv_CLAHEG_getInnerPtr_const(const cv::Ptr<cv::CLAHE>* instance) {
			return instance->get();
	}
	
	cv::CLAHE* cv_PtrLcv_CLAHEG_getInnerPtrMut(cv::Ptr<cv::CLAHE>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_CLAHEG_delete(cv::Ptr<cv::CLAHE>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_CLAHEG_to_PtrOfAlgorithm(cv::Ptr<cv::CLAHE>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::GeneralizedHough* cv_PtrLcv_GeneralizedHoughG_getInnerPtr_const(const cv::Ptr<cv::GeneralizedHough>* instance) {
			return instance->get();
	}
	
	cv::GeneralizedHough* cv_PtrLcv_GeneralizedHoughG_getInnerPtrMut(cv::Ptr<cv::GeneralizedHough>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_GeneralizedHoughG_delete(cv::Ptr<cv::GeneralizedHough>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_GeneralizedHoughG_to_PtrOfAlgorithm(cv::Ptr<cv::GeneralizedHough>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::GeneralizedHoughBallard* cv_PtrLcv_GeneralizedHoughBallardG_getInnerPtr_const(const cv::Ptr<cv::GeneralizedHoughBallard>* instance) {
			return instance->get();
	}
	
	cv::GeneralizedHoughBallard* cv_PtrLcv_GeneralizedHoughBallardG_getInnerPtrMut(cv::Ptr<cv::GeneralizedHoughBallard>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_GeneralizedHoughBallardG_delete(cv::Ptr<cv::GeneralizedHoughBallard>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_GeneralizedHoughBallardG_to_PtrOfAlgorithm(cv::Ptr<cv::GeneralizedHoughBallard>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::GeneralizedHough>* cv_PtrLcv_GeneralizedHoughBallardG_to_PtrOfGeneralizedHough(cv::Ptr<cv::GeneralizedHoughBallard>* instance) {
			return new cv::Ptr<cv::GeneralizedHough>(instance->dynamicCast<cv::GeneralizedHough>());
	}
	
}

extern "C" {
	const cv::GeneralizedHoughGuil* cv_PtrLcv_GeneralizedHoughGuilG_getInnerPtr_const(const cv::Ptr<cv::GeneralizedHoughGuil>* instance) {
			return instance->get();
	}
	
	cv::GeneralizedHoughGuil* cv_PtrLcv_GeneralizedHoughGuilG_getInnerPtrMut(cv::Ptr<cv::GeneralizedHoughGuil>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_GeneralizedHoughGuilG_delete(cv::Ptr<cv::GeneralizedHoughGuil>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_GeneralizedHoughGuilG_to_PtrOfAlgorithm(cv::Ptr<cv::GeneralizedHoughGuil>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::GeneralizedHough>* cv_PtrLcv_GeneralizedHoughGuilG_to_PtrOfGeneralizedHough(cv::Ptr<cv::GeneralizedHoughGuil>* instance) {
			return new cv::Ptr<cv::GeneralizedHough>(instance->dynamicCast<cv::GeneralizedHough>());
	}
	
}

extern "C" {
	const cv::LineSegmentDetector* cv_PtrLcv_LineSegmentDetectorG_getInnerPtr_const(const cv::Ptr<cv::LineSegmentDetector>* instance) {
			return instance->get();
	}
	
	cv::LineSegmentDetector* cv_PtrLcv_LineSegmentDetectorG_getInnerPtrMut(cv::Ptr<cv::LineSegmentDetector>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_LineSegmentDetectorG_delete(cv::Ptr<cv::LineSegmentDetector>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_LineSegmentDetectorG_to_PtrOfAlgorithm(cv::Ptr<cv::LineSegmentDetector>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}


extern "C" {
	const cv::AffineWarper* cv_PtrLcv_AffineWarperG_getInnerPtr_const(const cv::Ptr<cv::AffineWarper>* instance) {
			return instance->get();
	}
	
	cv::AffineWarper* cv_PtrLcv_AffineWarperG_getInnerPtrMut(cv::Ptr<cv::AffineWarper>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_AffineWarperG_delete(cv::Ptr<cv::AffineWarper>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::WarperCreator>* cv_PtrLcv_AffineWarperG_to_PtrOfWarperCreator(cv::Ptr<cv::AffineWarper>* instance) {
			return new cv::Ptr<cv::WarperCreator>(instance->dynamicCast<cv::WarperCreator>());
	}
	
	cv::Ptr<cv::AffineWarper>* cv_PtrLcv_AffineWarperG_new_const_AffineWarper(cv::AffineWarper* val) {
			return new cv::Ptr<cv::AffineWarper>(val);
	}
	
}

extern "C" {
	const cv::CompressedRectilinearPortraitWarper* cv_PtrLcv_CompressedRectilinearPortraitWarperG_getInnerPtr_const(const cv::Ptr<cv::CompressedRectilinearPortraitWarper>* instance) {
			return instance->get();
	}
	
	cv::CompressedRectilinearPortraitWarper* cv_PtrLcv_CompressedRectilinearPortraitWarperG_getInnerPtrMut(cv::Ptr<cv::CompressedRectilinearPortraitWarper>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_CompressedRectilinearPortraitWarperG_delete(cv::Ptr<cv::CompressedRectilinearPortraitWarper>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::WarperCreator>* cv_PtrLcv_CompressedRectilinearPortraitWarperG_to_PtrOfWarperCreator(cv::Ptr<cv::CompressedRectilinearPortraitWarper>* instance) {
			return new cv::Ptr<cv::WarperCreator>(instance->dynamicCast<cv::WarperCreator>());
	}
	
	cv::Ptr<cv::CompressedRectilinearPortraitWarper>* cv_PtrLcv_CompressedRectilinearPortraitWarperG_new_const_CompressedRectilinearPortraitWarper(cv::CompressedRectilinearPortraitWarper* val) {
			return new cv::Ptr<cv::CompressedRectilinearPortraitWarper>(val);
	}
	
}

extern "C" {
	const cv::CompressedRectilinearWarper* cv_PtrLcv_CompressedRectilinearWarperG_getInnerPtr_const(const cv::Ptr<cv::CompressedRectilinearWarper>* instance) {
			return instance->get();
	}
	
	cv::CompressedRectilinearWarper* cv_PtrLcv_CompressedRectilinearWarperG_getInnerPtrMut(cv::Ptr<cv::CompressedRectilinearWarper>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_CompressedRectilinearWarperG_delete(cv::Ptr<cv::CompressedRectilinearWarper>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::WarperCreator>* cv_PtrLcv_CompressedRectilinearWarperG_to_PtrOfWarperCreator(cv::Ptr<cv::CompressedRectilinearWarper>* instance) {
			return new cv::Ptr<cv::WarperCreator>(instance->dynamicCast<cv::WarperCreator>());
	}
	
	cv::Ptr<cv::CompressedRectilinearWarper>* cv_PtrLcv_CompressedRectilinearWarperG_new_const_CompressedRectilinearWarper(cv::CompressedRectilinearWarper* val) {
			return new cv::Ptr<cv::CompressedRectilinearWarper>(val);
	}
	
}

extern "C" {
	const cv::CylindricalWarper* cv_PtrLcv_CylindricalWarperG_getInnerPtr_const(const cv::Ptr<cv::CylindricalWarper>* instance) {
			return instance->get();
	}
	
	cv::CylindricalWarper* cv_PtrLcv_CylindricalWarperG_getInnerPtrMut(cv::Ptr<cv::CylindricalWarper>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_CylindricalWarperG_delete(cv::Ptr<cv::CylindricalWarper>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::WarperCreator>* cv_PtrLcv_CylindricalWarperG_to_PtrOfWarperCreator(cv::Ptr<cv::CylindricalWarper>* instance) {
			return new cv::Ptr<cv::WarperCreator>(instance->dynamicCast<cv::WarperCreator>());
	}
	
	cv::Ptr<cv::CylindricalWarper>* cv_PtrLcv_CylindricalWarperG_new_const_CylindricalWarper(cv::CylindricalWarper* val) {
			return new cv::Ptr<cv::CylindricalWarper>(val);
	}
	
}

extern "C" {
	const cv::detail::AffineBasedEstimator* cv_PtrLcv_detail_AffineBasedEstimatorG_getInnerPtr_const(const cv::Ptr<cv::detail::AffineBasedEstimator>* instance) {
			return instance->get();
	}
	
	cv::detail::AffineBasedEstimator* cv_PtrLcv_detail_AffineBasedEstimatorG_getInnerPtrMut(cv::Ptr<cv::detail::AffineBasedEstimator>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_AffineBasedEstimatorG_delete(cv::Ptr<cv::detail::AffineBasedEstimator>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::detail::Estimator>* cv_PtrLcv_detail_AffineBasedEstimatorG_to_PtrOfDetail_Estimator(cv::Ptr<cv::detail::AffineBasedEstimator>* instance) {
			return new cv::Ptr<cv::detail::Estimator>(instance->dynamicCast<cv::detail::Estimator>());
	}
	
	cv::Ptr<cv::detail::AffineBasedEstimator>* cv_PtrLcv_detail_AffineBasedEstimatorG_new_const_AffineBasedEstimator(cv::detail::AffineBasedEstimator* val) {
			return new cv::Ptr<cv::detail::AffineBasedEstimator>(val);
	}
	
}

extern "C" {
	const cv::detail::AffineBestOf2NearestMatcher* cv_PtrLcv_detail_AffineBestOf2NearestMatcherG_getInnerPtr_const(const cv::Ptr<cv::detail::AffineBestOf2NearestMatcher>* instance) {
			return instance->get();
	}
	
	cv::detail::AffineBestOf2NearestMatcher* cv_PtrLcv_detail_AffineBestOf2NearestMatcherG_getInnerPtrMut(cv::Ptr<cv::detail::AffineBestOf2NearestMatcher>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_AffineBestOf2NearestMatcherG_delete(cv::Ptr<cv::detail::AffineBestOf2NearestMatcher>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::detail::BestOf2NearestMatcher>* cv_PtrLcv_detail_AffineBestOf2NearestMatcherG_to_PtrOfDetail_BestOf2NearestMatcher(cv::Ptr<cv::detail::AffineBestOf2NearestMatcher>* instance) {
			return new cv::Ptr<cv::detail::BestOf2NearestMatcher>(instance->dynamicCast<cv::detail::BestOf2NearestMatcher>());
	}
	
	cv::Ptr<cv::detail::FeaturesMatcher>* cv_PtrLcv_detail_AffineBestOf2NearestMatcherG_to_PtrOfDetail_FeaturesMatcher(cv::Ptr<cv::detail::AffineBestOf2NearestMatcher>* instance) {
			return new cv::Ptr<cv::detail::FeaturesMatcher>(instance->dynamicCast<cv::detail::FeaturesMatcher>());
	}
	
	cv::Ptr<cv::detail::AffineBestOf2NearestMatcher>* cv_PtrLcv_detail_AffineBestOf2NearestMatcherG_new_const_AffineBestOf2NearestMatcher(cv::detail::AffineBestOf2NearestMatcher* val) {
			return new cv::Ptr<cv::detail::AffineBestOf2NearestMatcher>(val);
	}
	
}

extern "C" {
	const cv::detail::BestOf2NearestMatcher* cv_PtrLcv_detail_BestOf2NearestMatcherG_getInnerPtr_const(const cv::Ptr<cv::detail::BestOf2NearestMatcher>* instance) {
			return instance->get();
	}
	
	cv::detail::BestOf2NearestMatcher* cv_PtrLcv_detail_BestOf2NearestMatcherG_getInnerPtrMut(cv::Ptr<cv::detail::BestOf2NearestMatcher>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_BestOf2NearestMatcherG_delete(cv::Ptr<cv::detail::BestOf2NearestMatcher>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::detail::FeaturesMatcher>* cv_PtrLcv_detail_BestOf2NearestMatcherG_to_PtrOfDetail_FeaturesMatcher(cv::Ptr<cv::detail::BestOf2NearestMatcher>* instance) {
			return new cv::Ptr<cv::detail::FeaturesMatcher>(instance->dynamicCast<cv::detail::FeaturesMatcher>());
	}
	
	cv::Ptr<cv::detail::BestOf2NearestMatcher>* cv_PtrLcv_detail_BestOf2NearestMatcherG_new_const_BestOf2NearestMatcher(cv::detail::BestOf2NearestMatcher* val) {
			return new cv::Ptr<cv::detail::BestOf2NearestMatcher>(val);
	}
	
}

extern "C" {
	const cv::detail::BestOf2NearestRangeMatcher* cv_PtrLcv_detail_BestOf2NearestRangeMatcherG_getInnerPtr_const(const cv::Ptr<cv::detail::BestOf2NearestRangeMatcher>* instance) {
			return instance->get();
	}
	
	cv::detail::BestOf2NearestRangeMatcher* cv_PtrLcv_detail_BestOf2NearestRangeMatcherG_getInnerPtrMut(cv::Ptr<cv::detail::BestOf2NearestRangeMatcher>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_BestOf2NearestRangeMatcherG_delete(cv::Ptr<cv::detail::BestOf2NearestRangeMatcher>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::detail::BestOf2NearestMatcher>* cv_PtrLcv_detail_BestOf2NearestRangeMatcherG_to_PtrOfDetail_BestOf2NearestMatcher(cv::Ptr<cv::detail::BestOf2NearestRangeMatcher>* instance) {
			return new cv::Ptr<cv::detail::BestOf2NearestMatcher>(instance->dynamicCast<cv::detail::BestOf2NearestMatcher>());
	}
	
	cv::Ptr<cv::detail::FeaturesMatcher>* cv_PtrLcv_detail_BestOf2NearestRangeMatcherG_to_PtrOfDetail_FeaturesMatcher(cv::Ptr<cv::detail::BestOf2NearestRangeMatcher>* instance) {
			return new cv::Ptr<cv::detail::FeaturesMatcher>(instance->dynamicCast<cv::detail::FeaturesMatcher>());
	}
	
	cv::Ptr<cv::detail::BestOf2NearestRangeMatcher>* cv_PtrLcv_detail_BestOf2NearestRangeMatcherG_new_const_BestOf2NearestRangeMatcher(cv::detail::BestOf2NearestRangeMatcher* val) {
			return new cv::Ptr<cv::detail::BestOf2NearestRangeMatcher>(val);
	}
	
}

extern "C" {
	const cv::detail::Blender* cv_PtrLcv_detail_BlenderG_getInnerPtr_const(const cv::Ptr<cv::detail::Blender>* instance) {
			return instance->get();
	}
	
	cv::detail::Blender* cv_PtrLcv_detail_BlenderG_getInnerPtrMut(cv::Ptr<cv::detail::Blender>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_BlenderG_delete(cv::Ptr<cv::detail::Blender>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::detail::Blender>* cv_PtrLcv_detail_BlenderG_new_const_Blender(cv::detail::Blender* val) {
			return new cv::Ptr<cv::detail::Blender>(val);
	}
	
}

extern "C" {
	const cv::detail::BlocksChannelsCompensator* cv_PtrLcv_detail_BlocksChannelsCompensatorG_getInnerPtr_const(const cv::Ptr<cv::detail::BlocksChannelsCompensator>* instance) {
			return instance->get();
	}
	
	cv::detail::BlocksChannelsCompensator* cv_PtrLcv_detail_BlocksChannelsCompensatorG_getInnerPtrMut(cv::Ptr<cv::detail::BlocksChannelsCompensator>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_BlocksChannelsCompensatorG_delete(cv::Ptr<cv::detail::BlocksChannelsCompensator>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::detail::BlocksCompensator>* cv_PtrLcv_detail_BlocksChannelsCompensatorG_to_PtrOfDetail_BlocksCompensator(cv::Ptr<cv::detail::BlocksChannelsCompensator>* instance) {
			return new cv::Ptr<cv::detail::BlocksCompensator>(instance->dynamicCast<cv::detail::BlocksCompensator>());
	}
	
	cv::Ptr<cv::detail::ExposureCompensator>* cv_PtrLcv_detail_BlocksChannelsCompensatorG_to_PtrOfDetail_ExposureCompensator(cv::Ptr<cv::detail::BlocksChannelsCompensator>* instance) {
			return new cv::Ptr<cv::detail::ExposureCompensator>(instance->dynamicCast<cv::detail::ExposureCompensator>());
	}
	
	cv::Ptr<cv::detail::BlocksChannelsCompensator>* cv_PtrLcv_detail_BlocksChannelsCompensatorG_new_const_BlocksChannelsCompensator(cv::detail::BlocksChannelsCompensator* val) {
			return new cv::Ptr<cv::detail::BlocksChannelsCompensator>(val);
	}
	
}

extern "C" {
	const cv::detail::BlocksCompensator* cv_PtrLcv_detail_BlocksCompensatorG_getInnerPtr_const(const cv::Ptr<cv::detail::BlocksCompensator>* instance) {
			return instance->get();
	}
	
	cv::detail::BlocksCompensator* cv_PtrLcv_detail_BlocksCompensatorG_getInnerPtrMut(cv::Ptr<cv::detail::BlocksCompensator>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_BlocksCompensatorG_delete(cv::Ptr<cv::detail::BlocksCompensator>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::detail::ExposureCompensator>* cv_PtrLcv_detail_BlocksCompensatorG_to_PtrOfDetail_ExposureCompensator(cv::Ptr<cv::detail::BlocksCompensator>* instance) {
			return new cv::Ptr<cv::detail::ExposureCompensator>(instance->dynamicCast<cv::detail::ExposureCompensator>());
	}
	
}

extern "C" {
	const cv::detail::BlocksGainCompensator* cv_PtrLcv_detail_BlocksGainCompensatorG_getInnerPtr_const(const cv::Ptr<cv::detail::BlocksGainCompensator>* instance) {
			return instance->get();
	}
	
	cv::detail::BlocksGainCompensator* cv_PtrLcv_detail_BlocksGainCompensatorG_getInnerPtrMut(cv::Ptr<cv::detail::BlocksGainCompensator>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_BlocksGainCompensatorG_delete(cv::Ptr<cv::detail::BlocksGainCompensator>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::detail::BlocksCompensator>* cv_PtrLcv_detail_BlocksGainCompensatorG_to_PtrOfDetail_BlocksCompensator(cv::Ptr<cv::detail::BlocksGainCompensator>* instance) {
			return new cv::Ptr<cv::detail::BlocksCompensator>(instance->dynamicCast<cv::detail::BlocksCompensator>());
	}
	
	cv::Ptr<cv::detail::ExposureCompensator>* cv_PtrLcv_detail_BlocksGainCompensatorG_to_PtrOfDetail_ExposureCompensator(cv::Ptr<cv::detail::BlocksGainCompensator>* instance) {
			return new cv::Ptr<cv::detail::ExposureCompensator>(instance->dynamicCast<cv::detail::ExposureCompensator>());
	}
	
	cv::Ptr<cv::detail::BlocksGainCompensator>* cv_PtrLcv_detail_BlocksGainCompensatorG_new_const_BlocksGainCompensator(cv::detail::BlocksGainCompensator* val) {
			return new cv::Ptr<cv::detail::BlocksGainCompensator>(val);
	}
	
}

extern "C" {
	const cv::detail::BundleAdjusterAffine* cv_PtrLcv_detail_BundleAdjusterAffineG_getInnerPtr_const(const cv::Ptr<cv::detail::BundleAdjusterAffine>* instance) {
			return instance->get();
	}
	
	cv::detail::BundleAdjusterAffine* cv_PtrLcv_detail_BundleAdjusterAffineG_getInnerPtrMut(cv::Ptr<cv::detail::BundleAdjusterAffine>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_BundleAdjusterAffineG_delete(cv::Ptr<cv::detail::BundleAdjusterAffine>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::detail::BundleAdjusterBase>* cv_PtrLcv_detail_BundleAdjusterAffineG_to_PtrOfDetail_BundleAdjusterBase(cv::Ptr<cv::detail::BundleAdjusterAffine>* instance) {
			return new cv::Ptr<cv::detail::BundleAdjusterBase>(instance->dynamicCast<cv::detail::BundleAdjusterBase>());
	}
	
	cv::Ptr<cv::detail::Estimator>* cv_PtrLcv_detail_BundleAdjusterAffineG_to_PtrOfDetail_Estimator(cv::Ptr<cv::detail::BundleAdjusterAffine>* instance) {
			return new cv::Ptr<cv::detail::Estimator>(instance->dynamicCast<cv::detail::Estimator>());
	}
	
	cv::Ptr<cv::detail::BundleAdjusterAffine>* cv_PtrLcv_detail_BundleAdjusterAffineG_new_const_BundleAdjusterAffine(cv::detail::BundleAdjusterAffine* val) {
			return new cv::Ptr<cv::detail::BundleAdjusterAffine>(val);
	}
	
}

extern "C" {
	const cv::detail::BundleAdjusterAffinePartial* cv_PtrLcv_detail_BundleAdjusterAffinePartialG_getInnerPtr_const(const cv::Ptr<cv::detail::BundleAdjusterAffinePartial>* instance) {
			return instance->get();
	}
	
	cv::detail::BundleAdjusterAffinePartial* cv_PtrLcv_detail_BundleAdjusterAffinePartialG_getInnerPtrMut(cv::Ptr<cv::detail::BundleAdjusterAffinePartial>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_BundleAdjusterAffinePartialG_delete(cv::Ptr<cv::detail::BundleAdjusterAffinePartial>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::detail::BundleAdjusterBase>* cv_PtrLcv_detail_BundleAdjusterAffinePartialG_to_PtrOfDetail_BundleAdjusterBase(cv::Ptr<cv::detail::BundleAdjusterAffinePartial>* instance) {
			return new cv::Ptr<cv::detail::BundleAdjusterBase>(instance->dynamicCast<cv::detail::BundleAdjusterBase>());
	}
	
	cv::Ptr<cv::detail::Estimator>* cv_PtrLcv_detail_BundleAdjusterAffinePartialG_to_PtrOfDetail_Estimator(cv::Ptr<cv::detail::BundleAdjusterAffinePartial>* instance) {
			return new cv::Ptr<cv::detail::Estimator>(instance->dynamicCast<cv::detail::Estimator>());
	}
	
	cv::Ptr<cv::detail::BundleAdjusterAffinePartial>* cv_PtrLcv_detail_BundleAdjusterAffinePartialG_new_const_BundleAdjusterAffinePartial(cv::detail::BundleAdjusterAffinePartial* val) {
			return new cv::Ptr<cv::detail::BundleAdjusterAffinePartial>(val);
	}
	
}

extern "C" {
	const cv::detail::BundleAdjusterBase* cv_PtrLcv_detail_BundleAdjusterBaseG_getInnerPtr_const(const cv::Ptr<cv::detail::BundleAdjusterBase>* instance) {
			return instance->get();
	}
	
	cv::detail::BundleAdjusterBase* cv_PtrLcv_detail_BundleAdjusterBaseG_getInnerPtrMut(cv::Ptr<cv::detail::BundleAdjusterBase>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_BundleAdjusterBaseG_delete(cv::Ptr<cv::detail::BundleAdjusterBase>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::detail::Estimator>* cv_PtrLcv_detail_BundleAdjusterBaseG_to_PtrOfDetail_Estimator(cv::Ptr<cv::detail::BundleAdjusterBase>* instance) {
			return new cv::Ptr<cv::detail::Estimator>(instance->dynamicCast<cv::detail::Estimator>());
	}
	
}

extern "C" {
	const cv::detail::BundleAdjusterRay* cv_PtrLcv_detail_BundleAdjusterRayG_getInnerPtr_const(const cv::Ptr<cv::detail::BundleAdjusterRay>* instance) {
			return instance->get();
	}
	
	cv::detail::BundleAdjusterRay* cv_PtrLcv_detail_BundleAdjusterRayG_getInnerPtrMut(cv::Ptr<cv::detail::BundleAdjusterRay>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_BundleAdjusterRayG_delete(cv::Ptr<cv::detail::BundleAdjusterRay>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::detail::BundleAdjusterBase>* cv_PtrLcv_detail_BundleAdjusterRayG_to_PtrOfDetail_BundleAdjusterBase(cv::Ptr<cv::detail::BundleAdjusterRay>* instance) {
			return new cv::Ptr<cv::detail::BundleAdjusterBase>(instance->dynamicCast<cv::detail::BundleAdjusterBase>());
	}
	
	cv::Ptr<cv::detail::Estimator>* cv_PtrLcv_detail_BundleAdjusterRayG_to_PtrOfDetail_Estimator(cv::Ptr<cv::detail::BundleAdjusterRay>* instance) {
			return new cv::Ptr<cv::detail::Estimator>(instance->dynamicCast<cv::detail::Estimator>());
	}
	
	cv::Ptr<cv::detail::BundleAdjusterRay>* cv_PtrLcv_detail_BundleAdjusterRayG_new_const_BundleAdjusterRay(cv::detail::BundleAdjusterRay* val) {
			return new cv::Ptr<cv::detail::BundleAdjusterRay>(val);
	}
	
}

extern "C" {
	const cv::detail::BundleAdjusterReproj* cv_PtrLcv_detail_BundleAdjusterReprojG_getInnerPtr_const(const cv::Ptr<cv::detail::BundleAdjusterReproj>* instance) {
			return instance->get();
	}
	
	cv::detail::BundleAdjusterReproj* cv_PtrLcv_detail_BundleAdjusterReprojG_getInnerPtrMut(cv::Ptr<cv::detail::BundleAdjusterReproj>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_BundleAdjusterReprojG_delete(cv::Ptr<cv::detail::BundleAdjusterReproj>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::detail::BundleAdjusterBase>* cv_PtrLcv_detail_BundleAdjusterReprojG_to_PtrOfDetail_BundleAdjusterBase(cv::Ptr<cv::detail::BundleAdjusterReproj>* instance) {
			return new cv::Ptr<cv::detail::BundleAdjusterBase>(instance->dynamicCast<cv::detail::BundleAdjusterBase>());
	}
	
	cv::Ptr<cv::detail::Estimator>* cv_PtrLcv_detail_BundleAdjusterReprojG_to_PtrOfDetail_Estimator(cv::Ptr<cv::detail::BundleAdjusterReproj>* instance) {
			return new cv::Ptr<cv::detail::Estimator>(instance->dynamicCast<cv::detail::Estimator>());
	}
	
	cv::Ptr<cv::detail::BundleAdjusterReproj>* cv_PtrLcv_detail_BundleAdjusterReprojG_new_const_BundleAdjusterReproj(cv::detail::BundleAdjusterReproj* val) {
			return new cv::Ptr<cv::detail::BundleAdjusterReproj>(val);
	}
	
}

extern "C" {
	const cv::detail::ChannelsCompensator* cv_PtrLcv_detail_ChannelsCompensatorG_getInnerPtr_const(const cv::Ptr<cv::detail::ChannelsCompensator>* instance) {
			return instance->get();
	}
	
	cv::detail::ChannelsCompensator* cv_PtrLcv_detail_ChannelsCompensatorG_getInnerPtrMut(cv::Ptr<cv::detail::ChannelsCompensator>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_ChannelsCompensatorG_delete(cv::Ptr<cv::detail::ChannelsCompensator>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::detail::ExposureCompensator>* cv_PtrLcv_detail_ChannelsCompensatorG_to_PtrOfDetail_ExposureCompensator(cv::Ptr<cv::detail::ChannelsCompensator>* instance) {
			return new cv::Ptr<cv::detail::ExposureCompensator>(instance->dynamicCast<cv::detail::ExposureCompensator>());
	}
	
	cv::Ptr<cv::detail::ChannelsCompensator>* cv_PtrLcv_detail_ChannelsCompensatorG_new_const_ChannelsCompensator(cv::detail::ChannelsCompensator* val) {
			return new cv::Ptr<cv::detail::ChannelsCompensator>(val);
	}
	
}

extern "C" {
	const cv::detail::DpSeamFinder* cv_PtrLcv_detail_DpSeamFinderG_getInnerPtr_const(const cv::Ptr<cv::detail::DpSeamFinder>* instance) {
			return instance->get();
	}
	
	cv::detail::DpSeamFinder* cv_PtrLcv_detail_DpSeamFinderG_getInnerPtrMut(cv::Ptr<cv::detail::DpSeamFinder>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_DpSeamFinderG_delete(cv::Ptr<cv::detail::DpSeamFinder>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::detail::SeamFinder>* cv_PtrLcv_detail_DpSeamFinderG_to_PtrOfDetail_SeamFinder(cv::Ptr<cv::detail::DpSeamFinder>* instance) {
			return new cv::Ptr<cv::detail::SeamFinder>(instance->dynamicCast<cv::detail::SeamFinder>());
	}
	
	cv::Ptr<cv::detail::DpSeamFinder>* cv_PtrLcv_detail_DpSeamFinderG_new_const_DpSeamFinder(cv::detail::DpSeamFinder* val) {
			return new cv::Ptr<cv::detail::DpSeamFinder>(val);
	}
	
}

extern "C" {
	const cv::detail::Estimator* cv_PtrLcv_detail_EstimatorG_getInnerPtr_const(const cv::Ptr<cv::detail::Estimator>* instance) {
			return instance->get();
	}
	
	cv::detail::Estimator* cv_PtrLcv_detail_EstimatorG_getInnerPtrMut(cv::Ptr<cv::detail::Estimator>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_EstimatorG_delete(cv::Ptr<cv::detail::Estimator>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::detail::ExposureCompensator* cv_PtrLcv_detail_ExposureCompensatorG_getInnerPtr_const(const cv::Ptr<cv::detail::ExposureCompensator>* instance) {
			return instance->get();
	}
	
	cv::detail::ExposureCompensator* cv_PtrLcv_detail_ExposureCompensatorG_getInnerPtrMut(cv::Ptr<cv::detail::ExposureCompensator>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_ExposureCompensatorG_delete(cv::Ptr<cv::detail::ExposureCompensator>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::detail::FeatherBlender* cv_PtrLcv_detail_FeatherBlenderG_getInnerPtr_const(const cv::Ptr<cv::detail::FeatherBlender>* instance) {
			return instance->get();
	}
	
	cv::detail::FeatherBlender* cv_PtrLcv_detail_FeatherBlenderG_getInnerPtrMut(cv::Ptr<cv::detail::FeatherBlender>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_FeatherBlenderG_delete(cv::Ptr<cv::detail::FeatherBlender>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::detail::Blender>* cv_PtrLcv_detail_FeatherBlenderG_to_PtrOfDetail_Blender(cv::Ptr<cv::detail::FeatherBlender>* instance) {
			return new cv::Ptr<cv::detail::Blender>(instance->dynamicCast<cv::detail::Blender>());
	}
	
	cv::Ptr<cv::detail::FeatherBlender>* cv_PtrLcv_detail_FeatherBlenderG_new_const_FeatherBlender(cv::detail::FeatherBlender* val) {
			return new cv::Ptr<cv::detail::FeatherBlender>(val);
	}
	
}

extern "C" {
	const cv::detail::FeaturesMatcher* cv_PtrLcv_detail_FeaturesMatcherG_getInnerPtr_const(const cv::Ptr<cv::detail::FeaturesMatcher>* instance) {
			return instance->get();
	}
	
	cv::detail::FeaturesMatcher* cv_PtrLcv_detail_FeaturesMatcherG_getInnerPtrMut(cv::Ptr<cv::detail::FeaturesMatcher>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_FeaturesMatcherG_delete(cv::Ptr<cv::detail::FeaturesMatcher>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::detail::GainCompensator* cv_PtrLcv_detail_GainCompensatorG_getInnerPtr_const(const cv::Ptr<cv::detail::GainCompensator>* instance) {
			return instance->get();
	}
	
	cv::detail::GainCompensator* cv_PtrLcv_detail_GainCompensatorG_getInnerPtrMut(cv::Ptr<cv::detail::GainCompensator>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_GainCompensatorG_delete(cv::Ptr<cv::detail::GainCompensator>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::detail::ExposureCompensator>* cv_PtrLcv_detail_GainCompensatorG_to_PtrOfDetail_ExposureCompensator(cv::Ptr<cv::detail::GainCompensator>* instance) {
			return new cv::Ptr<cv::detail::ExposureCompensator>(instance->dynamicCast<cv::detail::ExposureCompensator>());
	}
	
	cv::Ptr<cv::detail::GainCompensator>* cv_PtrLcv_detail_GainCompensatorG_new_const_GainCompensator(cv::detail::GainCompensator* val) {
			return new cv::Ptr<cv::detail::GainCompensator>(val);
	}
	
}

extern "C" {
	const cv::detail::GraphCutSeamFinder* cv_PtrLcv_detail_GraphCutSeamFinderG_getInnerPtr_const(const cv::Ptr<cv::detail::GraphCutSeamFinder>* instance) {
			return instance->get();
	}
	
	cv::detail::GraphCutSeamFinder* cv_PtrLcv_detail_GraphCutSeamFinderG_getInnerPtrMut(cv::Ptr<cv::detail::GraphCutSeamFinder>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_GraphCutSeamFinderG_delete(cv::Ptr<cv::detail::GraphCutSeamFinder>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::detail::GraphCutSeamFinderBase>* cv_PtrLcv_detail_GraphCutSeamFinderG_to_PtrOfDetail_GraphCutSeamFinderBase(cv::Ptr<cv::detail::GraphCutSeamFinder>* instance) {
			return new cv::Ptr<cv::detail::GraphCutSeamFinderBase>(instance->dynamicCast<cv::detail::GraphCutSeamFinderBase>());
	}
	
	cv::Ptr<cv::detail::SeamFinder>* cv_PtrLcv_detail_GraphCutSeamFinderG_to_PtrOfDetail_SeamFinder(cv::Ptr<cv::detail::GraphCutSeamFinder>* instance) {
			return new cv::Ptr<cv::detail::SeamFinder>(instance->dynamicCast<cv::detail::SeamFinder>());
	}
	
	cv::Ptr<cv::detail::GraphCutSeamFinder>* cv_PtrLcv_detail_GraphCutSeamFinderG_new_const_GraphCutSeamFinder(cv::detail::GraphCutSeamFinder* val) {
			return new cv::Ptr<cv::detail::GraphCutSeamFinder>(val);
	}
	
}

extern "C" {
	const cv::detail::GraphCutSeamFinderBase* cv_PtrLcv_detail_GraphCutSeamFinderBaseG_getInnerPtr_const(const cv::Ptr<cv::detail::GraphCutSeamFinderBase>* instance) {
			return instance->get();
	}
	
	cv::detail::GraphCutSeamFinderBase* cv_PtrLcv_detail_GraphCutSeamFinderBaseG_getInnerPtrMut(cv::Ptr<cv::detail::GraphCutSeamFinderBase>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_GraphCutSeamFinderBaseG_delete(cv::Ptr<cv::detail::GraphCutSeamFinderBase>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::detail::GraphCutSeamFinderBase>* cv_PtrLcv_detail_GraphCutSeamFinderBaseG_new_const_GraphCutSeamFinderBase(cv::detail::GraphCutSeamFinderBase* val) {
			return new cv::Ptr<cv::detail::GraphCutSeamFinderBase>(val);
	}
	
}

extern "C" {
	const cv::detail::HomographyBasedEstimator* cv_PtrLcv_detail_HomographyBasedEstimatorG_getInnerPtr_const(const cv::Ptr<cv::detail::HomographyBasedEstimator>* instance) {
			return instance->get();
	}
	
	cv::detail::HomographyBasedEstimator* cv_PtrLcv_detail_HomographyBasedEstimatorG_getInnerPtrMut(cv::Ptr<cv::detail::HomographyBasedEstimator>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_HomographyBasedEstimatorG_delete(cv::Ptr<cv::detail::HomographyBasedEstimator>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::detail::Estimator>* cv_PtrLcv_detail_HomographyBasedEstimatorG_to_PtrOfDetail_Estimator(cv::Ptr<cv::detail::HomographyBasedEstimator>* instance) {
			return new cv::Ptr<cv::detail::Estimator>(instance->dynamicCast<cv::detail::Estimator>());
	}
	
	cv::Ptr<cv::detail::HomographyBasedEstimator>* cv_PtrLcv_detail_HomographyBasedEstimatorG_new_const_HomographyBasedEstimator(cv::detail::HomographyBasedEstimator* val) {
			return new cv::Ptr<cv::detail::HomographyBasedEstimator>(val);
	}
	
}

extern "C" {
	const cv::detail::MultiBandBlender* cv_PtrLcv_detail_MultiBandBlenderG_getInnerPtr_const(const cv::Ptr<cv::detail::MultiBandBlender>* instance) {
			return instance->get();
	}
	
	cv::detail::MultiBandBlender* cv_PtrLcv_detail_MultiBandBlenderG_getInnerPtrMut(cv::Ptr<cv::detail::MultiBandBlender>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_MultiBandBlenderG_delete(cv::Ptr<cv::detail::MultiBandBlender>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::detail::Blender>* cv_PtrLcv_detail_MultiBandBlenderG_to_PtrOfDetail_Blender(cv::Ptr<cv::detail::MultiBandBlender>* instance) {
			return new cv::Ptr<cv::detail::Blender>(instance->dynamicCast<cv::detail::Blender>());
	}
	
	cv::Ptr<cv::detail::MultiBandBlender>* cv_PtrLcv_detail_MultiBandBlenderG_new_const_MultiBandBlender(cv::detail::MultiBandBlender* val) {
			return new cv::Ptr<cv::detail::MultiBandBlender>(val);
	}
	
}

extern "C" {
	const cv::detail::NoBundleAdjuster* cv_PtrLcv_detail_NoBundleAdjusterG_getInnerPtr_const(const cv::Ptr<cv::detail::NoBundleAdjuster>* instance) {
			return instance->get();
	}
	
	cv::detail::NoBundleAdjuster* cv_PtrLcv_detail_NoBundleAdjusterG_getInnerPtrMut(cv::Ptr<cv::detail::NoBundleAdjuster>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_NoBundleAdjusterG_delete(cv::Ptr<cv::detail::NoBundleAdjuster>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::detail::BundleAdjusterBase>* cv_PtrLcv_detail_NoBundleAdjusterG_to_PtrOfDetail_BundleAdjusterBase(cv::Ptr<cv::detail::NoBundleAdjuster>* instance) {
			return new cv::Ptr<cv::detail::BundleAdjusterBase>(instance->dynamicCast<cv::detail::BundleAdjusterBase>());
	}
	
	cv::Ptr<cv::detail::Estimator>* cv_PtrLcv_detail_NoBundleAdjusterG_to_PtrOfDetail_Estimator(cv::Ptr<cv::detail::NoBundleAdjuster>* instance) {
			return new cv::Ptr<cv::detail::Estimator>(instance->dynamicCast<cv::detail::Estimator>());
	}
	
	cv::Ptr<cv::detail::NoBundleAdjuster>* cv_PtrLcv_detail_NoBundleAdjusterG_new_const_NoBundleAdjuster(cv::detail::NoBundleAdjuster* val) {
			return new cv::Ptr<cv::detail::NoBundleAdjuster>(val);
	}
	
}

extern "C" {
	const cv::detail::NoExposureCompensator* cv_PtrLcv_detail_NoExposureCompensatorG_getInnerPtr_const(const cv::Ptr<cv::detail::NoExposureCompensator>* instance) {
			return instance->get();
	}
	
	cv::detail::NoExposureCompensator* cv_PtrLcv_detail_NoExposureCompensatorG_getInnerPtrMut(cv::Ptr<cv::detail::NoExposureCompensator>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_NoExposureCompensatorG_delete(cv::Ptr<cv::detail::NoExposureCompensator>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::detail::ExposureCompensator>* cv_PtrLcv_detail_NoExposureCompensatorG_to_PtrOfDetail_ExposureCompensator(cv::Ptr<cv::detail::NoExposureCompensator>* instance) {
			return new cv::Ptr<cv::detail::ExposureCompensator>(instance->dynamicCast<cv::detail::ExposureCompensator>());
	}
	
	cv::Ptr<cv::detail::NoExposureCompensator>* cv_PtrLcv_detail_NoExposureCompensatorG_new_const_NoExposureCompensator(cv::detail::NoExposureCompensator* val) {
			return new cv::Ptr<cv::detail::NoExposureCompensator>(val);
	}
	
}

extern "C" {
	const cv::detail::NoSeamFinder* cv_PtrLcv_detail_NoSeamFinderG_getInnerPtr_const(const cv::Ptr<cv::detail::NoSeamFinder>* instance) {
			return instance->get();
	}
	
	cv::detail::NoSeamFinder* cv_PtrLcv_detail_NoSeamFinderG_getInnerPtrMut(cv::Ptr<cv::detail::NoSeamFinder>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_NoSeamFinderG_delete(cv::Ptr<cv::detail::NoSeamFinder>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::detail::SeamFinder>* cv_PtrLcv_detail_NoSeamFinderG_to_PtrOfDetail_SeamFinder(cv::Ptr<cv::detail::NoSeamFinder>* instance) {
			return new cv::Ptr<cv::detail::SeamFinder>(instance->dynamicCast<cv::detail::SeamFinder>());
	}
	
	cv::Ptr<cv::detail::NoSeamFinder>* cv_PtrLcv_detail_NoSeamFinderG_new_const_NoSeamFinder(cv::detail::NoSeamFinder* val) {
			return new cv::Ptr<cv::detail::NoSeamFinder>(val);
	}
	
}

extern "C" {
	const cv::detail::PairwiseSeamFinder* cv_PtrLcv_detail_PairwiseSeamFinderG_getInnerPtr_const(const cv::Ptr<cv::detail::PairwiseSeamFinder>* instance) {
			return instance->get();
	}
	
	cv::detail::PairwiseSeamFinder* cv_PtrLcv_detail_PairwiseSeamFinderG_getInnerPtrMut(cv::Ptr<cv::detail::PairwiseSeamFinder>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_PairwiseSeamFinderG_delete(cv::Ptr<cv::detail::PairwiseSeamFinder>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::detail::SeamFinder>* cv_PtrLcv_detail_PairwiseSeamFinderG_to_PtrOfDetail_SeamFinder(cv::Ptr<cv::detail::PairwiseSeamFinder>* instance) {
			return new cv::Ptr<cv::detail::SeamFinder>(instance->dynamicCast<cv::detail::SeamFinder>());
	}
	
}

extern "C" {
	const cv::detail::RotationWarper* cv_PtrLcv_detail_RotationWarperG_getInnerPtr_const(const cv::Ptr<cv::detail::RotationWarper>* instance) {
			return instance->get();
	}
	
	cv::detail::RotationWarper* cv_PtrLcv_detail_RotationWarperG_getInnerPtrMut(cv::Ptr<cv::detail::RotationWarper>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_RotationWarperG_delete(cv::Ptr<cv::detail::RotationWarper>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::detail::SeamFinder* cv_PtrLcv_detail_SeamFinderG_getInnerPtr_const(const cv::Ptr<cv::detail::SeamFinder>* instance) {
			return instance->get();
	}
	
	cv::detail::SeamFinder* cv_PtrLcv_detail_SeamFinderG_getInnerPtrMut(cv::Ptr<cv::detail::SeamFinder>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_SeamFinderG_delete(cv::Ptr<cv::detail::SeamFinder>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::detail::VoronoiSeamFinder* cv_PtrLcv_detail_VoronoiSeamFinderG_getInnerPtr_const(const cv::Ptr<cv::detail::VoronoiSeamFinder>* instance) {
			return instance->get();
	}
	
	cv::detail::VoronoiSeamFinder* cv_PtrLcv_detail_VoronoiSeamFinderG_getInnerPtrMut(cv::Ptr<cv::detail::VoronoiSeamFinder>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_detail_VoronoiSeamFinderG_delete(cv::Ptr<cv::detail::VoronoiSeamFinder>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::detail::PairwiseSeamFinder>* cv_PtrLcv_detail_VoronoiSeamFinderG_to_PtrOfDetail_PairwiseSeamFinder(cv::Ptr<cv::detail::VoronoiSeamFinder>* instance) {
			return new cv::Ptr<cv::detail::PairwiseSeamFinder>(instance->dynamicCast<cv::detail::PairwiseSeamFinder>());
	}
	
	cv::Ptr<cv::detail::SeamFinder>* cv_PtrLcv_detail_VoronoiSeamFinderG_to_PtrOfDetail_SeamFinder(cv::Ptr<cv::detail::VoronoiSeamFinder>* instance) {
			return new cv::Ptr<cv::detail::SeamFinder>(instance->dynamicCast<cv::detail::SeamFinder>());
	}
	
	cv::Ptr<cv::detail::VoronoiSeamFinder>* cv_PtrLcv_detail_VoronoiSeamFinderG_new_const_VoronoiSeamFinder(cv::detail::VoronoiSeamFinder* val) {
			return new cv::Ptr<cv::detail::VoronoiSeamFinder>(val);
	}
	
}

extern "C" {
	const cv::FisheyeWarper* cv_PtrLcv_FisheyeWarperG_getInnerPtr_const(const cv::Ptr<cv::FisheyeWarper>* instance) {
			return instance->get();
	}
	
	cv::FisheyeWarper* cv_PtrLcv_FisheyeWarperG_getInnerPtrMut(cv::Ptr<cv::FisheyeWarper>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_FisheyeWarperG_delete(cv::Ptr<cv::FisheyeWarper>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::WarperCreator>* cv_PtrLcv_FisheyeWarperG_to_PtrOfWarperCreator(cv::Ptr<cv::FisheyeWarper>* instance) {
			return new cv::Ptr<cv::WarperCreator>(instance->dynamicCast<cv::WarperCreator>());
	}
	
	cv::Ptr<cv::FisheyeWarper>* cv_PtrLcv_FisheyeWarperG_new_const_FisheyeWarper(cv::FisheyeWarper* val) {
			return new cv::Ptr<cv::FisheyeWarper>(val);
	}
	
}

extern "C" {
	const cv::MercatorWarper* cv_PtrLcv_MercatorWarperG_getInnerPtr_const(const cv::Ptr<cv::MercatorWarper>* instance) {
			return instance->get();
	}
	
	cv::MercatorWarper* cv_PtrLcv_MercatorWarperG_getInnerPtrMut(cv::Ptr<cv::MercatorWarper>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_MercatorWarperG_delete(cv::Ptr<cv::MercatorWarper>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::WarperCreator>* cv_PtrLcv_MercatorWarperG_to_PtrOfWarperCreator(cv::Ptr<cv::MercatorWarper>* instance) {
			return new cv::Ptr<cv::WarperCreator>(instance->dynamicCast<cv::WarperCreator>());
	}
	
	cv::Ptr<cv::MercatorWarper>* cv_PtrLcv_MercatorWarperG_new_const_MercatorWarper(cv::MercatorWarper* val) {
			return new cv::Ptr<cv::MercatorWarper>(val);
	}
	
}

extern "C" {
	const cv::PaniniPortraitWarper* cv_PtrLcv_PaniniPortraitWarperG_getInnerPtr_const(const cv::Ptr<cv::PaniniPortraitWarper>* instance) {
			return instance->get();
	}
	
	cv::PaniniPortraitWarper* cv_PtrLcv_PaniniPortraitWarperG_getInnerPtrMut(cv::Ptr<cv::PaniniPortraitWarper>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_PaniniPortraitWarperG_delete(cv::Ptr<cv::PaniniPortraitWarper>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::WarperCreator>* cv_PtrLcv_PaniniPortraitWarperG_to_PtrOfWarperCreator(cv::Ptr<cv::PaniniPortraitWarper>* instance) {
			return new cv::Ptr<cv::WarperCreator>(instance->dynamicCast<cv::WarperCreator>());
	}
	
	cv::Ptr<cv::PaniniPortraitWarper>* cv_PtrLcv_PaniniPortraitWarperG_new_const_PaniniPortraitWarper(cv::PaniniPortraitWarper* val) {
			return new cv::Ptr<cv::PaniniPortraitWarper>(val);
	}
	
}

extern "C" {
	const cv::PaniniWarper* cv_PtrLcv_PaniniWarperG_getInnerPtr_const(const cv::Ptr<cv::PaniniWarper>* instance) {
			return instance->get();
	}
	
	cv::PaniniWarper* cv_PtrLcv_PaniniWarperG_getInnerPtrMut(cv::Ptr<cv::PaniniWarper>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_PaniniWarperG_delete(cv::Ptr<cv::PaniniWarper>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::WarperCreator>* cv_PtrLcv_PaniniWarperG_to_PtrOfWarperCreator(cv::Ptr<cv::PaniniWarper>* instance) {
			return new cv::Ptr<cv::WarperCreator>(instance->dynamicCast<cv::WarperCreator>());
	}
	
	cv::Ptr<cv::PaniniWarper>* cv_PtrLcv_PaniniWarperG_new_const_PaniniWarper(cv::PaniniWarper* val) {
			return new cv::Ptr<cv::PaniniWarper>(val);
	}
	
}

extern "C" {
	const cv::PlaneWarper* cv_PtrLcv_PlaneWarperG_getInnerPtr_const(const cv::Ptr<cv::PlaneWarper>* instance) {
			return instance->get();
	}
	
	cv::PlaneWarper* cv_PtrLcv_PlaneWarperG_getInnerPtrMut(cv::Ptr<cv::PlaneWarper>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_PlaneWarperG_delete(cv::Ptr<cv::PlaneWarper>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::WarperCreator>* cv_PtrLcv_PlaneWarperG_to_PtrOfWarperCreator(cv::Ptr<cv::PlaneWarper>* instance) {
			return new cv::Ptr<cv::WarperCreator>(instance->dynamicCast<cv::WarperCreator>());
	}
	
	cv::Ptr<cv::PlaneWarper>* cv_PtrLcv_PlaneWarperG_new_const_PlaneWarper(cv::PlaneWarper* val) {
			return new cv::Ptr<cv::PlaneWarper>(val);
	}
	
}

extern "C" {
	const cv::SphericalWarper* cv_PtrLcv_SphericalWarperG_getInnerPtr_const(const cv::Ptr<cv::SphericalWarper>* instance) {
			return instance->get();
	}
	
	cv::SphericalWarper* cv_PtrLcv_SphericalWarperG_getInnerPtrMut(cv::Ptr<cv::SphericalWarper>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_SphericalWarperG_delete(cv::Ptr<cv::SphericalWarper>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::WarperCreator>* cv_PtrLcv_SphericalWarperG_to_PtrOfWarperCreator(cv::Ptr<cv::SphericalWarper>* instance) {
			return new cv::Ptr<cv::WarperCreator>(instance->dynamicCast<cv::WarperCreator>());
	}
	
	cv::Ptr<cv::SphericalWarper>* cv_PtrLcv_SphericalWarperG_new_const_SphericalWarper(cv::SphericalWarper* val) {
			return new cv::Ptr<cv::SphericalWarper>(val);
	}
	
}

extern "C" {
	const cv::StereographicWarper* cv_PtrLcv_StereographicWarperG_getInnerPtr_const(const cv::Ptr<cv::StereographicWarper>* instance) {
			return instance->get();
	}
	
	cv::StereographicWarper* cv_PtrLcv_StereographicWarperG_getInnerPtrMut(cv::Ptr<cv::StereographicWarper>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_StereographicWarperG_delete(cv::Ptr<cv::StereographicWarper>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::WarperCreator>* cv_PtrLcv_StereographicWarperG_to_PtrOfWarperCreator(cv::Ptr<cv::StereographicWarper>* instance) {
			return new cv::Ptr<cv::WarperCreator>(instance->dynamicCast<cv::WarperCreator>());
	}
	
	cv::Ptr<cv::StereographicWarper>* cv_PtrLcv_StereographicWarperG_new_const_StereographicWarper(cv::StereographicWarper* val) {
			return new cv::Ptr<cv::StereographicWarper>(val);
	}
	
}

extern "C" {
	const cv::Stitcher* cv_PtrLcv_StitcherG_getInnerPtr_const(const cv::Ptr<cv::Stitcher>* instance) {
			return instance->get();
	}
	
	cv::Stitcher* cv_PtrLcv_StitcherG_getInnerPtrMut(cv::Ptr<cv::Stitcher>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_StitcherG_delete(cv::Ptr<cv::Stitcher>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Stitcher>* cv_PtrLcv_StitcherG_new_const_Stitcher(cv::Stitcher* val) {
			return new cv::Ptr<cv::Stitcher>(val);
	}
	
}

extern "C" {
	const cv::TransverseMercatorWarper* cv_PtrLcv_TransverseMercatorWarperG_getInnerPtr_const(const cv::Ptr<cv::TransverseMercatorWarper>* instance) {
			return instance->get();
	}
	
	cv::TransverseMercatorWarper* cv_PtrLcv_TransverseMercatorWarperG_getInnerPtrMut(cv::Ptr<cv::TransverseMercatorWarper>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_TransverseMercatorWarperG_delete(cv::Ptr<cv::TransverseMercatorWarper>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::WarperCreator>* cv_PtrLcv_TransverseMercatorWarperG_to_PtrOfWarperCreator(cv::Ptr<cv::TransverseMercatorWarper>* instance) {
			return new cv::Ptr<cv::WarperCreator>(instance->dynamicCast<cv::WarperCreator>());
	}
	
	cv::Ptr<cv::TransverseMercatorWarper>* cv_PtrLcv_TransverseMercatorWarperG_new_const_TransverseMercatorWarper(cv::TransverseMercatorWarper* val) {
			return new cv::Ptr<cv::TransverseMercatorWarper>(val);
	}
	
}

extern "C" {
	const cv::WarperCreator* cv_PtrLcv_WarperCreatorG_getInnerPtr_const(const cv::Ptr<cv::WarperCreator>* instance) {
			return instance->get();
	}
	
	cv::WarperCreator* cv_PtrLcv_WarperCreatorG_getInnerPtrMut(cv::Ptr<cv::WarperCreator>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_WarperCreatorG_delete(cv::Ptr<cv::WarperCreator>* instance) {
			delete instance;
	}
	
}

extern "C" {
	std::vector<cv::detail::CameraParams>* std_vectorLcv_detail_CameraParamsG_new_const() {
			std::vector<cv::detail::CameraParams>* ret = new std::vector<cv::detail::CameraParams>();
			return ret;
	}
	
	void std_vectorLcv_detail_CameraParamsG_delete(std::vector<cv::detail::CameraParams>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_detail_CameraParamsG_len_const(const std::vector<cv::detail::CameraParams>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_detail_CameraParamsG_isEmpty_const(const std::vector<cv::detail::CameraParams>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_detail_CameraParamsG_capacity_const(const std::vector<cv::detail::CameraParams>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_detail_CameraParamsG_shrinkToFit(std::vector<cv::detail::CameraParams>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_detail_CameraParamsG_reserve_size_t(std::vector<cv::detail::CameraParams>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_detail_CameraParamsG_remove_size_t(std::vector<cv::detail::CameraParams>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_detail_CameraParamsG_swap_size_t_size_t(std::vector<cv::detail::CameraParams>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_detail_CameraParamsG_clear(std::vector<cv::detail::CameraParams>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_detail_CameraParamsG_push_const_CameraParams(std::vector<cv::detail::CameraParams>* instance, const cv::detail::CameraParams* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_detail_CameraParamsG_insert_size_t_const_CameraParams(std::vector<cv::detail::CameraParams>* instance, size_t index, const cv::detail::CameraParams* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_detail_CameraParamsG_get_const_size_t(const std::vector<cv::detail::CameraParams>* instance, size_t index, cv::detail::CameraParams** ocvrs_return) {
			cv::detail::CameraParams ret = (*instance)[index];
			*ocvrs_return = new cv::detail::CameraParams(ret);
	}
	
	void std_vectorLcv_detail_CameraParamsG_set_size_t_const_CameraParams(std::vector<cv::detail::CameraParams>* instance, size_t index, const cv::detail::CameraParams* val) {
			(*instance)[index] = *val;
	}
	
}


extern "C" {
	std::vector<cv::detail::ImageFeatures>* std_vectorLcv_detail_ImageFeaturesG_new_const() {
			std::vector<cv::detail::ImageFeatures>* ret = new std::vector<cv::detail::ImageFeatures>();
			return ret;
	}
	
	void std_vectorLcv_detail_ImageFeaturesG_delete(std::vector<cv::detail::ImageFeatures>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_detail_ImageFeaturesG_len_const(const std::vector<cv::detail::ImageFeatures>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_detail_ImageFeaturesG_isEmpty_const(const std::vector<cv::detail::ImageFeatures>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_detail_ImageFeaturesG_capacity_const(const std::vector<cv::detail::ImageFeatures>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_detail_ImageFeaturesG_shrinkToFit(std::vector<cv::detail::ImageFeatures>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_detail_ImageFeaturesG_reserve_size_t(std::vector<cv::detail::ImageFeatures>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_detail_ImageFeaturesG_remove_size_t(std::vector<cv::detail::ImageFeatures>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_detail_ImageFeaturesG_swap_size_t_size_t(std::vector<cv::detail::ImageFeatures>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_detail_ImageFeaturesG_clear(std::vector<cv::detail::ImageFeatures>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_detail_ImageFeaturesG_push_const_ImageFeatures(std::vector<cv::detail::ImageFeatures>* instance, const cv::detail::ImageFeatures* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_detail_ImageFeaturesG_insert_size_t_const_ImageFeatures(std::vector<cv::detail::ImageFeatures>* instance, size_t index, const cv::detail::ImageFeatures* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_detail_ImageFeaturesG_get_const_size_t(const std::vector<cv::detail::ImageFeatures>* instance, size_t index, cv::detail::ImageFeatures** ocvrs_return) {
			cv::detail::ImageFeatures ret = (*instance)[index];
			*ocvrs_return = new cv::detail::ImageFeatures(ret);
	}
	
	void std_vectorLcv_detail_ImageFeaturesG_set_size_t_const_ImageFeatures(std::vector<cv::detail::ImageFeatures>* instance, size_t index, const cv::detail::ImageFeatures* val) {
			(*instance)[index] = *val;
	}
	
}


extern "C" {
	std::vector<cv::detail::MatchesInfo>* std_vectorLcv_detail_MatchesInfoG_new_const() {
			std::vector<cv::detail::MatchesInfo>* ret = new std::vector<cv::detail::MatchesInfo>();
			return ret;
	}
	
	void std_vectorLcv_detail_MatchesInfoG_delete(std::vector<cv::detail::MatchesInfo>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_detail_MatchesInfoG_len_const(const std::vector<cv::detail::MatchesInfo>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_detail_MatchesInfoG_isEmpty_const(const std::vector<cv::detail::MatchesInfo>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_detail_MatchesInfoG_capacity_const(const std::vector<cv::detail::MatchesInfo>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_detail_MatchesInfoG_shrinkToFit(std::vector<cv::detail::MatchesInfo>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_detail_MatchesInfoG_reserve_size_t(std::vector<cv::detail::MatchesInfo>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_detail_MatchesInfoG_remove_size_t(std::vector<cv::detail::MatchesInfo>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_detail_MatchesInfoG_swap_size_t_size_t(std::vector<cv::detail::MatchesInfo>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_detail_MatchesInfoG_clear(std::vector<cv::detail::MatchesInfo>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_detail_MatchesInfoG_push_const_MatchesInfo(std::vector<cv::detail::MatchesInfo>* instance, const cv::detail::MatchesInfo* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_detail_MatchesInfoG_insert_size_t_const_MatchesInfo(std::vector<cv::detail::MatchesInfo>* instance, size_t index, const cv::detail::MatchesInfo* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_detail_MatchesInfoG_get_const_size_t(const std::vector<cv::detail::MatchesInfo>* instance, size_t index, cv::detail::MatchesInfo** ocvrs_return) {
			cv::detail::MatchesInfo ret = (*instance)[index];
			*ocvrs_return = new cv::detail::MatchesInfo(ret);
	}
	
	void std_vectorLcv_detail_MatchesInfoG_set_size_t_const_MatchesInfo(std::vector<cv::detail::MatchesInfo>* instance, size_t index, const cv::detail::MatchesInfo* val) {
			(*instance)[index] = *val;
	}
	
}



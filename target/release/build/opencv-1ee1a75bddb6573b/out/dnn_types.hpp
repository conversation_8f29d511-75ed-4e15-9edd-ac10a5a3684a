extern "C" {
	const cv::dnn::AbsLayer* cv_PtrLcv_dnn_AbsLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::AbsLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::AbsLayer* cv_PtrLcv_dnn_AbsLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::AbsLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_AbsLayerG_delete(cv::Ptr<cv::dnn::AbsLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_AbsLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::AbsLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::ActivationLayer>* cv_PtrLcv_dnn_AbsLayerG_to_PtrOfActivationLayer(cv::Ptr<cv::dnn::AbsLayer>* instance) {
			return new cv::Ptr<cv::dnn::ActivationLayer>(instance->dynamicCast<cv::dnn::ActivationLayer>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_AbsLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::AbsLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::AbsLayer>* cv_PtrLcv_dnn_AbsLayerG_new_const_AbsLayer(cv::dnn::AbsLayer* val) {
			return new cv::Ptr<cv::dnn::AbsLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::AccumLayer* cv_PtrLcv_dnn_AccumLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::AccumLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::AccumLayer* cv_PtrLcv_dnn_AccumLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::AccumLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_AccumLayerG_delete(cv::Ptr<cv::dnn::AccumLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_AccumLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::AccumLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_AccumLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::AccumLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::AccumLayer>* cv_PtrLcv_dnn_AccumLayerG_new_const_AccumLayer(cv::dnn::AccumLayer* val) {
			return new cv::Ptr<cv::dnn::AccumLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::ActivationLayer* cv_PtrLcv_dnn_ActivationLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::ActivationLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::ActivationLayer* cv_PtrLcv_dnn_ActivationLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::ActivationLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_ActivationLayerG_delete(cv::Ptr<cv::dnn::ActivationLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_ActivationLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::ActivationLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_ActivationLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::ActivationLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::ActivationLayer>* cv_PtrLcv_dnn_ActivationLayerG_new_const_ActivationLayer(cv::dnn::ActivationLayer* val) {
			return new cv::Ptr<cv::dnn::ActivationLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::ActivationLayerInt8* cv_PtrLcv_dnn_ActivationLayerInt8G_getInnerPtr_const(const cv::Ptr<cv::dnn::ActivationLayerInt8>* instance) {
			return instance->get();
	}
	
	cv::dnn::ActivationLayerInt8* cv_PtrLcv_dnn_ActivationLayerInt8G_getInnerPtrMut(cv::Ptr<cv::dnn::ActivationLayerInt8>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_ActivationLayerInt8G_delete(cv::Ptr<cv::dnn::ActivationLayerInt8>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_ActivationLayerInt8G_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::ActivationLayerInt8>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::ActivationLayer>* cv_PtrLcv_dnn_ActivationLayerInt8G_to_PtrOfActivationLayer(cv::Ptr<cv::dnn::ActivationLayerInt8>* instance) {
			return new cv::Ptr<cv::dnn::ActivationLayer>(instance->dynamicCast<cv::dnn::ActivationLayer>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_ActivationLayerInt8G_to_PtrOfLayer(cv::Ptr<cv::dnn::ActivationLayerInt8>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::ActivationLayerInt8>* cv_PtrLcv_dnn_ActivationLayerInt8G_new_const_ActivationLayerInt8(cv::dnn::ActivationLayerInt8* val) {
			return new cv::Ptr<cv::dnn::ActivationLayerInt8>(val);
	}
	
}

extern "C" {
	const cv::dnn::BNLLLayer* cv_PtrLcv_dnn_BNLLLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::BNLLLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::BNLLLayer* cv_PtrLcv_dnn_BNLLLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::BNLLLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_BNLLLayerG_delete(cv::Ptr<cv::dnn::BNLLLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_BNLLLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::BNLLLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::ActivationLayer>* cv_PtrLcv_dnn_BNLLLayerG_to_PtrOfActivationLayer(cv::Ptr<cv::dnn::BNLLLayer>* instance) {
			return new cv::Ptr<cv::dnn::ActivationLayer>(instance->dynamicCast<cv::dnn::ActivationLayer>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_BNLLLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::BNLLLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::BNLLLayer>* cv_PtrLcv_dnn_BNLLLayerG_new_const_BNLLLayer(cv::dnn::BNLLLayer* val) {
			return new cv::Ptr<cv::dnn::BNLLLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::BackendNode* cv_PtrLcv_dnn_BackendNodeG_getInnerPtr_const(const cv::Ptr<cv::dnn::BackendNode>* instance) {
			return instance->get();
	}
	
	cv::dnn::BackendNode* cv_PtrLcv_dnn_BackendNodeG_getInnerPtrMut(cv::Ptr<cv::dnn::BackendNode>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_BackendNodeG_delete(cv::Ptr<cv::dnn::BackendNode>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::dnn::BackendNode>* cv_PtrLcv_dnn_BackendNodeG_new_const_BackendNode(cv::dnn::BackendNode* val) {
			return new cv::Ptr<cv::dnn::BackendNode>(val);
	}
	
}

extern "C" {
	const cv::dnn::BackendWrapper* cv_PtrLcv_dnn_BackendWrapperG_getInnerPtr_const(const cv::Ptr<cv::dnn::BackendWrapper>* instance) {
			return instance->get();
	}
	
	cv::dnn::BackendWrapper* cv_PtrLcv_dnn_BackendWrapperG_getInnerPtrMut(cv::Ptr<cv::dnn::BackendWrapper>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_BackendWrapperG_delete(cv::Ptr<cv::dnn::BackendWrapper>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::dnn::BaseConvolutionLayer* cv_PtrLcv_dnn_BaseConvolutionLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::BaseConvolutionLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::BaseConvolutionLayer* cv_PtrLcv_dnn_BaseConvolutionLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::BaseConvolutionLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_BaseConvolutionLayerG_delete(cv::Ptr<cv::dnn::BaseConvolutionLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_BaseConvolutionLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::BaseConvolutionLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_BaseConvolutionLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::BaseConvolutionLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::BaseConvolutionLayer>* cv_PtrLcv_dnn_BaseConvolutionLayerG_new_const_BaseConvolutionLayer(cv::dnn::BaseConvolutionLayer* val) {
			return new cv::Ptr<cv::dnn::BaseConvolutionLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::BatchNormLayer* cv_PtrLcv_dnn_BatchNormLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::BatchNormLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::BatchNormLayer* cv_PtrLcv_dnn_BatchNormLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::BatchNormLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_BatchNormLayerG_delete(cv::Ptr<cv::dnn::BatchNormLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_BatchNormLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::BatchNormLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::ActivationLayer>* cv_PtrLcv_dnn_BatchNormLayerG_to_PtrOfActivationLayer(cv::Ptr<cv::dnn::BatchNormLayer>* instance) {
			return new cv::Ptr<cv::dnn::ActivationLayer>(instance->dynamicCast<cv::dnn::ActivationLayer>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_BatchNormLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::BatchNormLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::BatchNormLayer>* cv_PtrLcv_dnn_BatchNormLayerG_new_const_BatchNormLayer(cv::dnn::BatchNormLayer* val) {
			return new cv::Ptr<cv::dnn::BatchNormLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::BatchNormLayerInt8* cv_PtrLcv_dnn_BatchNormLayerInt8G_getInnerPtr_const(const cv::Ptr<cv::dnn::BatchNormLayerInt8>* instance) {
			return instance->get();
	}
	
	cv::dnn::BatchNormLayerInt8* cv_PtrLcv_dnn_BatchNormLayerInt8G_getInnerPtrMut(cv::Ptr<cv::dnn::BatchNormLayerInt8>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_BatchNormLayerInt8G_delete(cv::Ptr<cv::dnn::BatchNormLayerInt8>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_BatchNormLayerInt8G_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::BatchNormLayerInt8>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::ActivationLayer>* cv_PtrLcv_dnn_BatchNormLayerInt8G_to_PtrOfActivationLayer(cv::Ptr<cv::dnn::BatchNormLayerInt8>* instance) {
			return new cv::Ptr<cv::dnn::ActivationLayer>(instance->dynamicCast<cv::dnn::ActivationLayer>());
	}
	
	cv::Ptr<cv::dnn::BatchNormLayer>* cv_PtrLcv_dnn_BatchNormLayerInt8G_to_PtrOfBatchNormLayer(cv::Ptr<cv::dnn::BatchNormLayerInt8>* instance) {
			return new cv::Ptr<cv::dnn::BatchNormLayer>(instance->dynamicCast<cv::dnn::BatchNormLayer>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_BatchNormLayerInt8G_to_PtrOfLayer(cv::Ptr<cv::dnn::BatchNormLayerInt8>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::BatchNormLayerInt8>* cv_PtrLcv_dnn_BatchNormLayerInt8G_new_const_BatchNormLayerInt8(cv::dnn::BatchNormLayerInt8* val) {
			return new cv::Ptr<cv::dnn::BatchNormLayerInt8>(val);
	}
	
}

extern "C" {
	const cv::dnn::BlankLayer* cv_PtrLcv_dnn_BlankLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::BlankLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::BlankLayer* cv_PtrLcv_dnn_BlankLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::BlankLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_BlankLayerG_delete(cv::Ptr<cv::dnn::BlankLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_BlankLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::BlankLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_BlankLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::BlankLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::BlankLayer>* cv_PtrLcv_dnn_BlankLayerG_new_const_BlankLayer(cv::dnn::BlankLayer* val) {
			return new cv::Ptr<cv::dnn::BlankLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::ChannelsPReLULayer* cv_PtrLcv_dnn_ChannelsPReLULayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::ChannelsPReLULayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::ChannelsPReLULayer* cv_PtrLcv_dnn_ChannelsPReLULayerG_getInnerPtrMut(cv::Ptr<cv::dnn::ChannelsPReLULayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_ChannelsPReLULayerG_delete(cv::Ptr<cv::dnn::ChannelsPReLULayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_ChannelsPReLULayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::ChannelsPReLULayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::ActivationLayer>* cv_PtrLcv_dnn_ChannelsPReLULayerG_to_PtrOfActivationLayer(cv::Ptr<cv::dnn::ChannelsPReLULayer>* instance) {
			return new cv::Ptr<cv::dnn::ActivationLayer>(instance->dynamicCast<cv::dnn::ActivationLayer>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_ChannelsPReLULayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::ChannelsPReLULayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::ChannelsPReLULayer>* cv_PtrLcv_dnn_ChannelsPReLULayerG_new_const_ChannelsPReLULayer(cv::dnn::ChannelsPReLULayer* val) {
			return new cv::Ptr<cv::dnn::ChannelsPReLULayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::ConcatLayer* cv_PtrLcv_dnn_ConcatLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::ConcatLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::ConcatLayer* cv_PtrLcv_dnn_ConcatLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::ConcatLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_ConcatLayerG_delete(cv::Ptr<cv::dnn::ConcatLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_ConcatLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::ConcatLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_ConcatLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::ConcatLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::ConcatLayer>* cv_PtrLcv_dnn_ConcatLayerG_new_const_ConcatLayer(cv::dnn::ConcatLayer* val) {
			return new cv::Ptr<cv::dnn::ConcatLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::ConstLayer* cv_PtrLcv_dnn_ConstLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::ConstLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::ConstLayer* cv_PtrLcv_dnn_ConstLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::ConstLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_ConstLayerG_delete(cv::Ptr<cv::dnn::ConstLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_ConstLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::ConstLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_ConstLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::ConstLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::ConstLayer>* cv_PtrLcv_dnn_ConstLayerG_new_const_ConstLayer(cv::dnn::ConstLayer* val) {
			return new cv::Ptr<cv::dnn::ConstLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::ConvolutionLayer* cv_PtrLcv_dnn_ConvolutionLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::ConvolutionLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::ConvolutionLayer* cv_PtrLcv_dnn_ConvolutionLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::ConvolutionLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_ConvolutionLayerG_delete(cv::Ptr<cv::dnn::ConvolutionLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_ConvolutionLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::ConvolutionLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::BaseConvolutionLayer>* cv_PtrLcv_dnn_ConvolutionLayerG_to_PtrOfBaseConvolutionLayer(cv::Ptr<cv::dnn::ConvolutionLayer>* instance) {
			return new cv::Ptr<cv::dnn::BaseConvolutionLayer>(instance->dynamicCast<cv::dnn::BaseConvolutionLayer>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_ConvolutionLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::ConvolutionLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::ConvolutionLayer>* cv_PtrLcv_dnn_ConvolutionLayerG_new_const_ConvolutionLayer(cv::dnn::ConvolutionLayer* val) {
			return new cv::Ptr<cv::dnn::ConvolutionLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::ConvolutionLayerInt8* cv_PtrLcv_dnn_ConvolutionLayerInt8G_getInnerPtr_const(const cv::Ptr<cv::dnn::ConvolutionLayerInt8>* instance) {
			return instance->get();
	}
	
	cv::dnn::ConvolutionLayerInt8* cv_PtrLcv_dnn_ConvolutionLayerInt8G_getInnerPtrMut(cv::Ptr<cv::dnn::ConvolutionLayerInt8>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_ConvolutionLayerInt8G_delete(cv::Ptr<cv::dnn::ConvolutionLayerInt8>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_ConvolutionLayerInt8G_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::ConvolutionLayerInt8>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::BaseConvolutionLayer>* cv_PtrLcv_dnn_ConvolutionLayerInt8G_to_PtrOfBaseConvolutionLayer(cv::Ptr<cv::dnn::ConvolutionLayerInt8>* instance) {
			return new cv::Ptr<cv::dnn::BaseConvolutionLayer>(instance->dynamicCast<cv::dnn::BaseConvolutionLayer>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_ConvolutionLayerInt8G_to_PtrOfLayer(cv::Ptr<cv::dnn::ConvolutionLayerInt8>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::ConvolutionLayerInt8>* cv_PtrLcv_dnn_ConvolutionLayerInt8G_new_const_ConvolutionLayerInt8(cv::dnn::ConvolutionLayerInt8* val) {
			return new cv::Ptr<cv::dnn::ConvolutionLayerInt8>(val);
	}
	
}

extern "C" {
	const cv::dnn::CorrelationLayer* cv_PtrLcv_dnn_CorrelationLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::CorrelationLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::CorrelationLayer* cv_PtrLcv_dnn_CorrelationLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::CorrelationLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_CorrelationLayerG_delete(cv::Ptr<cv::dnn::CorrelationLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_CorrelationLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::CorrelationLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_CorrelationLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::CorrelationLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::CorrelationLayer>* cv_PtrLcv_dnn_CorrelationLayerG_new_const_CorrelationLayer(cv::dnn::CorrelationLayer* val) {
			return new cv::Ptr<cv::dnn::CorrelationLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::CropAndResizeLayer* cv_PtrLcv_dnn_CropAndResizeLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::CropAndResizeLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::CropAndResizeLayer* cv_PtrLcv_dnn_CropAndResizeLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::CropAndResizeLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_CropAndResizeLayerG_delete(cv::Ptr<cv::dnn::CropAndResizeLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_CropAndResizeLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::CropAndResizeLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_CropAndResizeLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::CropAndResizeLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::CropAndResizeLayer>* cv_PtrLcv_dnn_CropAndResizeLayerG_new_const_CropAndResizeLayer(cv::dnn::CropAndResizeLayer* val) {
			return new cv::Ptr<cv::dnn::CropAndResizeLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::CropLayer* cv_PtrLcv_dnn_CropLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::CropLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::CropLayer* cv_PtrLcv_dnn_CropLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::CropLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_CropLayerG_delete(cv::Ptr<cv::dnn::CropLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_CropLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::CropLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_CropLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::CropLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::CropLayer>* cv_PtrLcv_dnn_CropLayerG_new_const_CropLayer(cv::dnn::CropLayer* val) {
			return new cv::Ptr<cv::dnn::CropLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::CumSumLayer* cv_PtrLcv_dnn_CumSumLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::CumSumLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::CumSumLayer* cv_PtrLcv_dnn_CumSumLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::CumSumLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_CumSumLayerG_delete(cv::Ptr<cv::dnn::CumSumLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_CumSumLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::CumSumLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_CumSumLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::CumSumLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::CumSumLayer>* cv_PtrLcv_dnn_CumSumLayerG_new_const_CumSumLayer(cv::dnn::CumSumLayer* val) {
			return new cv::Ptr<cv::dnn::CumSumLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::DataAugmentationLayer* cv_PtrLcv_dnn_DataAugmentationLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::DataAugmentationLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::DataAugmentationLayer* cv_PtrLcv_dnn_DataAugmentationLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::DataAugmentationLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_DataAugmentationLayerG_delete(cv::Ptr<cv::dnn::DataAugmentationLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_DataAugmentationLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::DataAugmentationLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_DataAugmentationLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::DataAugmentationLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::DataAugmentationLayer>* cv_PtrLcv_dnn_DataAugmentationLayerG_new_const_DataAugmentationLayer(cv::dnn::DataAugmentationLayer* val) {
			return new cv::Ptr<cv::dnn::DataAugmentationLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::DeconvolutionLayer* cv_PtrLcv_dnn_DeconvolutionLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::DeconvolutionLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::DeconvolutionLayer* cv_PtrLcv_dnn_DeconvolutionLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::DeconvolutionLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_DeconvolutionLayerG_delete(cv::Ptr<cv::dnn::DeconvolutionLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_DeconvolutionLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::DeconvolutionLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::BaseConvolutionLayer>* cv_PtrLcv_dnn_DeconvolutionLayerG_to_PtrOfBaseConvolutionLayer(cv::Ptr<cv::dnn::DeconvolutionLayer>* instance) {
			return new cv::Ptr<cv::dnn::BaseConvolutionLayer>(instance->dynamicCast<cv::dnn::BaseConvolutionLayer>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_DeconvolutionLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::DeconvolutionLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::DeconvolutionLayer>* cv_PtrLcv_dnn_DeconvolutionLayerG_new_const_DeconvolutionLayer(cv::dnn::DeconvolutionLayer* val) {
			return new cv::Ptr<cv::dnn::DeconvolutionLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::DequantizeLayer* cv_PtrLcv_dnn_DequantizeLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::DequantizeLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::DequantizeLayer* cv_PtrLcv_dnn_DequantizeLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::DequantizeLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_DequantizeLayerG_delete(cv::Ptr<cv::dnn::DequantizeLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_DequantizeLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::DequantizeLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_DequantizeLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::DequantizeLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::DequantizeLayer>* cv_PtrLcv_dnn_DequantizeLayerG_new_const_DequantizeLayer(cv::dnn::DequantizeLayer* val) {
			return new cv::Ptr<cv::dnn::DequantizeLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::DetectionOutputLayer* cv_PtrLcv_dnn_DetectionOutputLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::DetectionOutputLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::DetectionOutputLayer* cv_PtrLcv_dnn_DetectionOutputLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::DetectionOutputLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_DetectionOutputLayerG_delete(cv::Ptr<cv::dnn::DetectionOutputLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_DetectionOutputLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::DetectionOutputLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_DetectionOutputLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::DetectionOutputLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::DetectionOutputLayer>* cv_PtrLcv_dnn_DetectionOutputLayerG_new_const_DetectionOutputLayer(cv::dnn::DetectionOutputLayer* val) {
			return new cv::Ptr<cv::dnn::DetectionOutputLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::ELULayer* cv_PtrLcv_dnn_ELULayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::ELULayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::ELULayer* cv_PtrLcv_dnn_ELULayerG_getInnerPtrMut(cv::Ptr<cv::dnn::ELULayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_ELULayerG_delete(cv::Ptr<cv::dnn::ELULayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_ELULayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::ELULayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::ActivationLayer>* cv_PtrLcv_dnn_ELULayerG_to_PtrOfActivationLayer(cv::Ptr<cv::dnn::ELULayer>* instance) {
			return new cv::Ptr<cv::dnn::ActivationLayer>(instance->dynamicCast<cv::dnn::ActivationLayer>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_ELULayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::ELULayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::ELULayer>* cv_PtrLcv_dnn_ELULayerG_new_const_ELULayer(cv::dnn::ELULayer* val) {
			return new cv::Ptr<cv::dnn::ELULayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::EltwiseLayer* cv_PtrLcv_dnn_EltwiseLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::EltwiseLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::EltwiseLayer* cv_PtrLcv_dnn_EltwiseLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::EltwiseLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_EltwiseLayerG_delete(cv::Ptr<cv::dnn::EltwiseLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_EltwiseLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::EltwiseLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_EltwiseLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::EltwiseLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::EltwiseLayer>* cv_PtrLcv_dnn_EltwiseLayerG_new_const_EltwiseLayer(cv::dnn::EltwiseLayer* val) {
			return new cv::Ptr<cv::dnn::EltwiseLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::EltwiseLayerInt8* cv_PtrLcv_dnn_EltwiseLayerInt8G_getInnerPtr_const(const cv::Ptr<cv::dnn::EltwiseLayerInt8>* instance) {
			return instance->get();
	}
	
	cv::dnn::EltwiseLayerInt8* cv_PtrLcv_dnn_EltwiseLayerInt8G_getInnerPtrMut(cv::Ptr<cv::dnn::EltwiseLayerInt8>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_EltwiseLayerInt8G_delete(cv::Ptr<cv::dnn::EltwiseLayerInt8>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_EltwiseLayerInt8G_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::EltwiseLayerInt8>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_EltwiseLayerInt8G_to_PtrOfLayer(cv::Ptr<cv::dnn::EltwiseLayerInt8>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::EltwiseLayerInt8>* cv_PtrLcv_dnn_EltwiseLayerInt8G_new_const_EltwiseLayerInt8(cv::dnn::EltwiseLayerInt8* val) {
			return new cv::Ptr<cv::dnn::EltwiseLayerInt8>(val);
	}
	
}

extern "C" {
	const cv::dnn::ExpLayer* cv_PtrLcv_dnn_ExpLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::ExpLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::ExpLayer* cv_PtrLcv_dnn_ExpLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::ExpLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_ExpLayerG_delete(cv::Ptr<cv::dnn::ExpLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_ExpLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::ExpLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::ActivationLayer>* cv_PtrLcv_dnn_ExpLayerG_to_PtrOfActivationLayer(cv::Ptr<cv::dnn::ExpLayer>* instance) {
			return new cv::Ptr<cv::dnn::ActivationLayer>(instance->dynamicCast<cv::dnn::ActivationLayer>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_ExpLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::ExpLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::ExpLayer>* cv_PtrLcv_dnn_ExpLayerG_new_const_ExpLayer(cv::dnn::ExpLayer* val) {
			return new cv::Ptr<cv::dnn::ExpLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::FlattenLayer* cv_PtrLcv_dnn_FlattenLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::FlattenLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::FlattenLayer* cv_PtrLcv_dnn_FlattenLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::FlattenLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_FlattenLayerG_delete(cv::Ptr<cv::dnn::FlattenLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_FlattenLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::FlattenLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_FlattenLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::FlattenLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::FlattenLayer>* cv_PtrLcv_dnn_FlattenLayerG_new_const_FlattenLayer(cv::dnn::FlattenLayer* val) {
			return new cv::Ptr<cv::dnn::FlattenLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::FlowWarpLayer* cv_PtrLcv_dnn_FlowWarpLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::FlowWarpLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::FlowWarpLayer* cv_PtrLcv_dnn_FlowWarpLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::FlowWarpLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_FlowWarpLayerG_delete(cv::Ptr<cv::dnn::FlowWarpLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_FlowWarpLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::FlowWarpLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_FlowWarpLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::FlowWarpLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::FlowWarpLayer>* cv_PtrLcv_dnn_FlowWarpLayerG_new_const_FlowWarpLayer(cv::dnn::FlowWarpLayer* val) {
			return new cv::Ptr<cv::dnn::FlowWarpLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::GRULayer* cv_PtrLcv_dnn_GRULayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::GRULayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::GRULayer* cv_PtrLcv_dnn_GRULayerG_getInnerPtrMut(cv::Ptr<cv::dnn::GRULayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_GRULayerG_delete(cv::Ptr<cv::dnn::GRULayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_GRULayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::GRULayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_GRULayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::GRULayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::GRULayer>* cv_PtrLcv_dnn_GRULayerG_new_const_GRULayer(cv::dnn::GRULayer* val) {
			return new cv::Ptr<cv::dnn::GRULayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::InnerProductLayer* cv_PtrLcv_dnn_InnerProductLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::InnerProductLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::InnerProductLayer* cv_PtrLcv_dnn_InnerProductLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::InnerProductLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_InnerProductLayerG_delete(cv::Ptr<cv::dnn::InnerProductLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_InnerProductLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::InnerProductLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_InnerProductLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::InnerProductLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::InnerProductLayer>* cv_PtrLcv_dnn_InnerProductLayerG_new_const_InnerProductLayer(cv::dnn::InnerProductLayer* val) {
			return new cv::Ptr<cv::dnn::InnerProductLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::InnerProductLayerInt8* cv_PtrLcv_dnn_InnerProductLayerInt8G_getInnerPtr_const(const cv::Ptr<cv::dnn::InnerProductLayerInt8>* instance) {
			return instance->get();
	}
	
	cv::dnn::InnerProductLayerInt8* cv_PtrLcv_dnn_InnerProductLayerInt8G_getInnerPtrMut(cv::Ptr<cv::dnn::InnerProductLayerInt8>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_InnerProductLayerInt8G_delete(cv::Ptr<cv::dnn::InnerProductLayerInt8>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_InnerProductLayerInt8G_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::InnerProductLayerInt8>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::InnerProductLayer>* cv_PtrLcv_dnn_InnerProductLayerInt8G_to_PtrOfInnerProductLayer(cv::Ptr<cv::dnn::InnerProductLayerInt8>* instance) {
			return new cv::Ptr<cv::dnn::InnerProductLayer>(instance->dynamicCast<cv::dnn::InnerProductLayer>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_InnerProductLayerInt8G_to_PtrOfLayer(cv::Ptr<cv::dnn::InnerProductLayerInt8>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::InnerProductLayerInt8>* cv_PtrLcv_dnn_InnerProductLayerInt8G_new_const_InnerProductLayerInt8(cv::dnn::InnerProductLayerInt8* val) {
			return new cv::Ptr<cv::dnn::InnerProductLayerInt8>(val);
	}
	
}

extern "C" {
	const cv::dnn::InterpLayer* cv_PtrLcv_dnn_InterpLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::InterpLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::InterpLayer* cv_PtrLcv_dnn_InterpLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::InterpLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_InterpLayerG_delete(cv::Ptr<cv::dnn::InterpLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_InterpLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::InterpLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_InterpLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::InterpLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::InterpLayer>* cv_PtrLcv_dnn_InterpLayerG_new_const_InterpLayer(cv::dnn::InterpLayer* val) {
			return new cv::Ptr<cv::dnn::InterpLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::LRNLayer* cv_PtrLcv_dnn_LRNLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::LRNLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::LRNLayer* cv_PtrLcv_dnn_LRNLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::LRNLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_LRNLayerG_delete(cv::Ptr<cv::dnn::LRNLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_LRNLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::LRNLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_LRNLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::LRNLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::LRNLayer>* cv_PtrLcv_dnn_LRNLayerG_new_const_LRNLayer(cv::dnn::LRNLayer* val) {
			return new cv::Ptr<cv::dnn::LRNLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::LSTMLayer* cv_PtrLcv_dnn_LSTMLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::LSTMLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::LSTMLayer* cv_PtrLcv_dnn_LSTMLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::LSTMLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_LSTMLayerG_delete(cv::Ptr<cv::dnn::LSTMLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_LSTMLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::LSTMLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_LSTMLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::LSTMLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
}

extern "C" {
	const cv::dnn::Layer* cv_PtrLcv_dnn_LayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::Layer>* instance) {
			return instance->get();
	}
	
	cv::dnn::Layer* cv_PtrLcv_dnn_LayerG_getInnerPtrMut(cv::Ptr<cv::dnn::Layer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_LayerG_delete(cv::Ptr<cv::dnn::Layer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_LayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::Layer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_LayerG_new_const_Layer(cv::dnn::Layer* val) {
			return new cv::Ptr<cv::dnn::Layer>(val);
	}
	
}

extern "C" {
	const cv::dnn::MVNLayer* cv_PtrLcv_dnn_MVNLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::MVNLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::MVNLayer* cv_PtrLcv_dnn_MVNLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::MVNLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_MVNLayerG_delete(cv::Ptr<cv::dnn::MVNLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_MVNLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::MVNLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_MVNLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::MVNLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::MVNLayer>* cv_PtrLcv_dnn_MVNLayerG_new_const_MVNLayer(cv::dnn::MVNLayer* val) {
			return new cv::Ptr<cv::dnn::MVNLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::MaxUnpoolLayer* cv_PtrLcv_dnn_MaxUnpoolLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::MaxUnpoolLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::MaxUnpoolLayer* cv_PtrLcv_dnn_MaxUnpoolLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::MaxUnpoolLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_MaxUnpoolLayerG_delete(cv::Ptr<cv::dnn::MaxUnpoolLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_MaxUnpoolLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::MaxUnpoolLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_MaxUnpoolLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::MaxUnpoolLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::MaxUnpoolLayer>* cv_PtrLcv_dnn_MaxUnpoolLayerG_new_const_MaxUnpoolLayer(cv::dnn::MaxUnpoolLayer* val) {
			return new cv::Ptr<cv::dnn::MaxUnpoolLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::MishLayer* cv_PtrLcv_dnn_MishLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::MishLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::MishLayer* cv_PtrLcv_dnn_MishLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::MishLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_MishLayerG_delete(cv::Ptr<cv::dnn::MishLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_MishLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::MishLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::ActivationLayer>* cv_PtrLcv_dnn_MishLayerG_to_PtrOfActivationLayer(cv::Ptr<cv::dnn::MishLayer>* instance) {
			return new cv::Ptr<cv::dnn::ActivationLayer>(instance->dynamicCast<cv::dnn::ActivationLayer>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_MishLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::MishLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::MishLayer>* cv_PtrLcv_dnn_MishLayerG_new_const_MishLayer(cv::dnn::MishLayer* val) {
			return new cv::Ptr<cv::dnn::MishLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::NormalizeBBoxLayer* cv_PtrLcv_dnn_NormalizeBBoxLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::NormalizeBBoxLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::NormalizeBBoxLayer* cv_PtrLcv_dnn_NormalizeBBoxLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::NormalizeBBoxLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_NormalizeBBoxLayerG_delete(cv::Ptr<cv::dnn::NormalizeBBoxLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_NormalizeBBoxLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::NormalizeBBoxLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_NormalizeBBoxLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::NormalizeBBoxLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::NormalizeBBoxLayer>* cv_PtrLcv_dnn_NormalizeBBoxLayerG_new_const_NormalizeBBoxLayer(cv::dnn::NormalizeBBoxLayer* val) {
			return new cv::Ptr<cv::dnn::NormalizeBBoxLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::PaddingLayer* cv_PtrLcv_dnn_PaddingLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::PaddingLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::PaddingLayer* cv_PtrLcv_dnn_PaddingLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::PaddingLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_PaddingLayerG_delete(cv::Ptr<cv::dnn::PaddingLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_PaddingLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::PaddingLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_PaddingLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::PaddingLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::PaddingLayer>* cv_PtrLcv_dnn_PaddingLayerG_new_const_PaddingLayer(cv::dnn::PaddingLayer* val) {
			return new cv::Ptr<cv::dnn::PaddingLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::PermuteLayer* cv_PtrLcv_dnn_PermuteLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::PermuteLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::PermuteLayer* cv_PtrLcv_dnn_PermuteLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::PermuteLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_PermuteLayerG_delete(cv::Ptr<cv::dnn::PermuteLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_PermuteLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::PermuteLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_PermuteLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::PermuteLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::PermuteLayer>* cv_PtrLcv_dnn_PermuteLayerG_new_const_PermuteLayer(cv::dnn::PermuteLayer* val) {
			return new cv::Ptr<cv::dnn::PermuteLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::PoolingLayer* cv_PtrLcv_dnn_PoolingLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::PoolingLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::PoolingLayer* cv_PtrLcv_dnn_PoolingLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::PoolingLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_PoolingLayerG_delete(cv::Ptr<cv::dnn::PoolingLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_PoolingLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::PoolingLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_PoolingLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::PoolingLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::PoolingLayer>* cv_PtrLcv_dnn_PoolingLayerG_new_const_PoolingLayer(cv::dnn::PoolingLayer* val) {
			return new cv::Ptr<cv::dnn::PoolingLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::PoolingLayerInt8* cv_PtrLcv_dnn_PoolingLayerInt8G_getInnerPtr_const(const cv::Ptr<cv::dnn::PoolingLayerInt8>* instance) {
			return instance->get();
	}
	
	cv::dnn::PoolingLayerInt8* cv_PtrLcv_dnn_PoolingLayerInt8G_getInnerPtrMut(cv::Ptr<cv::dnn::PoolingLayerInt8>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_PoolingLayerInt8G_delete(cv::Ptr<cv::dnn::PoolingLayerInt8>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_PoolingLayerInt8G_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::PoolingLayerInt8>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_PoolingLayerInt8G_to_PtrOfLayer(cv::Ptr<cv::dnn::PoolingLayerInt8>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::PoolingLayer>* cv_PtrLcv_dnn_PoolingLayerInt8G_to_PtrOfPoolingLayer(cv::Ptr<cv::dnn::PoolingLayerInt8>* instance) {
			return new cv::Ptr<cv::dnn::PoolingLayer>(instance->dynamicCast<cv::dnn::PoolingLayer>());
	}
	
	cv::Ptr<cv::dnn::PoolingLayerInt8>* cv_PtrLcv_dnn_PoolingLayerInt8G_new_const_PoolingLayerInt8(cv::dnn::PoolingLayerInt8* val) {
			return new cv::Ptr<cv::dnn::PoolingLayerInt8>(val);
	}
	
}

extern "C" {
	const cv::dnn::PowerLayer* cv_PtrLcv_dnn_PowerLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::PowerLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::PowerLayer* cv_PtrLcv_dnn_PowerLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::PowerLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_PowerLayerG_delete(cv::Ptr<cv::dnn::PowerLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_PowerLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::PowerLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::ActivationLayer>* cv_PtrLcv_dnn_PowerLayerG_to_PtrOfActivationLayer(cv::Ptr<cv::dnn::PowerLayer>* instance) {
			return new cv::Ptr<cv::dnn::ActivationLayer>(instance->dynamicCast<cv::dnn::ActivationLayer>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_PowerLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::PowerLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::PowerLayer>* cv_PtrLcv_dnn_PowerLayerG_new_const_PowerLayer(cv::dnn::PowerLayer* val) {
			return new cv::Ptr<cv::dnn::PowerLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::PriorBoxLayer* cv_PtrLcv_dnn_PriorBoxLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::PriorBoxLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::PriorBoxLayer* cv_PtrLcv_dnn_PriorBoxLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::PriorBoxLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_PriorBoxLayerG_delete(cv::Ptr<cv::dnn::PriorBoxLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_PriorBoxLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::PriorBoxLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_PriorBoxLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::PriorBoxLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::PriorBoxLayer>* cv_PtrLcv_dnn_PriorBoxLayerG_new_const_PriorBoxLayer(cv::dnn::PriorBoxLayer* val) {
			return new cv::Ptr<cv::dnn::PriorBoxLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::ProposalLayer* cv_PtrLcv_dnn_ProposalLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::ProposalLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::ProposalLayer* cv_PtrLcv_dnn_ProposalLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::ProposalLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_ProposalLayerG_delete(cv::Ptr<cv::dnn::ProposalLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_ProposalLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::ProposalLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_ProposalLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::ProposalLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::ProposalLayer>* cv_PtrLcv_dnn_ProposalLayerG_new_const_ProposalLayer(cv::dnn::ProposalLayer* val) {
			return new cv::Ptr<cv::dnn::ProposalLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::QuantizeLayer* cv_PtrLcv_dnn_QuantizeLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::QuantizeLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::QuantizeLayer* cv_PtrLcv_dnn_QuantizeLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::QuantizeLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_QuantizeLayerG_delete(cv::Ptr<cv::dnn::QuantizeLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_QuantizeLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::QuantizeLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_QuantizeLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::QuantizeLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::QuantizeLayer>* cv_PtrLcv_dnn_QuantizeLayerG_new_const_QuantizeLayer(cv::dnn::QuantizeLayer* val) {
			return new cv::Ptr<cv::dnn::QuantizeLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::RNNLayer* cv_PtrLcv_dnn_RNNLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::RNNLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::RNNLayer* cv_PtrLcv_dnn_RNNLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::RNNLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_RNNLayerG_delete(cv::Ptr<cv::dnn::RNNLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_RNNLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::RNNLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_RNNLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::RNNLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
}

extern "C" {
	const cv::dnn::ReLU6Layer* cv_PtrLcv_dnn_ReLU6LayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::ReLU6Layer>* instance) {
			return instance->get();
	}
	
	cv::dnn::ReLU6Layer* cv_PtrLcv_dnn_ReLU6LayerG_getInnerPtrMut(cv::Ptr<cv::dnn::ReLU6Layer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_ReLU6LayerG_delete(cv::Ptr<cv::dnn::ReLU6Layer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_ReLU6LayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::ReLU6Layer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::ActivationLayer>* cv_PtrLcv_dnn_ReLU6LayerG_to_PtrOfActivationLayer(cv::Ptr<cv::dnn::ReLU6Layer>* instance) {
			return new cv::Ptr<cv::dnn::ActivationLayer>(instance->dynamicCast<cv::dnn::ActivationLayer>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_ReLU6LayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::ReLU6Layer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::ReLU6Layer>* cv_PtrLcv_dnn_ReLU6LayerG_new_const_ReLU6Layer(cv::dnn::ReLU6Layer* val) {
			return new cv::Ptr<cv::dnn::ReLU6Layer>(val);
	}
	
}

extern "C" {
	const cv::dnn::ReLULayer* cv_PtrLcv_dnn_ReLULayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::ReLULayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::ReLULayer* cv_PtrLcv_dnn_ReLULayerG_getInnerPtrMut(cv::Ptr<cv::dnn::ReLULayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_ReLULayerG_delete(cv::Ptr<cv::dnn::ReLULayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_ReLULayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::ReLULayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::ActivationLayer>* cv_PtrLcv_dnn_ReLULayerG_to_PtrOfActivationLayer(cv::Ptr<cv::dnn::ReLULayer>* instance) {
			return new cv::Ptr<cv::dnn::ActivationLayer>(instance->dynamicCast<cv::dnn::ActivationLayer>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_ReLULayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::ReLULayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::ReLULayer>* cv_PtrLcv_dnn_ReLULayerG_new_const_ReLULayer(cv::dnn::ReLULayer* val) {
			return new cv::Ptr<cv::dnn::ReLULayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::RegionLayer* cv_PtrLcv_dnn_RegionLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::RegionLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::RegionLayer* cv_PtrLcv_dnn_RegionLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::RegionLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_RegionLayerG_delete(cv::Ptr<cv::dnn::RegionLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_RegionLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::RegionLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_RegionLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::RegionLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::RegionLayer>* cv_PtrLcv_dnn_RegionLayerG_new_const_RegionLayer(cv::dnn::RegionLayer* val) {
			return new cv::Ptr<cv::dnn::RegionLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::ReorgLayer* cv_PtrLcv_dnn_ReorgLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::ReorgLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::ReorgLayer* cv_PtrLcv_dnn_ReorgLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::ReorgLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_ReorgLayerG_delete(cv::Ptr<cv::dnn::ReorgLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_ReorgLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::ReorgLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_ReorgLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::ReorgLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::ReorgLayer>* cv_PtrLcv_dnn_ReorgLayerG_new_const_ReorgLayer(cv::dnn::ReorgLayer* val) {
			return new cv::Ptr<cv::dnn::ReorgLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::RequantizeLayer* cv_PtrLcv_dnn_RequantizeLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::RequantizeLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::RequantizeLayer* cv_PtrLcv_dnn_RequantizeLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::RequantizeLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_RequantizeLayerG_delete(cv::Ptr<cv::dnn::RequantizeLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_RequantizeLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::RequantizeLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_RequantizeLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::RequantizeLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::RequantizeLayer>* cv_PtrLcv_dnn_RequantizeLayerG_new_const_RequantizeLayer(cv::dnn::RequantizeLayer* val) {
			return new cv::Ptr<cv::dnn::RequantizeLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::ReshapeLayer* cv_PtrLcv_dnn_ReshapeLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::ReshapeLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::ReshapeLayer* cv_PtrLcv_dnn_ReshapeLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::ReshapeLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_ReshapeLayerG_delete(cv::Ptr<cv::dnn::ReshapeLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_ReshapeLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::ReshapeLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_ReshapeLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::ReshapeLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::ReshapeLayer>* cv_PtrLcv_dnn_ReshapeLayerG_new_const_ReshapeLayer(cv::dnn::ReshapeLayer* val) {
			return new cv::Ptr<cv::dnn::ReshapeLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::ResizeLayer* cv_PtrLcv_dnn_ResizeLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::ResizeLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::ResizeLayer* cv_PtrLcv_dnn_ResizeLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::ResizeLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_ResizeLayerG_delete(cv::Ptr<cv::dnn::ResizeLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_ResizeLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::ResizeLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_ResizeLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::ResizeLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::ResizeLayer>* cv_PtrLcv_dnn_ResizeLayerG_new_const_ResizeLayer(cv::dnn::ResizeLayer* val) {
			return new cv::Ptr<cv::dnn::ResizeLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::ScaleLayer* cv_PtrLcv_dnn_ScaleLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::ScaleLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::ScaleLayer* cv_PtrLcv_dnn_ScaleLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::ScaleLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_ScaleLayerG_delete(cv::Ptr<cv::dnn::ScaleLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_ScaleLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::ScaleLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_ScaleLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::ScaleLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::ScaleLayer>* cv_PtrLcv_dnn_ScaleLayerG_new_const_ScaleLayer(cv::dnn::ScaleLayer* val) {
			return new cv::Ptr<cv::dnn::ScaleLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::ScaleLayerInt8* cv_PtrLcv_dnn_ScaleLayerInt8G_getInnerPtr_const(const cv::Ptr<cv::dnn::ScaleLayerInt8>* instance) {
			return instance->get();
	}
	
	cv::dnn::ScaleLayerInt8* cv_PtrLcv_dnn_ScaleLayerInt8G_getInnerPtrMut(cv::Ptr<cv::dnn::ScaleLayerInt8>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_ScaleLayerInt8G_delete(cv::Ptr<cv::dnn::ScaleLayerInt8>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_ScaleLayerInt8G_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::ScaleLayerInt8>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_ScaleLayerInt8G_to_PtrOfLayer(cv::Ptr<cv::dnn::ScaleLayerInt8>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::ScaleLayer>* cv_PtrLcv_dnn_ScaleLayerInt8G_to_PtrOfScaleLayer(cv::Ptr<cv::dnn::ScaleLayerInt8>* instance) {
			return new cv::Ptr<cv::dnn::ScaleLayer>(instance->dynamicCast<cv::dnn::ScaleLayer>());
	}
	
	cv::Ptr<cv::dnn::ScaleLayerInt8>* cv_PtrLcv_dnn_ScaleLayerInt8G_new_const_ScaleLayerInt8(cv::dnn::ScaleLayerInt8* val) {
			return new cv::Ptr<cv::dnn::ScaleLayerInt8>(val);
	}
	
}

extern "C" {
	const cv::dnn::ShiftLayer* cv_PtrLcv_dnn_ShiftLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::ShiftLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::ShiftLayer* cv_PtrLcv_dnn_ShiftLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::ShiftLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_ShiftLayerG_delete(cv::Ptr<cv::dnn::ShiftLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_ShiftLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::ShiftLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_ShiftLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::ShiftLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::ShiftLayer>* cv_PtrLcv_dnn_ShiftLayerG_new_const_ShiftLayer(cv::dnn::ShiftLayer* val) {
			return new cv::Ptr<cv::dnn::ShiftLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::ShiftLayerInt8* cv_PtrLcv_dnn_ShiftLayerInt8G_getInnerPtr_const(const cv::Ptr<cv::dnn::ShiftLayerInt8>* instance) {
			return instance->get();
	}
	
	cv::dnn::ShiftLayerInt8* cv_PtrLcv_dnn_ShiftLayerInt8G_getInnerPtrMut(cv::Ptr<cv::dnn::ShiftLayerInt8>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_ShiftLayerInt8G_delete(cv::Ptr<cv::dnn::ShiftLayerInt8>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_ShiftLayerInt8G_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::ShiftLayerInt8>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_ShiftLayerInt8G_to_PtrOfLayer(cv::Ptr<cv::dnn::ShiftLayerInt8>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::ShiftLayerInt8>* cv_PtrLcv_dnn_ShiftLayerInt8G_new_const_ShiftLayerInt8(cv::dnn::ShiftLayerInt8* val) {
			return new cv::Ptr<cv::dnn::ShiftLayerInt8>(val);
	}
	
}

extern "C" {
	const cv::dnn::ShuffleChannelLayer* cv_PtrLcv_dnn_ShuffleChannelLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::ShuffleChannelLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::ShuffleChannelLayer* cv_PtrLcv_dnn_ShuffleChannelLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::ShuffleChannelLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_ShuffleChannelLayerG_delete(cv::Ptr<cv::dnn::ShuffleChannelLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_ShuffleChannelLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::ShuffleChannelLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_ShuffleChannelLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::ShuffleChannelLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::ShuffleChannelLayer>* cv_PtrLcv_dnn_ShuffleChannelLayerG_new_const_ShuffleChannelLayer(cv::dnn::ShuffleChannelLayer* val) {
			return new cv::Ptr<cv::dnn::ShuffleChannelLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::SigmoidLayer* cv_PtrLcv_dnn_SigmoidLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::SigmoidLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::SigmoidLayer* cv_PtrLcv_dnn_SigmoidLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::SigmoidLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_SigmoidLayerG_delete(cv::Ptr<cv::dnn::SigmoidLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_SigmoidLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::SigmoidLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::ActivationLayer>* cv_PtrLcv_dnn_SigmoidLayerG_to_PtrOfActivationLayer(cv::Ptr<cv::dnn::SigmoidLayer>* instance) {
			return new cv::Ptr<cv::dnn::ActivationLayer>(instance->dynamicCast<cv::dnn::ActivationLayer>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_SigmoidLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::SigmoidLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::SigmoidLayer>* cv_PtrLcv_dnn_SigmoidLayerG_new_const_SigmoidLayer(cv::dnn::SigmoidLayer* val) {
			return new cv::Ptr<cv::dnn::SigmoidLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::SliceLayer* cv_PtrLcv_dnn_SliceLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::SliceLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::SliceLayer* cv_PtrLcv_dnn_SliceLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::SliceLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_SliceLayerG_delete(cv::Ptr<cv::dnn::SliceLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_SliceLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::SliceLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_SliceLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::SliceLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::SliceLayer>* cv_PtrLcv_dnn_SliceLayerG_new_const_SliceLayer(cv::dnn::SliceLayer* val) {
			return new cv::Ptr<cv::dnn::SliceLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::SoftmaxLayer* cv_PtrLcv_dnn_SoftmaxLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::SoftmaxLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::SoftmaxLayer* cv_PtrLcv_dnn_SoftmaxLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::SoftmaxLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_SoftmaxLayerG_delete(cv::Ptr<cv::dnn::SoftmaxLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_SoftmaxLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::SoftmaxLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_SoftmaxLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::SoftmaxLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::SoftmaxLayer>* cv_PtrLcv_dnn_SoftmaxLayerG_new_const_SoftmaxLayer(cv::dnn::SoftmaxLayer* val) {
			return new cv::Ptr<cv::dnn::SoftmaxLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::SoftmaxLayerInt8* cv_PtrLcv_dnn_SoftmaxLayerInt8G_getInnerPtr_const(const cv::Ptr<cv::dnn::SoftmaxLayerInt8>* instance) {
			return instance->get();
	}
	
	cv::dnn::SoftmaxLayerInt8* cv_PtrLcv_dnn_SoftmaxLayerInt8G_getInnerPtrMut(cv::Ptr<cv::dnn::SoftmaxLayerInt8>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_SoftmaxLayerInt8G_delete(cv::Ptr<cv::dnn::SoftmaxLayerInt8>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_SoftmaxLayerInt8G_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::SoftmaxLayerInt8>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_SoftmaxLayerInt8G_to_PtrOfLayer(cv::Ptr<cv::dnn::SoftmaxLayerInt8>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::SoftmaxLayer>* cv_PtrLcv_dnn_SoftmaxLayerInt8G_to_PtrOfSoftmaxLayer(cv::Ptr<cv::dnn::SoftmaxLayerInt8>* instance) {
			return new cv::Ptr<cv::dnn::SoftmaxLayer>(instance->dynamicCast<cv::dnn::SoftmaxLayer>());
	}
	
	cv::Ptr<cv::dnn::SoftmaxLayerInt8>* cv_PtrLcv_dnn_SoftmaxLayerInt8G_new_const_SoftmaxLayerInt8(cv::dnn::SoftmaxLayerInt8* val) {
			return new cv::Ptr<cv::dnn::SoftmaxLayerInt8>(val);
	}
	
}

extern "C" {
	const cv::dnn::SplitLayer* cv_PtrLcv_dnn_SplitLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::SplitLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::SplitLayer* cv_PtrLcv_dnn_SplitLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::SplitLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_SplitLayerG_delete(cv::Ptr<cv::dnn::SplitLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_SplitLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::SplitLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_SplitLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::SplitLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::SplitLayer>* cv_PtrLcv_dnn_SplitLayerG_new_const_SplitLayer(cv::dnn::SplitLayer* val) {
			return new cv::Ptr<cv::dnn::SplitLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::SwishLayer* cv_PtrLcv_dnn_SwishLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::SwishLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::SwishLayer* cv_PtrLcv_dnn_SwishLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::SwishLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_SwishLayerG_delete(cv::Ptr<cv::dnn::SwishLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_SwishLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::SwishLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::ActivationLayer>* cv_PtrLcv_dnn_SwishLayerG_to_PtrOfActivationLayer(cv::Ptr<cv::dnn::SwishLayer>* instance) {
			return new cv::Ptr<cv::dnn::ActivationLayer>(instance->dynamicCast<cv::dnn::ActivationLayer>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_SwishLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::SwishLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::SwishLayer>* cv_PtrLcv_dnn_SwishLayerG_new_const_SwishLayer(cv::dnn::SwishLayer* val) {
			return new cv::Ptr<cv::dnn::SwishLayer>(val);
	}
	
}

extern "C" {
	const cv::dnn::TanHLayer* cv_PtrLcv_dnn_TanHLayerG_getInnerPtr_const(const cv::Ptr<cv::dnn::TanHLayer>* instance) {
			return instance->get();
	}
	
	cv::dnn::TanHLayer* cv_PtrLcv_dnn_TanHLayerG_getInnerPtrMut(cv::Ptr<cv::dnn::TanHLayer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_dnn_TanHLayerG_delete(cv::Ptr<cv::dnn::TanHLayer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_dnn_TanHLayerG_to_PtrOfAlgorithm(cv::Ptr<cv::dnn::TanHLayer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::dnn::ActivationLayer>* cv_PtrLcv_dnn_TanHLayerG_to_PtrOfActivationLayer(cv::Ptr<cv::dnn::TanHLayer>* instance) {
			return new cv::Ptr<cv::dnn::ActivationLayer>(instance->dynamicCast<cv::dnn::ActivationLayer>());
	}
	
	cv::Ptr<cv::dnn::Layer>* cv_PtrLcv_dnn_TanHLayerG_to_PtrOfLayer(cv::Ptr<cv::dnn::TanHLayer>* instance) {
			return new cv::Ptr<cv::dnn::Layer>(instance->dynamicCast<cv::dnn::Layer>());
	}
	
	cv::Ptr<cv::dnn::TanHLayer>* cv_PtrLcv_dnn_TanHLayerG_new_const_TanHLayer(cv::dnn::TanHLayer* val) {
			return new cv::Ptr<cv::dnn::TanHLayer>(val);
	}
	
}

extern "C" {
	std::pair<cv::dnn::Backend, cv::dnn::Target>* std_pairLcv_dnn_Backend__cv_dnn_TargetG_new_const_Backend_Target(cv::dnn::Backend arg, cv::dnn::Target arg_1) {
			std::pair<cv::dnn::Backend, cv::dnn::Target>* ret = new std::pair<cv::dnn::Backend, cv::dnn::Target>(arg, arg_1);
			return ret;
	}
	
	void std_pairLcv_dnn_Backend__cv_dnn_TargetG_get_0_const(const std::pair<cv::dnn::Backend, cv::dnn::Target>* instance, cv::dnn::Backend* ocvrs_return) {
			cv::dnn::Backend ret = std::get<0>(*instance);
			*ocvrs_return = ret;
	}
	
	void std_pairLcv_dnn_Backend__cv_dnn_TargetG_get_1_const(const std::pair<cv::dnn::Backend, cv::dnn::Target>* instance, cv::dnn::Target* ocvrs_return) {
			cv::dnn::Target ret = std::get<1>(*instance);
			*ocvrs_return = ret;
	}
	
	void std_pairLcv_dnn_Backend__cv_dnn_TargetG_delete(std::pair<cv::dnn::Backend, cv::dnn::Target>* instance) {
			delete instance;
	}
	
}

extern "C" {
	std::vector<cv::dnn::MatShape>* std_vectorLcv_dnn_MatShapeG_new_const() {
			std::vector<cv::dnn::MatShape>* ret = new std::vector<cv::dnn::MatShape>();
			return ret;
	}
	
	void std_vectorLcv_dnn_MatShapeG_delete(std::vector<cv::dnn::MatShape>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_dnn_MatShapeG_len_const(const std::vector<cv::dnn::MatShape>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_dnn_MatShapeG_isEmpty_const(const std::vector<cv::dnn::MatShape>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_dnn_MatShapeG_capacity_const(const std::vector<cv::dnn::MatShape>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_dnn_MatShapeG_shrinkToFit(std::vector<cv::dnn::MatShape>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_dnn_MatShapeG_reserve_size_t(std::vector<cv::dnn::MatShape>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_dnn_MatShapeG_remove_size_t(std::vector<cv::dnn::MatShape>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_dnn_MatShapeG_swap_size_t_size_t(std::vector<cv::dnn::MatShape>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_dnn_MatShapeG_clear(std::vector<cv::dnn::MatShape>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_dnn_MatShapeG_push_const_MatShape(std::vector<cv::dnn::MatShape>* instance, const cv::dnn::MatShape* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_dnn_MatShapeG_insert_size_t_const_MatShape(std::vector<cv::dnn::MatShape>* instance, size_t index, const cv::dnn::MatShape* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_dnn_MatShapeG_get_const_size_t(const std::vector<cv::dnn::MatShape>* instance, size_t index, cv::dnn::MatShape** ocvrs_return) {
			cv::dnn::MatShape ret = (*instance)[index];
			*ocvrs_return = new cv::dnn::MatShape(ret);
	}
	
	void std_vectorLcv_dnn_MatShapeG_set_size_t_const_MatShape(std::vector<cv::dnn::MatShape>* instance, size_t index, const cv::dnn::MatShape* val) {
			(*instance)[index] = *val;
	}
	
	void std_vectorLcv_dnn_MatShapeG_inputArray_const(const std::vector<cv::dnn::MatShape>* instance, Result<cv::_InputArray*>* ocvrs_return) {
		try {
			cv::_InputArray ret = cv::_InputArray(*instance);
			Ok(new cv::_InputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_dnn_MatShapeG_outputArray(std::vector<cv::dnn::MatShape>* instance, Result<cv::_OutputArray*>* ocvrs_return) {
		try {
			cv::_OutputArray ret = cv::_OutputArray(*instance);
			Ok(new cv::_OutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void std_vectorLcv_dnn_MatShapeG_inputOutputArray(std::vector<cv::dnn::MatShape>* instance, Result<cv::_InputOutputArray*>* ocvrs_return) {
		try {
			cv::_InputOutputArray ret = cv::_InputOutputArray(*instance);
			Ok(new cv::_InputOutputArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
}


extern "C" {
	std::vector<cv::Ptr<cv::dnn::BackendNode>>* std_vectorLcv_PtrLcv_dnn_BackendNodeGG_new_const() {
			std::vector<cv::Ptr<cv::dnn::BackendNode>>* ret = new std::vector<cv::Ptr<cv::dnn::BackendNode>>();
			return ret;
	}
	
	void std_vectorLcv_PtrLcv_dnn_BackendNodeGG_delete(std::vector<cv::Ptr<cv::dnn::BackendNode>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_PtrLcv_dnn_BackendNodeGG_len_const(const std::vector<cv::Ptr<cv::dnn::BackendNode>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_PtrLcv_dnn_BackendNodeGG_isEmpty_const(const std::vector<cv::Ptr<cv::dnn::BackendNode>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_PtrLcv_dnn_BackendNodeGG_capacity_const(const std::vector<cv::Ptr<cv::dnn::BackendNode>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_PtrLcv_dnn_BackendNodeGG_shrinkToFit(std::vector<cv::Ptr<cv::dnn::BackendNode>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_PtrLcv_dnn_BackendNodeGG_reserve_size_t(std::vector<cv::Ptr<cv::dnn::BackendNode>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_PtrLcv_dnn_BackendNodeGG_remove_size_t(std::vector<cv::Ptr<cv::dnn::BackendNode>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_PtrLcv_dnn_BackendNodeGG_swap_size_t_size_t(std::vector<cv::Ptr<cv::dnn::BackendNode>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_PtrLcv_dnn_BackendNodeGG_clear(std::vector<cv::Ptr<cv::dnn::BackendNode>>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_PtrLcv_dnn_BackendNodeGG_push_const_PtrLBackendNodeG(std::vector<cv::Ptr<cv::dnn::BackendNode>>* instance, const cv::Ptr<cv::dnn::BackendNode>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_PtrLcv_dnn_BackendNodeGG_insert_size_t_const_PtrLBackendNodeG(std::vector<cv::Ptr<cv::dnn::BackendNode>>* instance, size_t index, const cv::Ptr<cv::dnn::BackendNode>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_PtrLcv_dnn_BackendNodeGG_get_const_size_t(const std::vector<cv::Ptr<cv::dnn::BackendNode>>* instance, size_t index, cv::Ptr<cv::dnn::BackendNode>** ocvrs_return) {
			cv::Ptr<cv::dnn::BackendNode> ret = (*instance)[index];
			*ocvrs_return = new cv::Ptr<cv::dnn::BackendNode>(ret);
	}
	
	void std_vectorLcv_PtrLcv_dnn_BackendNodeGG_set_size_t_const_PtrLBackendNodeG(std::vector<cv::Ptr<cv::dnn::BackendNode>>* instance, size_t index, const cv::Ptr<cv::dnn::BackendNode>* val) {
			(*instance)[index] = *val;
	}
	
}


extern "C" {
	std::vector<cv::Ptr<cv::dnn::BackendWrapper>>* std_vectorLcv_PtrLcv_dnn_BackendWrapperGG_new_const() {
			std::vector<cv::Ptr<cv::dnn::BackendWrapper>>* ret = new std::vector<cv::Ptr<cv::dnn::BackendWrapper>>();
			return ret;
	}
	
	void std_vectorLcv_PtrLcv_dnn_BackendWrapperGG_delete(std::vector<cv::Ptr<cv::dnn::BackendWrapper>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_PtrLcv_dnn_BackendWrapperGG_len_const(const std::vector<cv::Ptr<cv::dnn::BackendWrapper>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_PtrLcv_dnn_BackendWrapperGG_isEmpty_const(const std::vector<cv::Ptr<cv::dnn::BackendWrapper>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_PtrLcv_dnn_BackendWrapperGG_capacity_const(const std::vector<cv::Ptr<cv::dnn::BackendWrapper>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_PtrLcv_dnn_BackendWrapperGG_shrinkToFit(std::vector<cv::Ptr<cv::dnn::BackendWrapper>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_PtrLcv_dnn_BackendWrapperGG_reserve_size_t(std::vector<cv::Ptr<cv::dnn::BackendWrapper>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_PtrLcv_dnn_BackendWrapperGG_remove_size_t(std::vector<cv::Ptr<cv::dnn::BackendWrapper>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_PtrLcv_dnn_BackendWrapperGG_swap_size_t_size_t(std::vector<cv::Ptr<cv::dnn::BackendWrapper>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_PtrLcv_dnn_BackendWrapperGG_clear(std::vector<cv::Ptr<cv::dnn::BackendWrapper>>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_PtrLcv_dnn_BackendWrapperGG_push_const_PtrLBackendWrapperG(std::vector<cv::Ptr<cv::dnn::BackendWrapper>>* instance, const cv::Ptr<cv::dnn::BackendWrapper>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_PtrLcv_dnn_BackendWrapperGG_insert_size_t_const_PtrLBackendWrapperG(std::vector<cv::Ptr<cv::dnn::BackendWrapper>>* instance, size_t index, const cv::Ptr<cv::dnn::BackendWrapper>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_PtrLcv_dnn_BackendWrapperGG_get_const_size_t(const std::vector<cv::Ptr<cv::dnn::BackendWrapper>>* instance, size_t index, cv::Ptr<cv::dnn::BackendWrapper>** ocvrs_return) {
			cv::Ptr<cv::dnn::BackendWrapper> ret = (*instance)[index];
			*ocvrs_return = new cv::Ptr<cv::dnn::BackendWrapper>(ret);
	}
	
	void std_vectorLcv_PtrLcv_dnn_BackendWrapperGG_set_size_t_const_PtrLBackendWrapperG(std::vector<cv::Ptr<cv::dnn::BackendWrapper>>* instance, size_t index, const cv::Ptr<cv::dnn::BackendWrapper>* val) {
			(*instance)[index] = *val;
	}
	
}


extern "C" {
	std::vector<cv::Ptr<cv::dnn::Layer>>* std_vectorLcv_PtrLcv_dnn_LayerGG_new_const() {
			std::vector<cv::Ptr<cv::dnn::Layer>>* ret = new std::vector<cv::Ptr<cv::dnn::Layer>>();
			return ret;
	}
	
	void std_vectorLcv_PtrLcv_dnn_LayerGG_delete(std::vector<cv::Ptr<cv::dnn::Layer>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_PtrLcv_dnn_LayerGG_len_const(const std::vector<cv::Ptr<cv::dnn::Layer>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_PtrLcv_dnn_LayerGG_isEmpty_const(const std::vector<cv::Ptr<cv::dnn::Layer>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_PtrLcv_dnn_LayerGG_capacity_const(const std::vector<cv::Ptr<cv::dnn::Layer>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_PtrLcv_dnn_LayerGG_shrinkToFit(std::vector<cv::Ptr<cv::dnn::Layer>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_PtrLcv_dnn_LayerGG_reserve_size_t(std::vector<cv::Ptr<cv::dnn::Layer>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_PtrLcv_dnn_LayerGG_remove_size_t(std::vector<cv::Ptr<cv::dnn::Layer>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_PtrLcv_dnn_LayerGG_swap_size_t_size_t(std::vector<cv::Ptr<cv::dnn::Layer>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_PtrLcv_dnn_LayerGG_clear(std::vector<cv::Ptr<cv::dnn::Layer>>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_PtrLcv_dnn_LayerGG_push_const_PtrLLayerG(std::vector<cv::Ptr<cv::dnn::Layer>>* instance, const cv::Ptr<cv::dnn::Layer>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_PtrLcv_dnn_LayerGG_insert_size_t_const_PtrLLayerG(std::vector<cv::Ptr<cv::dnn::Layer>>* instance, size_t index, const cv::Ptr<cv::dnn::Layer>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_PtrLcv_dnn_LayerGG_get_const_size_t(const std::vector<cv::Ptr<cv::dnn::Layer>>* instance, size_t index, cv::Ptr<cv::dnn::Layer>** ocvrs_return) {
			cv::Ptr<cv::dnn::Layer> ret = (*instance)[index];
			*ocvrs_return = new cv::Ptr<cv::dnn::Layer>(ret);
	}
	
	void std_vectorLcv_PtrLcv_dnn_LayerGG_set_size_t_const_PtrLLayerG(std::vector<cv::Ptr<cv::dnn::Layer>>* instance, size_t index, const cv::Ptr<cv::dnn::Layer>* val) {
			(*instance)[index] = *val;
	}
	
}


extern "C" {
	std::vector<cv::dnn::Target>* std_vectorLcv_dnn_TargetG_new_const() {
			std::vector<cv::dnn::Target>* ret = new std::vector<cv::dnn::Target>();
			return ret;
	}
	
	void std_vectorLcv_dnn_TargetG_delete(std::vector<cv::dnn::Target>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_dnn_TargetG_len_const(const std::vector<cv::dnn::Target>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_dnn_TargetG_isEmpty_const(const std::vector<cv::dnn::Target>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_dnn_TargetG_capacity_const(const std::vector<cv::dnn::Target>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_dnn_TargetG_shrinkToFit(std::vector<cv::dnn::Target>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_dnn_TargetG_reserve_size_t(std::vector<cv::dnn::Target>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_dnn_TargetG_remove_size_t(std::vector<cv::dnn::Target>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_dnn_TargetG_swap_size_t_size_t(std::vector<cv::dnn::Target>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_dnn_TargetG_clear(std::vector<cv::dnn::Target>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_dnn_TargetG_push_const_Target(std::vector<cv::dnn::Target>* instance, const cv::dnn::Target val) {
			instance->push_back(val);
	}
	
	void std_vectorLcv_dnn_TargetG_insert_size_t_const_Target(std::vector<cv::dnn::Target>* instance, size_t index, const cv::dnn::Target val) {
			instance->insert(instance->begin() + index, val);
	}
	
	void std_vectorLcv_dnn_TargetG_get_const_size_t(const std::vector<cv::dnn::Target>* instance, size_t index, cv::dnn::Target* ocvrs_return) {
			cv::dnn::Target ret = (*instance)[index];
			*ocvrs_return = ret;
	}
	
	void std_vectorLcv_dnn_TargetG_set_size_t_const_Target(std::vector<cv::dnn::Target>* instance, size_t index, const cv::dnn::Target val) {
			(*instance)[index] = val;
	}
	
	std::vector<cv::dnn::Target>* std_vectorLcv_dnn_TargetG_clone_const(const std::vector<cv::dnn::Target>* instance) {
			std::vector<cv::dnn::Target> ret = std::vector<cv::dnn::Target>(*instance);
			return new std::vector<cv::dnn::Target>(ret);
	}
	
	const cv::dnn::Target* std_vectorLcv_dnn_TargetG_data_const(const std::vector<cv::dnn::Target>* instance) {
			const cv::dnn::Target* ret = instance->data();
			return ret;
	}
	
	cv::dnn::Target* std_vectorLcv_dnn_TargetG_dataMut(std::vector<cv::dnn::Target>* instance) {
			cv::dnn::Target* ret = instance->data();
			return ret;
	}
	
	std::vector<cv::dnn::Target>* cv_fromSlice_const_const_TargetX_size_t(const cv::dnn::Target* data, size_t len) {
			return new std::vector<cv::dnn::Target>(data, data + len);
	}
	
}


extern "C" {
	std::vector<std::pair<cv::dnn::Backend, cv::dnn::Target>>* std_vectorLstd_pairLcv_dnn_Backend__cv_dnn_TargetGG_new_const() {
			std::vector<std::pair<cv::dnn::Backend, cv::dnn::Target>>* ret = new std::vector<std::pair<cv::dnn::Backend, cv::dnn::Target>>();
			return ret;
	}
	
	void std_vectorLstd_pairLcv_dnn_Backend__cv_dnn_TargetGG_delete(std::vector<std::pair<cv::dnn::Backend, cv::dnn::Target>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLstd_pairLcv_dnn_Backend__cv_dnn_TargetGG_len_const(const std::vector<std::pair<cv::dnn::Backend, cv::dnn::Target>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLstd_pairLcv_dnn_Backend__cv_dnn_TargetGG_isEmpty_const(const std::vector<std::pair<cv::dnn::Backend, cv::dnn::Target>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLstd_pairLcv_dnn_Backend__cv_dnn_TargetGG_capacity_const(const std::vector<std::pair<cv::dnn::Backend, cv::dnn::Target>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLstd_pairLcv_dnn_Backend__cv_dnn_TargetGG_shrinkToFit(std::vector<std::pair<cv::dnn::Backend, cv::dnn::Target>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLstd_pairLcv_dnn_Backend__cv_dnn_TargetGG_reserve_size_t(std::vector<std::pair<cv::dnn::Backend, cv::dnn::Target>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLstd_pairLcv_dnn_Backend__cv_dnn_TargetGG_remove_size_t(std::vector<std::pair<cv::dnn::Backend, cv::dnn::Target>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLstd_pairLcv_dnn_Backend__cv_dnn_TargetGG_swap_size_t_size_t(std::vector<std::pair<cv::dnn::Backend, cv::dnn::Target>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLstd_pairLcv_dnn_Backend__cv_dnn_TargetGG_clear(std::vector<std::pair<cv::dnn::Backend, cv::dnn::Target>>* instance) {
			instance->clear();
	}
	
	void std_vectorLstd_pairLcv_dnn_Backend__cv_dnn_TargetGG_push_const_pairLcv_dnn_Backend__cv_dnn_TargetG(std::vector<std::pair<cv::dnn::Backend, cv::dnn::Target>>* instance, const std::pair<cv::dnn::Backend, cv::dnn::Target>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLstd_pairLcv_dnn_Backend__cv_dnn_TargetGG_insert_size_t_const_pairLcv_dnn_Backend__cv_dnn_TargetG(std::vector<std::pair<cv::dnn::Backend, cv::dnn::Target>>* instance, size_t index, const std::pair<cv::dnn::Backend, cv::dnn::Target>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLstd_pairLcv_dnn_Backend__cv_dnn_TargetGG_get_const_size_t(const std::vector<std::pair<cv::dnn::Backend, cv::dnn::Target>>* instance, size_t index, std::pair<cv::dnn::Backend, cv::dnn::Target>** ocvrs_return) {
			std::pair<cv::dnn::Backend, cv::dnn::Target> ret = (*instance)[index];
			*ocvrs_return = new std::pair<cv::dnn::Backend, cv::dnn::Target>(ret);
	}
	
	void std_vectorLstd_pairLcv_dnn_Backend__cv_dnn_TargetGG_set_size_t_const_pairLcv_dnn_Backend__cv_dnn_TargetG(std::vector<std::pair<cv::dnn::Backend, cv::dnn::Target>>* instance, size_t index, const std::pair<cv::dnn::Backend, cv::dnn::Target>* val) {
			(*instance)[index] = *val;
	}
	
}


extern "C" {
	std::vector<std::vector<cv::dnn::MatShape>>* std_vectorLstd_vectorLcv_dnn_MatShapeGG_new_const() {
			std::vector<std::vector<cv::dnn::MatShape>>* ret = new std::vector<std::vector<cv::dnn::MatShape>>();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_dnn_MatShapeGG_delete(std::vector<std::vector<cv::dnn::MatShape>>* instance) {
			delete instance;
	}
	
	size_t std_vectorLstd_vectorLcv_dnn_MatShapeGG_len_const(const std::vector<std::vector<cv::dnn::MatShape>>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLstd_vectorLcv_dnn_MatShapeGG_isEmpty_const(const std::vector<std::vector<cv::dnn::MatShape>>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLstd_vectorLcv_dnn_MatShapeGG_capacity_const(const std::vector<std::vector<cv::dnn::MatShape>>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLstd_vectorLcv_dnn_MatShapeGG_shrinkToFit(std::vector<std::vector<cv::dnn::MatShape>>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLstd_vectorLcv_dnn_MatShapeGG_reserve_size_t(std::vector<std::vector<cv::dnn::MatShape>>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLstd_vectorLcv_dnn_MatShapeGG_remove_size_t(std::vector<std::vector<cv::dnn::MatShape>>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLstd_vectorLcv_dnn_MatShapeGG_swap_size_t_size_t(std::vector<std::vector<cv::dnn::MatShape>>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLstd_vectorLcv_dnn_MatShapeGG_clear(std::vector<std::vector<cv::dnn::MatShape>>* instance) {
			instance->clear();
	}
	
	void std_vectorLstd_vectorLcv_dnn_MatShapeGG_push_const_vectorLMatShapeG(std::vector<std::vector<cv::dnn::MatShape>>* instance, const std::vector<cv::dnn::MatShape>* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLstd_vectorLcv_dnn_MatShapeGG_insert_size_t_const_vectorLMatShapeG(std::vector<std::vector<cv::dnn::MatShape>>* instance, size_t index, const std::vector<cv::dnn::MatShape>* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLstd_vectorLcv_dnn_MatShapeGG_get_const_size_t(const std::vector<std::vector<cv::dnn::MatShape>>* instance, size_t index, std::vector<cv::dnn::MatShape>** ocvrs_return) {
			std::vector<cv::dnn::MatShape> ret = (*instance)[index];
			*ocvrs_return = new std::vector<cv::dnn::MatShape>(ret);
	}
	
	void std_vectorLstd_vectorLcv_dnn_MatShapeGG_set_size_t_const_vectorLMatShapeG(std::vector<std::vector<cv::dnn::MatShape>>* instance, size_t index, const std::vector<cv::dnn::MatShape>* val) {
			(*instance)[index] = *val;
	}
	
}



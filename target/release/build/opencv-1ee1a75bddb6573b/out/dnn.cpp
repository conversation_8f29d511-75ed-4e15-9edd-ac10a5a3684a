#include "dnn.hpp"
#include "dnn_types.hpp"

extern "C" {
	void cv_dnn_NMSBoxes_const_vectorLRect2dGR_const_vectorLfloatGR_const_float_const_float_vectorLintGR(const std::vector<cv::Rect2d>* bboxes, const std::vector<float>* scores, const float score_threshold, const float nms_threshold, std::vector<int>* indices, ResultVoid* ocvrs_return) {
		try {
			cv::dnn::NMSBoxes(*bboxes, *scores, score_threshold, nms_threshold, *indices);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_NMSBoxes_const_vectorLRect2dGR_const_vectorLfloatGR_const_float_const_float_vectorLintGR_const_float_const_int(const std::vector<cv::Rect2d>* bboxes, const std::vector<float>* scores, const float score_threshold, const float nms_threshold, std::vector<int>* indices, const float eta, const int top_k, ResultVoid* ocvrs_return) {
		try {
			cv::dnn::NMSBoxes(*bboxes, *scores, score_threshold, nms_threshold, *indices, eta, top_k);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_NMSBoxes_const_vectorLRectGR_const_vectorLfloatGR_const_float_const_float_vectorLintGR(const std::vector<cv::Rect>* bboxes, const std::vector<float>* scores, const float score_threshold, const float nms_threshold, std::vector<int>* indices, ResultVoid* ocvrs_return) {
		try {
			cv::dnn::NMSBoxes(*bboxes, *scores, score_threshold, nms_threshold, *indices);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_NMSBoxes_const_vectorLRectGR_const_vectorLfloatGR_const_float_const_float_vectorLintGR_const_float_const_int(const std::vector<cv::Rect>* bboxes, const std::vector<float>* scores, const float score_threshold, const float nms_threshold, std::vector<int>* indices, const float eta, const int top_k, ResultVoid* ocvrs_return) {
		try {
			cv::dnn::NMSBoxes(*bboxes, *scores, score_threshold, nms_threshold, *indices, eta, top_k);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_NMSBoxes_const_vectorLRotatedRectGR_const_vectorLfloatGR_const_float_const_float_vectorLintGR(const std::vector<cv::RotatedRect>* bboxes, const std::vector<float>* scores, const float score_threshold, const float nms_threshold, std::vector<int>* indices, ResultVoid* ocvrs_return) {
		try {
			cv::dnn::NMSBoxes(*bboxes, *scores, score_threshold, nms_threshold, *indices);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_NMSBoxes_const_vectorLRotatedRectGR_const_vectorLfloatGR_const_float_const_float_vectorLintGR_const_float_const_int(const std::vector<cv::RotatedRect>* bboxes, const std::vector<float>* scores, const float score_threshold, const float nms_threshold, std::vector<int>* indices, const float eta, const int top_k, ResultVoid* ocvrs_return) {
		try {
			cv::dnn::NMSBoxes(*bboxes, *scores, score_threshold, nms_threshold, *indices, eta, top_k);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_blobFromImage_const__InputArrayR(const cv::_InputArray* image, Result<cv::Mat*>* ocvrs_return) {
		try {
			cv::Mat ret = cv::dnn::blobFromImage(*image);
			Ok(new cv::Mat(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_blobFromImage_const__InputArrayR_const__OutputArrayR(const cv::_InputArray* image, const cv::_OutputArray* blob, ResultVoid* ocvrs_return) {
		try {
			cv::dnn::blobFromImage(*image, *blob);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_blobFromImage_const__InputArrayR_const__OutputArrayR_double_const_SizeR_const_ScalarR_bool_bool_int(const cv::_InputArray* image, const cv::_OutputArray* blob, double scalefactor, const cv::Size* size, const cv::Scalar* mean, bool swapRB, bool crop, int ddepth, ResultVoid* ocvrs_return) {
		try {
			cv::dnn::blobFromImage(*image, *blob, scalefactor, *size, *mean, swapRB, crop, ddepth);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_blobFromImage_const__InputArrayR_double_const_SizeR_const_ScalarR_bool_bool_int(const cv::_InputArray* image, double scalefactor, const cv::Size* size, const cv::Scalar* mean, bool swapRB, bool crop, int ddepth, Result<cv::Mat*>* ocvrs_return) {
		try {
			cv::Mat ret = cv::dnn::blobFromImage(*image, scalefactor, *size, *mean, swapRB, crop, ddepth);
			Ok(new cv::Mat(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_blobFromImages_const__InputArrayR(const cv::_InputArray* images, Result<cv::Mat*>* ocvrs_return) {
		try {
			cv::Mat ret = cv::dnn::blobFromImages(*images);
			Ok(new cv::Mat(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_blobFromImages_const__InputArrayR_const__OutputArrayR(const cv::_InputArray* images, const cv::_OutputArray* blob, ResultVoid* ocvrs_return) {
		try {
			cv::dnn::blobFromImages(*images, *blob);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_blobFromImages_const__InputArrayR_const__OutputArrayR_double_Size_const_ScalarR_bool_bool_int(const cv::_InputArray* images, const cv::_OutputArray* blob, double scalefactor, cv::Size* size, const cv::Scalar* mean, bool swapRB, bool crop, int ddepth, ResultVoid* ocvrs_return) {
		try {
			cv::dnn::blobFromImages(*images, *blob, scalefactor, *size, *mean, swapRB, crop, ddepth);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_blobFromImages_const__InputArrayR_double_Size_const_ScalarR_bool_bool_int(const cv::_InputArray* images, double scalefactor, cv::Size* size, const cv::Scalar* mean, bool swapRB, bool crop, int ddepth, Result<cv::Mat*>* ocvrs_return) {
		try {
			cv::Mat ret = cv::dnn::blobFromImages(*images, scalefactor, *size, *mean, swapRB, crop, ddepth);
			Ok(new cv::Mat(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_concat_const_MatShapeR_const_MatShapeR(const cv::dnn::MatShape* a, const cv::dnn::MatShape* b, Result<cv::dnn::MatShape*>* ocvrs_return) {
		try {
			cv::dnn::MatShape ret = cv::dnn::concat(*a, *b);
			Ok(new cv::dnn::MatShape(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_enableModelDiagnostics_bool(bool isDiagnosticsMode, ResultVoid* ocvrs_return) {
		try {
			cv::dnn::enableModelDiagnostics(isDiagnosticsMode);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_getAvailableBackends(Result<std::vector<std::pair<cv::dnn::Backend, cv::dnn::Target>>*>* ocvrs_return) {
		try {
			std::vector<std::pair<cv::dnn::Backend, cv::dnn::Target>> ret = cv::dnn::getAvailableBackends();
			Ok(new std::vector<std::pair<cv::dnn::Backend, cv::dnn::Target>>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_getAvailableTargets_Backend(cv::dnn::Backend be, Result<std::vector<cv::dnn::Target>*>* ocvrs_return) {
		try {
			std::vector<cv::dnn::Target> ret = cv::dnn::getAvailableTargets(be);
			Ok(new std::vector<cv::dnn::Target>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_getInferenceEngineBackendType(Result<void*>* ocvrs_return) {
		try {
			cv::String ret = cv::dnn::getInferenceEngineBackendType();
			Ok(ocvrs_create_string(ret.c_str()), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_getInferenceEngineCPUType(Result<void*>* ocvrs_return) {
		try {
			cv::String ret = cv::dnn::getInferenceEngineCPUType();
			Ok(ocvrs_create_string(ret.c_str()), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_getInferenceEngineVPUType(Result<void*>* ocvrs_return) {
		try {
			cv::String ret = cv::dnn::getInferenceEngineVPUType();
			Ok(ocvrs_create_string(ret.c_str()), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_getPlane_const_MatR_int_int(const cv::Mat* m, int n, int cn, Result<cv::Mat*>* ocvrs_return) {
		try {
			cv::Mat ret = cv::dnn::getPlane(*m, n, cn);
			Ok(new cv::Mat(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_imagesFromBlob_const_MatR_const__OutputArrayR(const cv::Mat* blob_, const cv::_OutputArray* images_, ResultVoid* ocvrs_return) {
		try {
			cv::dnn::imagesFromBlob(*blob_, *images_);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_print_const_MatShapeR(const cv::dnn::MatShape* shape, ResultVoid* ocvrs_return) {
		try {
			cv::dnn::print(*shape);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_print_const_MatShapeR_const_StringR(const cv::dnn::MatShape* shape, const char* name, ResultVoid* ocvrs_return) {
		try {
			cv::dnn::print(*shape, std::string(name));
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNetFromCaffe_const_StringR(const char* prototxt, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNetFromCaffe(std::string(prototxt));
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNetFromCaffe_const_StringR_const_StringR(const char* prototxt, const char* caffeModel, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNetFromCaffe(std::string(prototxt), std::string(caffeModel));
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNetFromCaffe_const_charX_size_t(const char* bufferProto, size_t lenProto, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNetFromCaffe(bufferProto, lenProto);
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNetFromCaffe_const_charX_size_t_const_charX_size_t(const char* bufferProto, size_t lenProto, const char* bufferModel, size_t lenModel, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNetFromCaffe(bufferProto, lenProto, bufferModel, lenModel);
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNetFromCaffe_const_vectorLunsigned_charGR(const std::vector<unsigned char>* bufferProto, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNetFromCaffe(*bufferProto);
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNetFromCaffe_const_vectorLunsigned_charGR_const_vectorLunsigned_charGR(const std::vector<unsigned char>* bufferProto, const std::vector<unsigned char>* bufferModel, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNetFromCaffe(*bufferProto, *bufferModel);
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNetFromDarknet_const_StringR(const char* cfgFile, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNetFromDarknet(std::string(cfgFile));
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNetFromDarknet_const_StringR_const_StringR(const char* cfgFile, const char* darknetModel, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNetFromDarknet(std::string(cfgFile), std::string(darknetModel));
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNetFromDarknet_const_charX_size_t(const char* bufferCfg, size_t lenCfg, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNetFromDarknet(bufferCfg, lenCfg);
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNetFromDarknet_const_charX_size_t_const_charX_size_t(const char* bufferCfg, size_t lenCfg, const char* bufferModel, size_t lenModel, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNetFromDarknet(bufferCfg, lenCfg, bufferModel, lenModel);
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNetFromDarknet_const_vectorLunsigned_charGR(const std::vector<unsigned char>* bufferCfg, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNetFromDarknet(*bufferCfg);
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNetFromDarknet_const_vectorLunsigned_charGR_const_vectorLunsigned_charGR(const std::vector<unsigned char>* bufferCfg, const std::vector<unsigned char>* bufferModel, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNetFromDarknet(*bufferCfg, *bufferModel);
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNetFromModelOptimizer_const_StringR_const_StringR(const char* xml, const char* bin, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNetFromModelOptimizer(std::string(xml), std::string(bin));
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNetFromModelOptimizer_const_unsigned_charX_size_t_const_unsigned_charX_size_t(const unsigned char* bufferModelConfigPtr, size_t bufferModelConfigSize, const unsigned char* bufferWeightsPtr, size_t bufferWeightsSize, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNetFromModelOptimizer(bufferModelConfigPtr, bufferModelConfigSize, bufferWeightsPtr, bufferWeightsSize);
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNetFromModelOptimizer_const_vectorLunsigned_charGR_const_vectorLunsigned_charGR(const std::vector<unsigned char>* bufferModelConfig, const std::vector<unsigned char>* bufferWeights, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNetFromModelOptimizer(*bufferModelConfig, *bufferWeights);
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNetFromONNX_const_StringR(const char* onnxFile, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNetFromONNX(std::string(onnxFile));
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNetFromONNX_const_charX_size_t(const char* buffer, size_t sizeBuffer, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNetFromONNX(buffer, sizeBuffer);
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNetFromONNX_const_vectorLunsigned_charGR(const std::vector<unsigned char>* buffer, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNetFromONNX(*buffer);
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNetFromTensorflow_const_StringR(const char* model, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNetFromTensorflow(std::string(model));
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNetFromTensorflow_const_StringR_const_StringR(const char* model, const char* config, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNetFromTensorflow(std::string(model), std::string(config));
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNetFromTensorflow_const_charX_size_t(const char* bufferModel, size_t lenModel, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNetFromTensorflow(bufferModel, lenModel);
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNetFromTensorflow_const_charX_size_t_const_charX_size_t(const char* bufferModel, size_t lenModel, const char* bufferConfig, size_t lenConfig, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNetFromTensorflow(bufferModel, lenModel, bufferConfig, lenConfig);
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNetFromTensorflow_const_vectorLunsigned_charGR(const std::vector<unsigned char>* bufferModel, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNetFromTensorflow(*bufferModel);
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNetFromTensorflow_const_vectorLunsigned_charGR_const_vectorLunsigned_charGR(const std::vector<unsigned char>* bufferModel, const std::vector<unsigned char>* bufferConfig, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNetFromTensorflow(*bufferModel, *bufferConfig);
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNetFromTorch_const_StringR(const char* model, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNetFromTorch(std::string(model));
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNetFromTorch_const_StringR_bool_bool(const char* model, bool isBinary, bool evaluate, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNetFromTorch(std::string(model), isBinary, evaluate);
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNet_const_StringR(const char* model, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNet(std::string(model));
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNet_const_StringR_const_StringR_const_StringR(const char* model, const char* config, const char* framework, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNet(std::string(model), std::string(config), std::string(framework));
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNet_const_StringR_const_vectorLunsigned_charGR(const char* framework, const std::vector<unsigned char>* bufferModel, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNet(std::string(framework), *bufferModel);
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readNet_const_StringR_const_vectorLunsigned_charGR_const_vectorLunsigned_charGR(const char* framework, const std::vector<unsigned char>* bufferModel, const std::vector<unsigned char>* bufferConfig, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::readNet(std::string(framework), *bufferModel, *bufferConfig);
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readTensorFromONNX_const_StringR(const char* path, Result<cv::Mat*>* ocvrs_return) {
		try {
			cv::Mat ret = cv::dnn::readTensorFromONNX(std::string(path));
			Ok(new cv::Mat(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readTorchBlob_const_StringR(const char* filename, Result<cv::Mat*>* ocvrs_return) {
		try {
			cv::Mat ret = cv::dnn::readTorchBlob(std::string(filename));
			Ok(new cv::Mat(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_readTorchBlob_const_StringR_bool(const char* filename, bool isBinary, Result<cv::Mat*>* ocvrs_return) {
		try {
			cv::Mat ret = cv::dnn::readTorchBlob(std::string(filename), isBinary);
			Ok(new cv::Mat(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_releaseHDDLPlugin(ResultVoid* ocvrs_return) {
		try {
			cv::dnn::releaseHDDLPlugin();
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_resetMyriadDevice(ResultVoid* ocvrs_return) {
		try {
			cv::dnn::resetMyriadDevice();
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_setInferenceEngineBackendType_const_StringR(const char* newBackendType, Result<void*>* ocvrs_return) {
		try {
			cv::String ret = cv::dnn::setInferenceEngineBackendType(std::string(newBackendType));
			Ok(ocvrs_create_string(ret.c_str()), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_shape_const_MatR(const cv::Mat* mat, Result<cv::dnn::MatShape*>* ocvrs_return) {
		try {
			cv::dnn::MatShape ret = cv::dnn::shape(*mat);
			Ok(new cv::dnn::MatShape(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_shape_const_MatSizeR(const cv::MatSize* sz, Result<cv::dnn::MatShape*>* ocvrs_return) {
		try {
			cv::dnn::MatShape ret = cv::dnn::shape(*sz);
			Ok(new cv::dnn::MatShape(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_shape_const_UMatR(const cv::UMat* mat, Result<cv::dnn::MatShape*>* ocvrs_return) {
		try {
			cv::dnn::MatShape ret = cv::dnn::shape(*mat);
			Ok(new cv::dnn::MatShape(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_shape_const_intX_const_int(const int* dims, const int n, Result<cv::dnn::MatShape*>* ocvrs_return) {
		try {
			cv::dnn::MatShape ret = cv::dnn::shape(dims, n);
			Ok(new cv::dnn::MatShape(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_shape_int(int a0, Result<cv::dnn::MatShape*>* ocvrs_return) {
		try {
			cv::dnn::MatShape ret = cv::dnn::shape(a0);
			Ok(new cv::dnn::MatShape(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_shape_int_int_int_int(int a0, int a1, int a2, int a3, Result<cv::dnn::MatShape*>* ocvrs_return) {
		try {
			cv::dnn::MatShape ret = cv::dnn::shape(a0, a1, a2, a3);
			Ok(new cv::dnn::MatShape(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_shrinkCaffeModel_const_StringR_const_StringR(const char* src, const char* dst, ResultVoid* ocvrs_return) {
		try {
			cv::dnn::shrinkCaffeModel(std::string(src), std::string(dst));
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_shrinkCaffeModel_const_StringR_const_StringR_const_vectorLStringGR(const char* src, const char* dst, const std::vector<cv::String>* layersTypes, ResultVoid* ocvrs_return) {
		try {
			cv::dnn::shrinkCaffeModel(std::string(src), std::string(dst), *layersTypes);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_slice_const_MatR_const__RangeR(const cv::Mat* m, const cv::dnn::_Range* r0, Result<cv::Mat*>* ocvrs_return) {
		try {
			cv::Mat ret = cv::dnn::slice(*m, *r0);
			Ok(new cv::Mat(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_slice_const_MatR_const__RangeR_const__RangeR(const cv::Mat* m, const cv::dnn::_Range* r0, const cv::dnn::_Range* r1, Result<cv::Mat*>* ocvrs_return) {
		try {
			cv::Mat ret = cv::dnn::slice(*m, *r0, *r1);
			Ok(new cv::Mat(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_slice_const_MatR_const__RangeR_const__RangeR_const__RangeR(const cv::Mat* m, const cv::dnn::_Range* r0, const cv::dnn::_Range* r1, const cv::dnn::_Range* r2, Result<cv::Mat*>* ocvrs_return) {
		try {
			cv::Mat ret = cv::dnn::slice(*m, *r0, *r1, *r2);
			Ok(new cv::Mat(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_slice_const_MatR_const__RangeR_const__RangeR_const__RangeR_const__RangeR(const cv::Mat* m, const cv::dnn::_Range* r0, const cv::dnn::_Range* r1, const cv::dnn::_Range* r2, const cv::dnn::_Range* r3, Result<cv::Mat*>* ocvrs_return) {
		try {
			cv::Mat ret = cv::dnn::slice(*m, *r0, *r1, *r2, *r3);
			Ok(new cv::Mat(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_softNMSBoxes_const_vectorLRectGR_const_vectorLfloatGR_vectorLfloatGR_const_float_const_float_vectorLintGR(const std::vector<cv::Rect>* bboxes, const std::vector<float>* scores, std::vector<float>* updated_scores, const float score_threshold, const float nms_threshold, std::vector<int>* indices, ResultVoid* ocvrs_return) {
		try {
			cv::dnn::softNMSBoxes(*bboxes, *scores, *updated_scores, score_threshold, nms_threshold, *indices);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_softNMSBoxes_const_vectorLRectGR_const_vectorLfloatGR_vectorLfloatGR_const_float_const_float_vectorLintGR_size_t_const_float_SoftNMSMethod(const std::vector<cv::Rect>* bboxes, const std::vector<float>* scores, std::vector<float>* updated_scores, const float score_threshold, const float nms_threshold, std::vector<int>* indices, size_t top_k, const float sigma, cv::dnn::SoftNMSMethod method, ResultVoid* ocvrs_return) {
		try {
			cv::dnn::softNMSBoxes(*bboxes, *scores, *updated_scores, score_threshold, nms_threshold, *indices, top_k, sigma, method);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_toString_const_MatShapeR(const cv::dnn::MatShape* shape, Result<void*>* ocvrs_return) {
		try {
			std::string ret = cv::dnn::toString(*shape);
			Ok(ocvrs_create_string(ret.c_str()), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_toString_const_MatShapeR_const_StringR(const cv::dnn::MatShape* shape, const char* name, Result<void*>* ocvrs_return) {
		try {
			std::string ret = cv::dnn::toString(*shape, std::string(name));
			Ok(ocvrs_create_string(ret.c_str()), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_total_const_MatShapeR(const cv::dnn::MatShape* shape, Result<int>* ocvrs_return) {
		try {
			int ret = cv::dnn::total(*shape);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_total_const_MatShapeR_int_int(const cv::dnn::MatShape* shape, int start, int end, Result<int>* ocvrs_return) {
		try {
			int ret = cv::dnn::total(*shape, start, end);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_writeTextGraph_const_StringR_const_StringR(const char* model, const char* output, ResultVoid* ocvrs_return) {
		try {
			cv::dnn::writeTextGraph(std::string(model), std::string(output));
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_AbsLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::AbsLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::AbsLayer> ret = cv::dnn::AbsLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::AbsLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::dnn::ActivationLayer* cv_dnn_AbsLayer_to_ActivationLayer(cv::dnn::AbsLayer* instance) {
			return dynamic_cast<cv::dnn::ActivationLayer*>(instance);
	}
	
	cv::Algorithm* cv_dnn_AbsLayer_to_Algorithm(cv::dnn::AbsLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_AbsLayer_to_Layer(cv::dnn::AbsLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_AbsLayer_delete(cv::dnn::AbsLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_AccumLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::AccumLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::AccumLayer> ret = cv::dnn::AccumLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::AccumLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::Algorithm* cv_dnn_AccumLayer_to_Algorithm(cv::dnn::AccumLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_AccumLayer_to_Layer(cv::dnn::AccumLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_AccumLayer_delete(cv::dnn::AccumLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_ActivationLayer_forwardSlice_const_const_floatX_floatX_int_size_t_int_int(const cv::dnn::ActivationLayer* instance, const float* src, float* dst, int len, size_t outPlaneSize, int cn0, int cn1, ResultVoid* ocvrs_return) {
		try {
			instance->forwardSlice(src, dst, len, outPlaneSize, cn0, cn1);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_ActivationLayer_forwardSlice_const_const_intX_const_intX_intX_int_size_t_int_int(const cv::dnn::ActivationLayer* instance, const int* src, const int* lut, int* dst, int len, size_t outPlaneSize, int cn0, int cn1, ResultVoid* ocvrs_return) {
		try {
			instance->forwardSlice(src, lut, dst, len, outPlaneSize, cn0, cn1);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::dnn::AbsLayer* cv_dnn_ActivationLayer_to_AbsLayer(cv::dnn::ActivationLayer* instance) {
			return dynamic_cast<cv::dnn::AbsLayer*>(instance);
	}
	
	cv::dnn::ActivationLayerInt8* cv_dnn_ActivationLayer_to_ActivationLayerInt8(cv::dnn::ActivationLayer* instance) {
			return dynamic_cast<cv::dnn::ActivationLayerInt8*>(instance);
	}
	
	cv::dnn::BNLLLayer* cv_dnn_ActivationLayer_to_BNLLLayer(cv::dnn::ActivationLayer* instance) {
			return dynamic_cast<cv::dnn::BNLLLayer*>(instance);
	}
	
	cv::dnn::BatchNormLayer* cv_dnn_ActivationLayer_to_BatchNormLayer(cv::dnn::ActivationLayer* instance) {
			return dynamic_cast<cv::dnn::BatchNormLayer*>(instance);
	}
	
	cv::dnn::BatchNormLayerInt8* cv_dnn_ActivationLayer_to_BatchNormLayerInt8(cv::dnn::ActivationLayer* instance) {
			return dynamic_cast<cv::dnn::BatchNormLayerInt8*>(instance);
	}
	
	cv::dnn::ChannelsPReLULayer* cv_dnn_ActivationLayer_to_ChannelsPReLULayer(cv::dnn::ActivationLayer* instance) {
			return dynamic_cast<cv::dnn::ChannelsPReLULayer*>(instance);
	}
	
	cv::dnn::ELULayer* cv_dnn_ActivationLayer_to_ELULayer(cv::dnn::ActivationLayer* instance) {
			return dynamic_cast<cv::dnn::ELULayer*>(instance);
	}
	
	cv::dnn::ExpLayer* cv_dnn_ActivationLayer_to_ExpLayer(cv::dnn::ActivationLayer* instance) {
			return dynamic_cast<cv::dnn::ExpLayer*>(instance);
	}
	
	cv::dnn::MishLayer* cv_dnn_ActivationLayer_to_MishLayer(cv::dnn::ActivationLayer* instance) {
			return dynamic_cast<cv::dnn::MishLayer*>(instance);
	}
	
	cv::dnn::PowerLayer* cv_dnn_ActivationLayer_to_PowerLayer(cv::dnn::ActivationLayer* instance) {
			return dynamic_cast<cv::dnn::PowerLayer*>(instance);
	}
	
	cv::dnn::ReLU6Layer* cv_dnn_ActivationLayer_to_ReLU6Layer(cv::dnn::ActivationLayer* instance) {
			return dynamic_cast<cv::dnn::ReLU6Layer*>(instance);
	}
	
	cv::dnn::ReLULayer* cv_dnn_ActivationLayer_to_ReLULayer(cv::dnn::ActivationLayer* instance) {
			return dynamic_cast<cv::dnn::ReLULayer*>(instance);
	}
	
	cv::dnn::SigmoidLayer* cv_dnn_ActivationLayer_to_SigmoidLayer(cv::dnn::ActivationLayer* instance) {
			return dynamic_cast<cv::dnn::SigmoidLayer*>(instance);
	}
	
	cv::dnn::SwishLayer* cv_dnn_ActivationLayer_to_SwishLayer(cv::dnn::ActivationLayer* instance) {
			return dynamic_cast<cv::dnn::SwishLayer*>(instance);
	}
	
	cv::dnn::TanHLayer* cv_dnn_ActivationLayer_to_TanHLayer(cv::dnn::ActivationLayer* instance) {
			return dynamic_cast<cv::dnn::TanHLayer*>(instance);
	}
	
	cv::Algorithm* cv_dnn_ActivationLayer_to_Algorithm(cv::dnn::ActivationLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_ActivationLayer_to_Layer(cv::dnn::ActivationLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_ActivationLayer_delete(cv::dnn::ActivationLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_ActivationLayerInt8_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::ActivationLayerInt8>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::ActivationLayerInt8> ret = cv::dnn::ActivationLayerInt8::create(*params);
			Ok(new cv::Ptr<cv::dnn::ActivationLayerInt8>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::dnn::ActivationLayer* cv_dnn_ActivationLayerInt8_to_ActivationLayer(cv::dnn::ActivationLayerInt8* instance) {
			return dynamic_cast<cv::dnn::ActivationLayer*>(instance);
	}
	
	cv::Algorithm* cv_dnn_ActivationLayerInt8_to_Algorithm(cv::dnn::ActivationLayerInt8* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_ActivationLayerInt8_to_Layer(cv::dnn::ActivationLayerInt8* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_ActivationLayerInt8_delete(cv::dnn::ActivationLayerInt8* instance) {
			delete instance;
	}
	
	void cv_dnn_BNLLLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::BNLLLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::BNLLLayer> ret = cv::dnn::BNLLLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::BNLLLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::dnn::ActivationLayer* cv_dnn_BNLLLayer_to_ActivationLayer(cv::dnn::BNLLLayer* instance) {
			return dynamic_cast<cv::dnn::ActivationLayer*>(instance);
	}
	
	cv::Algorithm* cv_dnn_BNLLLayer_to_Algorithm(cv::dnn::BNLLLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_BNLLLayer_to_Layer(cv::dnn::BNLLLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_BNLLLayer_delete(cv::dnn::BNLLLayer* instance) {
			delete instance;
	}
	
	int cv_dnn_BackendNode_propBackendId_const(const cv::dnn::BackendNode* instance) {
			int ret = instance->backendId;
			return ret;
	}
	
	void cv_dnn_BackendNode_propBackendId_const_int(cv::dnn::BackendNode* instance, const int val) {
			instance->backendId = val;
	}
	
	void cv_dnn_BackendNode_delete(cv::dnn::BackendNode* instance) {
			delete instance;
	}
	
	void cv_dnn_BackendWrapper_copyToHost(cv::dnn::BackendWrapper* instance, ResultVoid* ocvrs_return) {
		try {
			instance->copyToHost();
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_BackendWrapper_setHostDirty(cv::dnn::BackendWrapper* instance, ResultVoid* ocvrs_return) {
		try {
			instance->setHostDirty();
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	int cv_dnn_BackendWrapper_propBackendId_const(const cv::dnn::BackendWrapper* instance) {
			int ret = instance->backendId;
			return ret;
	}
	
	void cv_dnn_BackendWrapper_propBackendId_const_int(cv::dnn::BackendWrapper* instance, const int val) {
			instance->backendId = val;
	}
	
	int cv_dnn_BackendWrapper_propTargetId_const(const cv::dnn::BackendWrapper* instance) {
			int ret = instance->targetId;
			return ret;
	}
	
	void cv_dnn_BackendWrapper_propTargetId_const_int(cv::dnn::BackendWrapper* instance, const int val) {
			instance->targetId = val;
	}
	
	void cv_dnn_BackendWrapper_delete(cv::dnn::BackendWrapper* instance) {
			delete instance;
	}
	
	void cv_dnn_BaseConvolutionLayer_propKernel_const(const cv::dnn::BaseConvolutionLayer* instance, cv::Size* ocvrs_return) {
			cv::Size ret = instance->kernel;
			*ocvrs_return = ret;
	}
	
	void cv_dnn_BaseConvolutionLayer_propKernel_const_Size(cv::dnn::BaseConvolutionLayer* instance, const cv::Size* val) {
			instance->kernel = *val;
	}
	
	void cv_dnn_BaseConvolutionLayer_propStride_const(const cv::dnn::BaseConvolutionLayer* instance, cv::Size* ocvrs_return) {
			cv::Size ret = instance->stride;
			*ocvrs_return = ret;
	}
	
	void cv_dnn_BaseConvolutionLayer_propStride_const_Size(cv::dnn::BaseConvolutionLayer* instance, const cv::Size* val) {
			instance->stride = *val;
	}
	
	void cv_dnn_BaseConvolutionLayer_propPad_const(const cv::dnn::BaseConvolutionLayer* instance, cv::Size* ocvrs_return) {
			cv::Size ret = instance->pad;
			*ocvrs_return = ret;
	}
	
	void cv_dnn_BaseConvolutionLayer_propPad_const_Size(cv::dnn::BaseConvolutionLayer* instance, const cv::Size* val) {
			instance->pad = *val;
	}
	
	void cv_dnn_BaseConvolutionLayer_propDilation_const(const cv::dnn::BaseConvolutionLayer* instance, cv::Size* ocvrs_return) {
			cv::Size ret = instance->dilation;
			*ocvrs_return = ret;
	}
	
	void cv_dnn_BaseConvolutionLayer_propDilation_const_Size(cv::dnn::BaseConvolutionLayer* instance, const cv::Size* val) {
			instance->dilation = *val;
	}
	
	void cv_dnn_BaseConvolutionLayer_propAdjustPad_const(const cv::dnn::BaseConvolutionLayer* instance, cv::Size* ocvrs_return) {
			cv::Size ret = instance->adjustPad;
			*ocvrs_return = ret;
	}
	
	void cv_dnn_BaseConvolutionLayer_propAdjustPad_const_Size(cv::dnn::BaseConvolutionLayer* instance, const cv::Size* val) {
			instance->adjustPad = *val;
	}
	
	std::vector<size_t>* cv_dnn_BaseConvolutionLayer_propAdjust_pads_const(const cv::dnn::BaseConvolutionLayer* instance) {
			std::vector<size_t> ret = instance->adjust_pads;
			return new std::vector<size_t>(ret);
	}
	
	void cv_dnn_BaseConvolutionLayer_propAdjust_pads_const_vectorLsize_tG(cv::dnn::BaseConvolutionLayer* instance, const std::vector<size_t>* val) {
			instance->adjust_pads = *val;
	}
	
	std::vector<size_t>* cv_dnn_BaseConvolutionLayer_propKernel_size_const(const cv::dnn::BaseConvolutionLayer* instance) {
			std::vector<size_t> ret = instance->kernel_size;
			return new std::vector<size_t>(ret);
	}
	
	void cv_dnn_BaseConvolutionLayer_propKernel_size_const_vectorLsize_tG(cv::dnn::BaseConvolutionLayer* instance, const std::vector<size_t>* val) {
			instance->kernel_size = *val;
	}
	
	std::vector<size_t>* cv_dnn_BaseConvolutionLayer_propStrides_const(const cv::dnn::BaseConvolutionLayer* instance) {
			std::vector<size_t> ret = instance->strides;
			return new std::vector<size_t>(ret);
	}
	
	void cv_dnn_BaseConvolutionLayer_propStrides_const_vectorLsize_tG(cv::dnn::BaseConvolutionLayer* instance, const std::vector<size_t>* val) {
			instance->strides = *val;
	}
	
	std::vector<size_t>* cv_dnn_BaseConvolutionLayer_propDilations_const(const cv::dnn::BaseConvolutionLayer* instance) {
			std::vector<size_t> ret = instance->dilations;
			return new std::vector<size_t>(ret);
	}
	
	void cv_dnn_BaseConvolutionLayer_propDilations_const_vectorLsize_tG(cv::dnn::BaseConvolutionLayer* instance, const std::vector<size_t>* val) {
			instance->dilations = *val;
	}
	
	std::vector<size_t>* cv_dnn_BaseConvolutionLayer_propPads_begin_const(const cv::dnn::BaseConvolutionLayer* instance) {
			std::vector<size_t> ret = instance->pads_begin;
			return new std::vector<size_t>(ret);
	}
	
	void cv_dnn_BaseConvolutionLayer_propPads_begin_const_vectorLsize_tG(cv::dnn::BaseConvolutionLayer* instance, const std::vector<size_t>* val) {
			instance->pads_begin = *val;
	}
	
	std::vector<size_t>* cv_dnn_BaseConvolutionLayer_propPads_end_const(const cv::dnn::BaseConvolutionLayer* instance) {
			std::vector<size_t> ret = instance->pads_end;
			return new std::vector<size_t>(ret);
	}
	
	void cv_dnn_BaseConvolutionLayer_propPads_end_const_vectorLsize_tG(cv::dnn::BaseConvolutionLayer* instance, const std::vector<size_t>* val) {
			instance->pads_end = *val;
	}
	
	void* cv_dnn_BaseConvolutionLayer_propPadMode_const(const cv::dnn::BaseConvolutionLayer* instance) {
			cv::String ret = instance->padMode;
			return ocvrs_create_string(ret.c_str());
	}
	
	void cv_dnn_BaseConvolutionLayer_propPadMode_const_String(cv::dnn::BaseConvolutionLayer* instance, const char* val) {
			instance->padMode = std::string(val);
	}
	
	int cv_dnn_BaseConvolutionLayer_propNumOutput_const(const cv::dnn::BaseConvolutionLayer* instance) {
			int ret = instance->numOutput;
			return ret;
	}
	
	void cv_dnn_BaseConvolutionLayer_propNumOutput_const_int(cv::dnn::BaseConvolutionLayer* instance, const int val) {
			instance->numOutput = val;
	}
	
	cv::Algorithm* cv_dnn_BaseConvolutionLayer_to_Algorithm(cv::dnn::BaseConvolutionLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_BaseConvolutionLayer_to_Layer(cv::dnn::BaseConvolutionLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_BaseConvolutionLayer_delete(cv::dnn::BaseConvolutionLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_BatchNormLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::BatchNormLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::BatchNormLayer> ret = cv::dnn::BatchNormLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::BatchNormLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	bool cv_dnn_BatchNormLayer_propHasWeights_const(const cv::dnn::BatchNormLayer* instance) {
			bool ret = instance->hasWeights;
			return ret;
	}
	
	void cv_dnn_BatchNormLayer_propHasWeights_const_bool(cv::dnn::BatchNormLayer* instance, const bool val) {
			instance->hasWeights = val;
	}
	
	bool cv_dnn_BatchNormLayer_propHasBias_const(const cv::dnn::BatchNormLayer* instance) {
			bool ret = instance->hasBias;
			return ret;
	}
	
	void cv_dnn_BatchNormLayer_propHasBias_const_bool(cv::dnn::BatchNormLayer* instance, const bool val) {
			instance->hasBias = val;
	}
	
	float cv_dnn_BatchNormLayer_propEpsilon_const(const cv::dnn::BatchNormLayer* instance) {
			float ret = instance->epsilon;
			return ret;
	}
	
	void cv_dnn_BatchNormLayer_propEpsilon_const_float(cv::dnn::BatchNormLayer* instance, const float val) {
			instance->epsilon = val;
	}
	
	cv::dnn::ActivationLayer* cv_dnn_BatchNormLayer_to_ActivationLayer(cv::dnn::BatchNormLayer* instance) {
			return dynamic_cast<cv::dnn::ActivationLayer*>(instance);
	}
	
	cv::Algorithm* cv_dnn_BatchNormLayer_to_Algorithm(cv::dnn::BatchNormLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_BatchNormLayer_to_Layer(cv::dnn::BatchNormLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_BatchNormLayer_delete(cv::dnn::BatchNormLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_BatchNormLayerInt8_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::BatchNormLayerInt8>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::BatchNormLayerInt8> ret = cv::dnn::BatchNormLayerInt8::create(*params);
			Ok(new cv::Ptr<cv::dnn::BatchNormLayerInt8>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	float cv_dnn_BatchNormLayerInt8_propInput_sc_const(const cv::dnn::BatchNormLayerInt8* instance) {
			float ret = instance->input_sc;
			return ret;
	}
	
	void cv_dnn_BatchNormLayerInt8_propInput_sc_const_float(cv::dnn::BatchNormLayerInt8* instance, const float val) {
			instance->input_sc = val;
	}
	
	float cv_dnn_BatchNormLayerInt8_propOutput_sc_const(const cv::dnn::BatchNormLayerInt8* instance) {
			float ret = instance->output_sc;
			return ret;
	}
	
	void cv_dnn_BatchNormLayerInt8_propOutput_sc_const_float(cv::dnn::BatchNormLayerInt8* instance, const float val) {
			instance->output_sc = val;
	}
	
	int cv_dnn_BatchNormLayerInt8_propInput_zp_const(const cv::dnn::BatchNormLayerInt8* instance) {
			int ret = instance->input_zp;
			return ret;
	}
	
	void cv_dnn_BatchNormLayerInt8_propInput_zp_const_int(cv::dnn::BatchNormLayerInt8* instance, const int val) {
			instance->input_zp = val;
	}
	
	int cv_dnn_BatchNormLayerInt8_propOutput_zp_const(const cv::dnn::BatchNormLayerInt8* instance) {
			int ret = instance->output_zp;
			return ret;
	}
	
	void cv_dnn_BatchNormLayerInt8_propOutput_zp_const_int(cv::dnn::BatchNormLayerInt8* instance, const int val) {
			instance->output_zp = val;
	}
	
	cv::dnn::ActivationLayer* cv_dnn_BatchNormLayerInt8_to_ActivationLayer(cv::dnn::BatchNormLayerInt8* instance) {
			return dynamic_cast<cv::dnn::ActivationLayer*>(instance);
	}
	
	cv::Algorithm* cv_dnn_BatchNormLayerInt8_to_Algorithm(cv::dnn::BatchNormLayerInt8* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::BatchNormLayer* cv_dnn_BatchNormLayerInt8_to_BatchNormLayer(cv::dnn::BatchNormLayerInt8* instance) {
			return dynamic_cast<cv::dnn::BatchNormLayer*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_BatchNormLayerInt8_to_Layer(cv::dnn::BatchNormLayerInt8* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_BatchNormLayerInt8_delete(cv::dnn::BatchNormLayerInt8* instance) {
			delete instance;
	}
	
	void cv_dnn_BlankLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::Layer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::Layer> ret = cv::dnn::BlankLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::Layer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::Algorithm* cv_dnn_BlankLayer_to_Algorithm(cv::dnn::BlankLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_BlankLayer_to_Layer(cv::dnn::BlankLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_BlankLayer_delete(cv::dnn::BlankLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_ChannelsPReLULayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::Layer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::Layer> ret = cv::dnn::ChannelsPReLULayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::Layer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::dnn::ActivationLayer* cv_dnn_ChannelsPReLULayer_to_ActivationLayer(cv::dnn::ChannelsPReLULayer* instance) {
			return dynamic_cast<cv::dnn::ActivationLayer*>(instance);
	}
	
	cv::Algorithm* cv_dnn_ChannelsPReLULayer_to_Algorithm(cv::dnn::ChannelsPReLULayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_ChannelsPReLULayer_to_Layer(cv::dnn::ChannelsPReLULayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_ChannelsPReLULayer_delete(cv::dnn::ChannelsPReLULayer* instance) {
			delete instance;
	}
	
	void cv_dnn_ClassificationModel_ClassificationModel_const_StringR_const_StringR(const char* model, const char* config, Result<cv::dnn::ClassificationModel*>* ocvrs_return) {
		try {
			cv::dnn::ClassificationModel* ret = new cv::dnn::ClassificationModel(std::string(model), std::string(config));
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_ClassificationModel_ClassificationModel_const_StringR(const char* model, Result<cv::dnn::ClassificationModel*>* ocvrs_return) {
		try {
			cv::dnn::ClassificationModel* ret = new cv::dnn::ClassificationModel(std::string(model));
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_ClassificationModel_ClassificationModel_const_NetR(const cv::dnn::Net* network, Result<cv::dnn::ClassificationModel*>* ocvrs_return) {
		try {
			cv::dnn::ClassificationModel* ret = new cv::dnn::ClassificationModel(*network);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_ClassificationModel_classify_const__InputArrayR(cv::dnn::ClassificationModel* instance, const cv::_InputArray* frame, Result<std::pair<int, float>*>* ocvrs_return) {
		try {
			std::pair<int, float> ret = instance->classify(*frame);
			Ok(new std::pair<int, float>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_ClassificationModel_classify_const__InputArrayR_intR_floatR(cv::dnn::ClassificationModel* instance, const cv::_InputArray* frame, int* classId, float* conf, ResultVoid* ocvrs_return) {
		try {
			instance->classify(*frame, *classId, *conf);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::dnn::ClassificationModel* cv_dnn_ClassificationModel_implicitClone_const(const cv::dnn::ClassificationModel* instance) {
			return new cv::dnn::ClassificationModel(*instance);
	}
	
	cv::dnn::Model* cv_dnn_ClassificationModel_to_Model(cv::dnn::ClassificationModel* instance) {
			return dynamic_cast<cv::dnn::Model*>(instance);
	}
	
	void cv_dnn_ClassificationModel_delete(cv::dnn::ClassificationModel* instance) {
			delete instance;
	}
	
	void cv_dnn_ConcatLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::ConcatLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::ConcatLayer> ret = cv::dnn::ConcatLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::ConcatLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	int cv_dnn_ConcatLayer_propAxis_const(const cv::dnn::ConcatLayer* instance) {
			int ret = instance->axis;
			return ret;
	}
	
	void cv_dnn_ConcatLayer_propAxis_const_int(cv::dnn::ConcatLayer* instance, const int val) {
			instance->axis = val;
	}
	
	bool cv_dnn_ConcatLayer_propPadding_const(const cv::dnn::ConcatLayer* instance) {
			bool ret = instance->padding;
			return ret;
	}
	
	void cv_dnn_ConcatLayer_propPadding_const_bool(cv::dnn::ConcatLayer* instance, const bool val) {
			instance->padding = val;
	}
	
	int cv_dnn_ConcatLayer_propPaddingValue_const(const cv::dnn::ConcatLayer* instance) {
			int ret = instance->paddingValue;
			return ret;
	}
	
	void cv_dnn_ConcatLayer_propPaddingValue_const_int(cv::dnn::ConcatLayer* instance, const int val) {
			instance->paddingValue = val;
	}
	
	cv::Algorithm* cv_dnn_ConcatLayer_to_Algorithm(cv::dnn::ConcatLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_ConcatLayer_to_Layer(cv::dnn::ConcatLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_ConcatLayer_delete(cv::dnn::ConcatLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_ConstLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::Layer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::Layer> ret = cv::dnn::ConstLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::Layer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::Algorithm* cv_dnn_ConstLayer_to_Algorithm(cv::dnn::ConstLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_ConstLayer_to_Layer(cv::dnn::ConstLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_ConstLayer_delete(cv::dnn::ConstLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_ConvolutionLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::BaseConvolutionLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::BaseConvolutionLayer> ret = cv::dnn::ConvolutionLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::BaseConvolutionLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::Algorithm* cv_dnn_ConvolutionLayer_to_Algorithm(cv::dnn::ConvolutionLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::BaseConvolutionLayer* cv_dnn_ConvolutionLayer_to_BaseConvolutionLayer(cv::dnn::ConvolutionLayer* instance) {
			return dynamic_cast<cv::dnn::BaseConvolutionLayer*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_ConvolutionLayer_to_Layer(cv::dnn::ConvolutionLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_ConvolutionLayer_delete(cv::dnn::ConvolutionLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_ConvolutionLayerInt8_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::BaseConvolutionLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::BaseConvolutionLayer> ret = cv::dnn::ConvolutionLayerInt8::create(*params);
			Ok(new cv::Ptr<cv::dnn::BaseConvolutionLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	int cv_dnn_ConvolutionLayerInt8_propInput_zp_const(const cv::dnn::ConvolutionLayerInt8* instance) {
			int ret = instance->input_zp;
			return ret;
	}
	
	void cv_dnn_ConvolutionLayerInt8_propInput_zp_const_int(cv::dnn::ConvolutionLayerInt8* instance, const int val) {
			instance->input_zp = val;
	}
	
	int cv_dnn_ConvolutionLayerInt8_propOutput_zp_const(const cv::dnn::ConvolutionLayerInt8* instance) {
			int ret = instance->output_zp;
			return ret;
	}
	
	void cv_dnn_ConvolutionLayerInt8_propOutput_zp_const_int(cv::dnn::ConvolutionLayerInt8* instance, const int val) {
			instance->output_zp = val;
	}
	
	float cv_dnn_ConvolutionLayerInt8_propOutput_sc_const(const cv::dnn::ConvolutionLayerInt8* instance) {
			float ret = instance->output_sc;
			return ret;
	}
	
	void cv_dnn_ConvolutionLayerInt8_propOutput_sc_const_float(cv::dnn::ConvolutionLayerInt8* instance, const float val) {
			instance->output_sc = val;
	}
	
	cv::Algorithm* cv_dnn_ConvolutionLayerInt8_to_Algorithm(cv::dnn::ConvolutionLayerInt8* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::BaseConvolutionLayer* cv_dnn_ConvolutionLayerInt8_to_BaseConvolutionLayer(cv::dnn::ConvolutionLayerInt8* instance) {
			return dynamic_cast<cv::dnn::BaseConvolutionLayer*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_ConvolutionLayerInt8_to_Layer(cv::dnn::ConvolutionLayerInt8* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_ConvolutionLayerInt8_delete(cv::dnn::ConvolutionLayerInt8* instance) {
			delete instance;
	}
	
	void cv_dnn_CorrelationLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::CorrelationLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::CorrelationLayer> ret = cv::dnn::CorrelationLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::CorrelationLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::Algorithm* cv_dnn_CorrelationLayer_to_Algorithm(cv::dnn::CorrelationLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_CorrelationLayer_to_Layer(cv::dnn::CorrelationLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_CorrelationLayer_delete(cv::dnn::CorrelationLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_CropAndResizeLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::Layer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::Layer> ret = cv::dnn::CropAndResizeLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::Layer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::Algorithm* cv_dnn_CropAndResizeLayer_to_Algorithm(cv::dnn::CropAndResizeLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_CropAndResizeLayer_to_Layer(cv::dnn::CropAndResizeLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_CropAndResizeLayer_delete(cv::dnn::CropAndResizeLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_CropLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::Layer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::Layer> ret = cv::dnn::CropLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::Layer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::Algorithm* cv_dnn_CropLayer_to_Algorithm(cv::dnn::CropLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_CropLayer_to_Layer(cv::dnn::CropLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_CropLayer_delete(cv::dnn::CropLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_CumSumLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::CumSumLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::CumSumLayer> ret = cv::dnn::CumSumLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::CumSumLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	int cv_dnn_CumSumLayer_propExclusive_const(const cv::dnn::CumSumLayer* instance) {
			int ret = instance->exclusive;
			return ret;
	}
	
	void cv_dnn_CumSumLayer_propExclusive_const_int(cv::dnn::CumSumLayer* instance, const int val) {
			instance->exclusive = val;
	}
	
	int cv_dnn_CumSumLayer_propReverse_const(const cv::dnn::CumSumLayer* instance) {
			int ret = instance->reverse;
			return ret;
	}
	
	void cv_dnn_CumSumLayer_propReverse_const_int(cv::dnn::CumSumLayer* instance, const int val) {
			instance->reverse = val;
	}
	
	cv::Algorithm* cv_dnn_CumSumLayer_to_Algorithm(cv::dnn::CumSumLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_CumSumLayer_to_Layer(cv::dnn::CumSumLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_CumSumLayer_delete(cv::dnn::CumSumLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_DataAugmentationLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::DataAugmentationLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::DataAugmentationLayer> ret = cv::dnn::DataAugmentationLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::DataAugmentationLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::Algorithm* cv_dnn_DataAugmentationLayer_to_Algorithm(cv::dnn::DataAugmentationLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_DataAugmentationLayer_to_Layer(cv::dnn::DataAugmentationLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_DataAugmentationLayer_delete(cv::dnn::DataAugmentationLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_DeconvolutionLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::BaseConvolutionLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::BaseConvolutionLayer> ret = cv::dnn::DeconvolutionLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::BaseConvolutionLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::Algorithm* cv_dnn_DeconvolutionLayer_to_Algorithm(cv::dnn::DeconvolutionLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::BaseConvolutionLayer* cv_dnn_DeconvolutionLayer_to_BaseConvolutionLayer(cv::dnn::DeconvolutionLayer* instance) {
			return dynamic_cast<cv::dnn::BaseConvolutionLayer*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_DeconvolutionLayer_to_Layer(cv::dnn::DeconvolutionLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_DeconvolutionLayer_delete(cv::dnn::DeconvolutionLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_DequantizeLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::DequantizeLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::DequantizeLayer> ret = cv::dnn::DequantizeLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::DequantizeLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	float cv_dnn_DequantizeLayer_propScale_const(const cv::dnn::DequantizeLayer* instance) {
			float ret = instance->scale;
			return ret;
	}
	
	void cv_dnn_DequantizeLayer_propScale_const_float(cv::dnn::DequantizeLayer* instance, const float val) {
			instance->scale = val;
	}
	
	int cv_dnn_DequantizeLayer_propZeropoint_const(const cv::dnn::DequantizeLayer* instance) {
			int ret = instance->zeropoint;
			return ret;
	}
	
	void cv_dnn_DequantizeLayer_propZeropoint_const_int(cv::dnn::DequantizeLayer* instance, const int val) {
			instance->zeropoint = val;
	}
	
	cv::Algorithm* cv_dnn_DequantizeLayer_to_Algorithm(cv::dnn::DequantizeLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_DequantizeLayer_to_Layer(cv::dnn::DequantizeLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_DequantizeLayer_delete(cv::dnn::DequantizeLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_DetectionModel_DetectionModel_const_StringR_const_StringR(const char* model, const char* config, Result<cv::dnn::DetectionModel*>* ocvrs_return) {
		try {
			cv::dnn::DetectionModel* ret = new cv::dnn::DetectionModel(std::string(model), std::string(config));
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DetectionModel_DetectionModel_const_StringR(const char* model, Result<cv::dnn::DetectionModel*>* ocvrs_return) {
		try {
			cv::dnn::DetectionModel* ret = new cv::dnn::DetectionModel(std::string(model));
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DetectionModel_DetectionModel_const_NetR(const cv::dnn::Net* network, Result<cv::dnn::DetectionModel*>* ocvrs_return) {
		try {
			cv::dnn::DetectionModel* ret = new cv::dnn::DetectionModel(*network);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DetectionModel_DetectionModel(Result<cv::dnn::DetectionModel*>* ocvrs_return) {
		try {
			cv::dnn::DetectionModel* ret = new cv::dnn::DetectionModel();
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DetectionModel_setNmsAcrossClasses_bool(cv::dnn::DetectionModel* instance, bool value, Result<cv::dnn::DetectionModel*>* ocvrs_return) {
		try {
			cv::dnn::DetectionModel ret = instance->setNmsAcrossClasses(value);
			Ok(new cv::dnn::DetectionModel(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DetectionModel_getNmsAcrossClasses(cv::dnn::DetectionModel* instance, Result<bool>* ocvrs_return) {
		try {
			bool ret = instance->getNmsAcrossClasses();
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DetectionModel_detect_const__InputArrayR_vectorLintGR_vectorLfloatGR_vectorLRectGR_float_float(cv::dnn::DetectionModel* instance, const cv::_InputArray* frame, std::vector<int>* classIds, std::vector<float>* confidences, std::vector<cv::Rect>* boxes, float confThreshold, float nmsThreshold, ResultVoid* ocvrs_return) {
		try {
			instance->detect(*frame, *classIds, *confidences, *boxes, confThreshold, nmsThreshold);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DetectionModel_detect_const__InputArrayR_vectorLintGR_vectorLfloatGR_vectorLRectGR(cv::dnn::DetectionModel* instance, const cv::_InputArray* frame, std::vector<int>* classIds, std::vector<float>* confidences, std::vector<cv::Rect>* boxes, ResultVoid* ocvrs_return) {
		try {
			instance->detect(*frame, *classIds, *confidences, *boxes);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::dnn::DetectionModel* cv_dnn_DetectionModel_implicitClone_const(const cv::dnn::DetectionModel* instance) {
			return new cv::dnn::DetectionModel(*instance);
	}
	
	cv::dnn::Model* cv_dnn_DetectionModel_to_Model(cv::dnn::DetectionModel* instance) {
			return dynamic_cast<cv::dnn::Model*>(instance);
	}
	
	void cv_dnn_DetectionModel_delete(cv::dnn::DetectionModel* instance) {
			delete instance;
	}
	
	void cv_dnn_DetectionOutputLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::DetectionOutputLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::DetectionOutputLayer> ret = cv::dnn::DetectionOutputLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::DetectionOutputLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::Algorithm* cv_dnn_DetectionOutputLayer_to_Algorithm(cv::dnn::DetectionOutputLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_DetectionOutputLayer_to_Layer(cv::dnn::DetectionOutputLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_DetectionOutputLayer_delete(cv::dnn::DetectionOutputLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_Dict_has_const_const_StringR(const cv::dnn::Dict* instance, const char* key, Result<bool>* ocvrs_return) {
		try {
			bool ret = instance->has(std::string(key));
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Dict_ptr_const_StringR(cv::dnn::Dict* instance, const char* key, Result<cv::dnn::DictValue**>* ocvrs_return) {
		try {
			cv::dnn::DictValue* ret = instance->ptr(std::string(key));
			Ok(new cv::dnn::DictValue*(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Dict_ptr_const_const_StringR(const cv::dnn::Dict* instance, const char* key, Result<const cv::dnn::DictValue**>* ocvrs_return) {
		try {
			const cv::dnn::DictValue* ret = instance->ptr(std::string(key));
			Ok(new const cv::dnn::DictValue*(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Dict_get_const_const_StringR(const cv::dnn::Dict* instance, const char* key, Result<cv::dnn::DictValue*>* ocvrs_return) {
		try {
			const cv::dnn::DictValue ret = instance->get(std::string(key));
			Ok(new const cv::dnn::DictValue(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Dict_set_const_cv_String_const_StringR_const_StringR(cv::dnn::Dict* instance, const char* key, const char* value, Result<void*>* ocvrs_return) {
		try {
			const cv::String ret = instance->set<cv::String>(std::string(key), cv::String(value));
			Ok(ocvrs_create_string(ret.c_str()), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Dict_set_const_cv_dnn_DictValue_const_StringR_const_DictValueR(cv::dnn::Dict* instance, const char* key, const cv::dnn::DictValue* value, Result<cv::dnn::DictValue*>* ocvrs_return) {
		try {
			const cv::dnn::DictValue ret = instance->set<cv::dnn::DictValue>(std::string(key), *value);
			Ok(new const cv::dnn::DictValue(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Dict_set_const_double_const_StringR_const_doubleR(cv::dnn::Dict* instance, const char* key, const double* value, Result<double>* ocvrs_return) {
		try {
			const double ret = instance->set<double>(std::string(key), *value);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Dict_set_const_int64_t_const_StringR_const_int64_tR(cv::dnn::Dict* instance, const char* key, const int64_t* value, Result<int64_t>* ocvrs_return) {
		try {
			const int64_t ret = instance->set<int64_t>(std::string(key), *value);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Dict_erase_const_StringR(cv::dnn::Dict* instance, const char* key, ResultVoid* ocvrs_return) {
		try {
			instance->erase(std::string(key));
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Dict_delete(cv::dnn::Dict* instance) {
			delete instance;
	}
	
	void cv_dnn_DictValue_DictValue_const_DictValueR(const cv::dnn::DictValue* r, Result<cv::dnn::DictValue*>* ocvrs_return) {
		try {
			cv::dnn::DictValue* ret = new cv::dnn::DictValue(*r);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DictValue_DictValue_bool(bool i, Result<cv::dnn::DictValue*>* ocvrs_return) {
		try {
			cv::dnn::DictValue* ret = new cv::dnn::DictValue(i);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DictValue_DictValue_int64_t(int64_t i, Result<cv::dnn::DictValue*>* ocvrs_return) {
		try {
			cv::dnn::DictValue* ret = new cv::dnn::DictValue(i);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DictValue_DictValue(Result<cv::dnn::DictValue*>* ocvrs_return) {
		try {
			cv::dnn::DictValue* ret = new cv::dnn::DictValue();
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DictValue_DictValue_int(int i, Result<cv::dnn::DictValue*>* ocvrs_return) {
		try {
			cv::dnn::DictValue* ret = new cv::dnn::DictValue(i);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DictValue_DictValue_unsigned_int(unsigned int p, Result<cv::dnn::DictValue*>* ocvrs_return) {
		try {
			cv::dnn::DictValue* ret = new cv::dnn::DictValue(p);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DictValue_DictValue_double(double p, Result<cv::dnn::DictValue*>* ocvrs_return) {
		try {
			cv::dnn::DictValue* ret = new cv::dnn::DictValue(p);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DictValue_DictValue_const_charX(const char* s, Result<cv::dnn::DictValue*>* ocvrs_return) {
		try {
			cv::dnn::DictValue* ret = new cv::dnn::DictValue(s);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DictValue_get_cv_String_const_int(const cv::dnn::DictValue* instance, int idx, Result<void*>* ocvrs_return) {
		try {
			cv::String ret = instance->get<cv::String>(idx);
			Ok(ocvrs_create_string(ret.c_str()), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DictValue_get_cv_String_const(const cv::dnn::DictValue* instance, Result<void*>* ocvrs_return) {
		try {
			cv::String ret = instance->get<cv::String>();
			Ok(ocvrs_create_string(ret.c_str()), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DictValue_get_double_const_int(const cv::dnn::DictValue* instance, int idx, Result<double>* ocvrs_return) {
		try {
			double ret = instance->get<double>(idx);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DictValue_get_double_const(const cv::dnn::DictValue* instance, Result<double>* ocvrs_return) {
		try {
			double ret = instance->get<double>();
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DictValue_get_int_const_int(const cv::dnn::DictValue* instance, int idx, Result<int>* ocvrs_return) {
		try {
			int ret = instance->get<int>(idx);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DictValue_get_int_const(const cv::dnn::DictValue* instance, Result<int>* ocvrs_return) {
		try {
			int ret = instance->get<int>();
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DictValue_get_int64_t_const_int(const cv::dnn::DictValue* instance, int idx, Result<int64_t>* ocvrs_return) {
		try {
			int64_t ret = instance->get<int64_t>(idx);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DictValue_get_int64_t_const(const cv::dnn::DictValue* instance, Result<int64_t>* ocvrs_return) {
		try {
			int64_t ret = instance->get<int64_t>();
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DictValue_size_const(const cv::dnn::DictValue* instance, Result<int>* ocvrs_return) {
		try {
			int ret = instance->size();
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DictValue_isInt_const(const cv::dnn::DictValue* instance, Result<bool>* ocvrs_return) {
		try {
			bool ret = instance->isInt();
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DictValue_isString_const(const cv::dnn::DictValue* instance, Result<bool>* ocvrs_return) {
		try {
			bool ret = instance->isString();
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DictValue_isReal_const(const cv::dnn::DictValue* instance, Result<bool>* ocvrs_return) {
		try {
			bool ret = instance->isReal();
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DictValue_getIntValue_const_int(const cv::dnn::DictValue* instance, int idx, Result<int>* ocvrs_return) {
		try {
			int ret = instance->getIntValue(idx);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DictValue_getIntValue_const(const cv::dnn::DictValue* instance, Result<int>* ocvrs_return) {
		try {
			int ret = instance->getIntValue();
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DictValue_getRealValue_const_int(const cv::dnn::DictValue* instance, int idx, Result<double>* ocvrs_return) {
		try {
			double ret = instance->getRealValue(idx);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DictValue_getRealValue_const(const cv::dnn::DictValue* instance, Result<double>* ocvrs_return) {
		try {
			double ret = instance->getRealValue();
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DictValue_getStringValue_const_int(const cv::dnn::DictValue* instance, int idx, Result<void*>* ocvrs_return) {
		try {
			cv::String ret = instance->getStringValue(idx);
			Ok(ocvrs_create_string(ret.c_str()), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DictValue_getStringValue_const(const cv::dnn::DictValue* instance, Result<void*>* ocvrs_return) {
		try {
			cv::String ret = instance->getStringValue();
			Ok(ocvrs_create_string(ret.c_str()), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DictValue_operatorST_const_DictValueR(cv::dnn::DictValue* instance, const cv::dnn::DictValue* r, ResultVoid* ocvrs_return) {
		try {
			instance->operator=(*r);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_DictValue_delete(cv::dnn::DictValue* instance) {
			delete instance;
	}
	
	void cv_dnn_ELULayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::ELULayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::ELULayer> ret = cv::dnn::ELULayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::ELULayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::dnn::ActivationLayer* cv_dnn_ELULayer_to_ActivationLayer(cv::dnn::ELULayer* instance) {
			return dynamic_cast<cv::dnn::ActivationLayer*>(instance);
	}
	
	cv::Algorithm* cv_dnn_ELULayer_to_Algorithm(cv::dnn::ELULayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_ELULayer_to_Layer(cv::dnn::ELULayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_ELULayer_delete(cv::dnn::ELULayer* instance) {
			delete instance;
	}
	
	void cv_dnn_EltwiseLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::EltwiseLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::EltwiseLayer> ret = cv::dnn::EltwiseLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::EltwiseLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::Algorithm* cv_dnn_EltwiseLayer_to_Algorithm(cv::dnn::EltwiseLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_EltwiseLayer_to_Layer(cv::dnn::EltwiseLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_EltwiseLayer_delete(cv::dnn::EltwiseLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_EltwiseLayerInt8_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::EltwiseLayerInt8>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::EltwiseLayerInt8> ret = cv::dnn::EltwiseLayerInt8::create(*params);
			Ok(new cv::Ptr<cv::dnn::EltwiseLayerInt8>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::Algorithm* cv_dnn_EltwiseLayerInt8_to_Algorithm(cv::dnn::EltwiseLayerInt8* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_EltwiseLayerInt8_to_Layer(cv::dnn::EltwiseLayerInt8* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_EltwiseLayerInt8_delete(cv::dnn::EltwiseLayerInt8* instance) {
			delete instance;
	}
	
	void cv_dnn_ExpLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::ExpLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::ExpLayer> ret = cv::dnn::ExpLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::ExpLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	float cv_dnn_ExpLayer_propBase_const(const cv::dnn::ExpLayer* instance) {
			float ret = instance->base;
			return ret;
	}
	
	void cv_dnn_ExpLayer_propBase_const_float(cv::dnn::ExpLayer* instance, const float val) {
			instance->base = val;
	}
	
	float cv_dnn_ExpLayer_propScale_const(const cv::dnn::ExpLayer* instance) {
			float ret = instance->scale;
			return ret;
	}
	
	void cv_dnn_ExpLayer_propScale_const_float(cv::dnn::ExpLayer* instance, const float val) {
			instance->scale = val;
	}
	
	float cv_dnn_ExpLayer_propShift_const(const cv::dnn::ExpLayer* instance) {
			float ret = instance->shift;
			return ret;
	}
	
	void cv_dnn_ExpLayer_propShift_const_float(cv::dnn::ExpLayer* instance, const float val) {
			instance->shift = val;
	}
	
	cv::dnn::ActivationLayer* cv_dnn_ExpLayer_to_ActivationLayer(cv::dnn::ExpLayer* instance) {
			return dynamic_cast<cv::dnn::ActivationLayer*>(instance);
	}
	
	cv::Algorithm* cv_dnn_ExpLayer_to_Algorithm(cv::dnn::ExpLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_ExpLayer_to_Layer(cv::dnn::ExpLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_ExpLayer_delete(cv::dnn::ExpLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_FlattenLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::FlattenLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::FlattenLayer> ret = cv::dnn::FlattenLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::FlattenLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::Algorithm* cv_dnn_FlattenLayer_to_Algorithm(cv::dnn::FlattenLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_FlattenLayer_to_Layer(cv::dnn::FlattenLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_FlattenLayer_delete(cv::dnn::FlattenLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_FlowWarpLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::FlowWarpLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::FlowWarpLayer> ret = cv::dnn::FlowWarpLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::FlowWarpLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::Algorithm* cv_dnn_FlowWarpLayer_to_Algorithm(cv::dnn::FlowWarpLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_FlowWarpLayer_to_Layer(cv::dnn::FlowWarpLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_FlowWarpLayer_delete(cv::dnn::FlowWarpLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_GRULayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::GRULayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::GRULayer> ret = cv::dnn::GRULayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::GRULayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::Algorithm* cv_dnn_GRULayer_to_Algorithm(cv::dnn::GRULayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_GRULayer_to_Layer(cv::dnn::GRULayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_GRULayer_delete(cv::dnn::GRULayer* instance) {
			delete instance;
	}
	
	void cv_dnn_InnerProductLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::InnerProductLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::InnerProductLayer> ret = cv::dnn::InnerProductLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::InnerProductLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	int cv_dnn_InnerProductLayer_propAxis_const(const cv::dnn::InnerProductLayer* instance) {
			int ret = instance->axis;
			return ret;
	}
	
	void cv_dnn_InnerProductLayer_propAxis_const_int(cv::dnn::InnerProductLayer* instance, const int val) {
			instance->axis = val;
	}
	
	cv::Algorithm* cv_dnn_InnerProductLayer_to_Algorithm(cv::dnn::InnerProductLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_InnerProductLayer_to_Layer(cv::dnn::InnerProductLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_InnerProductLayer_delete(cv::dnn::InnerProductLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_InnerProductLayerInt8_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::InnerProductLayerInt8>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::InnerProductLayerInt8> ret = cv::dnn::InnerProductLayerInt8::create(*params);
			Ok(new cv::Ptr<cv::dnn::InnerProductLayerInt8>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	int cv_dnn_InnerProductLayerInt8_propOutput_zp_const(const cv::dnn::InnerProductLayerInt8* instance) {
			int ret = instance->output_zp;
			return ret;
	}
	
	void cv_dnn_InnerProductLayerInt8_propOutput_zp_const_int(cv::dnn::InnerProductLayerInt8* instance, const int val) {
			instance->output_zp = val;
	}
	
	cv::Algorithm* cv_dnn_InnerProductLayerInt8_to_Algorithm(cv::dnn::InnerProductLayerInt8* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::InnerProductLayer* cv_dnn_InnerProductLayerInt8_to_InnerProductLayer(cv::dnn::InnerProductLayerInt8* instance) {
			return dynamic_cast<cv::dnn::InnerProductLayer*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_InnerProductLayerInt8_to_Layer(cv::dnn::InnerProductLayerInt8* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_InnerProductLayerInt8_delete(cv::dnn::InnerProductLayerInt8* instance) {
			delete instance;
	}
	
	void cv_dnn_InterpLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::Layer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::Layer> ret = cv::dnn::InterpLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::Layer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::Algorithm* cv_dnn_InterpLayer_to_Algorithm(cv::dnn::InterpLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_InterpLayer_to_Layer(cv::dnn::InterpLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_InterpLayer_delete(cv::dnn::InterpLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_KeypointsModel_KeypointsModel_const_StringR_const_StringR(const char* model, const char* config, Result<cv::dnn::KeypointsModel*>* ocvrs_return) {
		try {
			cv::dnn::KeypointsModel* ret = new cv::dnn::KeypointsModel(std::string(model), std::string(config));
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_KeypointsModel_KeypointsModel_const_StringR(const char* model, Result<cv::dnn::KeypointsModel*>* ocvrs_return) {
		try {
			cv::dnn::KeypointsModel* ret = new cv::dnn::KeypointsModel(std::string(model));
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_KeypointsModel_KeypointsModel_const_NetR(const cv::dnn::Net* network, Result<cv::dnn::KeypointsModel*>* ocvrs_return) {
		try {
			cv::dnn::KeypointsModel* ret = new cv::dnn::KeypointsModel(*network);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_KeypointsModel_estimate_const__InputArrayR_float(cv::dnn::KeypointsModel* instance, const cv::_InputArray* frame, float thresh, Result<std::vector<cv::Point2f>*>* ocvrs_return) {
		try {
			std::vector<cv::Point2f> ret = instance->estimate(*frame, thresh);
			Ok(new std::vector<cv::Point2f>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_KeypointsModel_estimate_const__InputArrayR(cv::dnn::KeypointsModel* instance, const cv::_InputArray* frame, Result<std::vector<cv::Point2f>*>* ocvrs_return) {
		try {
			std::vector<cv::Point2f> ret = instance->estimate(*frame);
			Ok(new std::vector<cv::Point2f>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::dnn::KeypointsModel* cv_dnn_KeypointsModel_implicitClone_const(const cv::dnn::KeypointsModel* instance) {
			return new cv::dnn::KeypointsModel(*instance);
	}
	
	cv::dnn::Model* cv_dnn_KeypointsModel_to_Model(cv::dnn::KeypointsModel* instance) {
			return dynamic_cast<cv::dnn::Model*>(instance);
	}
	
	void cv_dnn_KeypointsModel_delete(cv::dnn::KeypointsModel* instance) {
			delete instance;
	}
	
	void cv_dnn_LRNLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::LRNLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::LRNLayer> ret = cv::dnn::LRNLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::LRNLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	int cv_dnn_LRNLayer_propType_const(const cv::dnn::LRNLayer* instance) {
			int ret = instance->type;
			return ret;
	}
	
	void cv_dnn_LRNLayer_propType_const_int(cv::dnn::LRNLayer* instance, const int val) {
			instance->type = val;
	}
	
	int cv_dnn_LRNLayer_propSize_const(const cv::dnn::LRNLayer* instance) {
			int ret = instance->size;
			return ret;
	}
	
	void cv_dnn_LRNLayer_propSize_const_int(cv::dnn::LRNLayer* instance, const int val) {
			instance->size = val;
	}
	
	float cv_dnn_LRNLayer_propAlpha_const(const cv::dnn::LRNLayer* instance) {
			float ret = instance->alpha;
			return ret;
	}
	
	void cv_dnn_LRNLayer_propAlpha_const_float(cv::dnn::LRNLayer* instance, const float val) {
			instance->alpha = val;
	}
	
	float cv_dnn_LRNLayer_propBeta_const(const cv::dnn::LRNLayer* instance) {
			float ret = instance->beta;
			return ret;
	}
	
	void cv_dnn_LRNLayer_propBeta_const_float(cv::dnn::LRNLayer* instance, const float val) {
			instance->beta = val;
	}
	
	float cv_dnn_LRNLayer_propBias_const(const cv::dnn::LRNLayer* instance) {
			float ret = instance->bias;
			return ret;
	}
	
	void cv_dnn_LRNLayer_propBias_const_float(cv::dnn::LRNLayer* instance, const float val) {
			instance->bias = val;
	}
	
	bool cv_dnn_LRNLayer_propNormBySize_const(const cv::dnn::LRNLayer* instance) {
			bool ret = instance->normBySize;
			return ret;
	}
	
	void cv_dnn_LRNLayer_propNormBySize_const_bool(cv::dnn::LRNLayer* instance, const bool val) {
			instance->normBySize = val;
	}
	
	cv::Algorithm* cv_dnn_LRNLayer_to_Algorithm(cv::dnn::LRNLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_LRNLayer_to_Layer(cv::dnn::LRNLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_LRNLayer_delete(cv::dnn::LRNLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_LSTMLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::LSTMLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::LSTMLayer> ret = cv::dnn::LSTMLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::LSTMLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_LSTMLayer_setWeights_const_MatR_const_MatR_const_MatR(cv::dnn::LSTMLayer* instance, const cv::Mat* Wh, const cv::Mat* Wx, const cv::Mat* b, ResultVoid* ocvrs_return) {
		try {
			instance->setWeights(*Wh, *Wx, *b);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_LSTMLayer_setOutShape_const_MatShapeR(cv::dnn::LSTMLayer* instance, const cv::dnn::MatShape* outTailShape, ResultVoid* ocvrs_return) {
		try {
			instance->setOutShape(*outTailShape);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_LSTMLayer_setOutShape(cv::dnn::LSTMLayer* instance, ResultVoid* ocvrs_return) {
		try {
			instance->setOutShape();
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_LSTMLayer_setUseTimstampsDim_bool(cv::dnn::LSTMLayer* instance, bool use, ResultVoid* ocvrs_return) {
		try {
			instance->setUseTimstampsDim(use);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_LSTMLayer_setUseTimstampsDim(cv::dnn::LSTMLayer* instance, ResultVoid* ocvrs_return) {
		try {
			instance->setUseTimstampsDim();
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_LSTMLayer_setProduceCellOutput_bool(cv::dnn::LSTMLayer* instance, bool produce, ResultVoid* ocvrs_return) {
		try {
			instance->setProduceCellOutput(produce);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_LSTMLayer_setProduceCellOutput(cv::dnn::LSTMLayer* instance, ResultVoid* ocvrs_return) {
		try {
			instance->setProduceCellOutput();
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_LSTMLayer_inputNameToIndex_String(cv::dnn::LSTMLayer* instance, const char* inputName, Result<int>* ocvrs_return) {
		try {
			int ret = instance->inputNameToIndex(std::string(inputName));
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_LSTMLayer_outputNameToIndex_const_StringR(cv::dnn::LSTMLayer* instance, const char* outputName, Result<int>* ocvrs_return) {
		try {
			int ret = instance->outputNameToIndex(std::string(outputName));
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::Algorithm* cv_dnn_LSTMLayer_to_Algorithm(cv::dnn::LSTMLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_LSTMLayer_to_Layer(cv::dnn::LSTMLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_LSTMLayer_delete(cv::dnn::LSTMLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_Layer_finalize_const__InputArrayR_const__OutputArrayR(cv::dnn::Layer* instance, const cv::_InputArray* inputs, const cv::_OutputArray* outputs, ResultVoid* ocvrs_return) {
		try {
			instance->finalize(*inputs, *outputs);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Layer_forward_vectorLMatXGR_vectorLMatGR_vectorLMatGR(cv::dnn::Layer* instance, std::vector<cv::Mat*>* input, std::vector<cv::Mat>* output, std::vector<cv::Mat>* internals, ResultVoid* ocvrs_return) {
		try {
			instance->forward(*input, *output, *internals);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Layer_forward_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(cv::dnn::Layer* instance, const cv::_InputArray* inputs, const cv::_OutputArray* outputs, const cv::_OutputArray* internals, ResultVoid* ocvrs_return) {
		try {
			instance->forward(*inputs, *outputs, *internals);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Layer_tryQuantize_const_vectorLvectorLfloatGGR_const_vectorLvectorLintGGR_LayerParamsR(cv::dnn::Layer* instance, const std::vector<std::vector<float>>* scales, const std::vector<std::vector<int>>* zeropoints, cv::dnn::LayerParams* params, Result<bool>* ocvrs_return) {
		try {
			bool ret = instance->tryQuantize(*scales, *zeropoints, *params);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Layer_forward_fallback_const__InputArrayR_const__OutputArrayR_const__OutputArrayR(cv::dnn::Layer* instance, const cv::_InputArray* inputs, const cv::_OutputArray* outputs, const cv::_OutputArray* internals, ResultVoid* ocvrs_return) {
		try {
			instance->forward_fallback(*inputs, *outputs, *internals);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Layer_finalize_const_vectorLMatGR_vectorLMatGR(cv::dnn::Layer* instance, const std::vector<cv::Mat>* inputs, std::vector<cv::Mat>* outputs, ResultVoid* ocvrs_return) {
		try {
			instance->finalize(*inputs, *outputs);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Layer_finalize_const_vectorLMatGR(cv::dnn::Layer* instance, const std::vector<cv::Mat>* inputs, Result<std::vector<cv::Mat>*>* ocvrs_return) {
		try {
			std::vector<cv::Mat> ret = instance->finalize(*inputs);
			Ok(new std::vector<cv::Mat>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Layer_run_const_vectorLMatGR_vectorLMatGR_vectorLMatGR(cv::dnn::Layer* instance, const std::vector<cv::Mat>* inputs, std::vector<cv::Mat>* outputs, std::vector<cv::Mat>* internals, ResultVoid* ocvrs_return) {
		try {
			instance->run(*inputs, *outputs, *internals);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Layer_inputNameToIndex_String(cv::dnn::Layer* instance, const char* inputName, Result<int>* ocvrs_return) {
		try {
			int ret = instance->inputNameToIndex(std::string(inputName));
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Layer_outputNameToIndex_const_StringR(cv::dnn::Layer* instance, const char* outputName, Result<int>* ocvrs_return) {
		try {
			int ret = instance->outputNameToIndex(std::string(outputName));
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Layer_supportBackend_int(cv::dnn::Layer* instance, int backendId, Result<bool>* ocvrs_return) {
		try {
			bool ret = instance->supportBackend(backendId);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Layer_initHalide_const_vectorLPtrLBackendWrapperGGR(cv::dnn::Layer* instance, const std::vector<cv::Ptr<cv::dnn::BackendWrapper>>* inputs, Result<cv::Ptr<cv::dnn::BackendNode>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::BackendNode> ret = instance->initHalide(*inputs);
			Ok(new cv::Ptr<cv::dnn::BackendNode>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Layer_initInfEngine_const_vectorLPtrLBackendWrapperGGR(cv::dnn::Layer* instance, const std::vector<cv::Ptr<cv::dnn::BackendWrapper>>* inputs, Result<cv::Ptr<cv::dnn::BackendNode>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::BackendNode> ret = instance->initInfEngine(*inputs);
			Ok(new cv::Ptr<cv::dnn::BackendNode>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Layer_initNgraph_const_vectorLPtrLBackendWrapperGGR_const_vectorLPtrLBackendNodeGGR(cv::dnn::Layer* instance, const std::vector<cv::Ptr<cv::dnn::BackendWrapper>>* inputs, const std::vector<cv::Ptr<cv::dnn::BackendNode>>* nodes, Result<cv::Ptr<cv::dnn::BackendNode>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::BackendNode> ret = instance->initNgraph(*inputs, *nodes);
			Ok(new cv::Ptr<cv::dnn::BackendNode>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Layer_initVkCom_const_vectorLPtrLBackendWrapperGGR(cv::dnn::Layer* instance, const std::vector<cv::Ptr<cv::dnn::BackendWrapper>>* inputs, Result<cv::Ptr<cv::dnn::BackendNode>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::BackendNode> ret = instance->initVkCom(*inputs);
			Ok(new cv::Ptr<cv::dnn::BackendNode>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Layer_initCUDA_voidX_const_vectorLPtrLBackendWrapperGGR_const_vectorLPtrLBackendWrapperGGR(cv::dnn::Layer* instance, void* context, const std::vector<cv::Ptr<cv::dnn::BackendWrapper>>* inputs, const std::vector<cv::Ptr<cv::dnn::BackendWrapper>>* outputs, Result<cv::Ptr<cv::dnn::BackendNode>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::BackendNode> ret = instance->initCUDA(context, *inputs, *outputs);
			Ok(new cv::Ptr<cv::dnn::BackendNode>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Layer_applyHalideScheduler_const_PtrLBackendNodeGR_const_vectorLMatXGR_const_vectorLMatGR_int(const cv::dnn::Layer* instance, cv::Ptr<cv::dnn::BackendNode>* node, const std::vector<cv::Mat*>* inputs, const std::vector<cv::Mat>* outputs, int targetId, ResultVoid* ocvrs_return) {
		try {
			instance->applyHalideScheduler(*node, *inputs, *outputs, targetId);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Layer_tryAttach_const_PtrLBackendNodeGR(cv::dnn::Layer* instance, const cv::Ptr<cv::dnn::BackendNode>* node, Result<cv::Ptr<cv::dnn::BackendNode>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::BackendNode> ret = instance->tryAttach(*node);
			Ok(new cv::Ptr<cv::dnn::BackendNode>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Layer_setActivation_const_PtrLActivationLayerGR(cv::dnn::Layer* instance, const cv::Ptr<cv::dnn::ActivationLayer>* layer, Result<bool>* ocvrs_return) {
		try {
			bool ret = instance->setActivation(*layer);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Layer_tryFuse_PtrLLayerGR(cv::dnn::Layer* instance, cv::Ptr<cv::dnn::Layer>* top, Result<bool>* ocvrs_return) {
		try {
			bool ret = instance->tryFuse(*top);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Layer_getScaleShift_const_MatR_MatR(const cv::dnn::Layer* instance, cv::Mat* scale, cv::Mat* shift, ResultVoid* ocvrs_return) {
		try {
			instance->getScaleShift(*scale, *shift);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Layer_getScaleZeropoint_const_floatR_intR(const cv::dnn::Layer* instance, float* scale, int* zeropoint, ResultVoid* ocvrs_return) {
		try {
			instance->getScaleZeropoint(*scale, *zeropoint);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Layer_unsetAttached(cv::dnn::Layer* instance, ResultVoid* ocvrs_return) {
		try {
			instance->unsetAttached();
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Layer_getMemoryShapes_const_const_vectorLMatShapeGR_const_int_vectorLMatShapeGR_vectorLMatShapeGR(const cv::dnn::Layer* instance, const std::vector<cv::dnn::MatShape>* inputs, const int requiredOutputs, std::vector<cv::dnn::MatShape>* outputs, std::vector<cv::dnn::MatShape>* internals, Result<bool>* ocvrs_return) {
		try {
			bool ret = instance->getMemoryShapes(*inputs, requiredOutputs, *outputs, *internals);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Layer_getFLOPS_const_const_vectorLMatShapeGR_const_vectorLMatShapeGR(const cv::dnn::Layer* instance, const std::vector<cv::dnn::MatShape>* inputs, const std::vector<cv::dnn::MatShape>* outputs, Result<int64_t>* ocvrs_return) {
		try {
			int64_t ret = instance->getFLOPS(*inputs, *outputs);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Layer_updateMemoryShapes_const_vectorLMatShapeGR(cv::dnn::Layer* instance, const std::vector<cv::dnn::MatShape>* inputs, Result<bool>* ocvrs_return) {
		try {
			bool ret = instance->updateMemoryShapes(*inputs);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Layer_Layer(Result<cv::dnn::Layer*>* ocvrs_return) {
		try {
			cv::dnn::Layer* ret = new cv::dnn::Layer();
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Layer_Layer_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::dnn::Layer*>* ocvrs_return) {
		try {
			cv::dnn::Layer* ret = new cv::dnn::Layer(*params);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Layer_setParamsFrom_const_LayerParamsR(cv::dnn::Layer* instance, const cv::dnn::LayerParams* params, ResultVoid* ocvrs_return) {
		try {
			instance->setParamsFrom(*params);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	std::vector<cv::Mat>* cv_dnn_Layer_propBlobs_const(const cv::dnn::Layer* instance) {
			std::vector<cv::Mat> ret = instance->blobs;
			return new std::vector<cv::Mat>(ret);
	}
	
	void cv_dnn_Layer_propBlobs_const_vectorLMatG(cv::dnn::Layer* instance, const std::vector<cv::Mat>* val) {
			instance->blobs = *val;
	}
	
	void* cv_dnn_Layer_propName_const(const cv::dnn::Layer* instance) {
			cv::String ret = instance->name;
			return ocvrs_create_string(ret.c_str());
	}
	
	void cv_dnn_Layer_propName_const_String(cv::dnn::Layer* instance, const char* val) {
			instance->name = std::string(val);
	}
	
	void* cv_dnn_Layer_propType_const(const cv::dnn::Layer* instance) {
			cv::String ret = instance->type;
			return ocvrs_create_string(ret.c_str());
	}
	
	void cv_dnn_Layer_propType_const_String(cv::dnn::Layer* instance, const char* val) {
			instance->type = std::string(val);
	}
	
	int cv_dnn_Layer_propPreferableTarget_const(const cv::dnn::Layer* instance) {
			int ret = instance->preferableTarget;
			return ret;
	}
	
	void cv_dnn_Layer_propPreferableTarget_const_int(cv::dnn::Layer* instance, const int val) {
			instance->preferableTarget = val;
	}
	
	cv::dnn::AbsLayer* cv_dnn_Layer_to_AbsLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::AbsLayer*>(instance);
	}
	
	cv::dnn::AccumLayer* cv_dnn_Layer_to_AccumLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::AccumLayer*>(instance);
	}
	
	cv::dnn::ActivationLayer* cv_dnn_Layer_to_ActivationLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::ActivationLayer*>(instance);
	}
	
	cv::dnn::ActivationLayerInt8* cv_dnn_Layer_to_ActivationLayerInt8(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::ActivationLayerInt8*>(instance);
	}
	
	cv::dnn::BNLLLayer* cv_dnn_Layer_to_BNLLLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::BNLLLayer*>(instance);
	}
	
	cv::dnn::BaseConvolutionLayer* cv_dnn_Layer_to_BaseConvolutionLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::BaseConvolutionLayer*>(instance);
	}
	
	cv::dnn::BatchNormLayer* cv_dnn_Layer_to_BatchNormLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::BatchNormLayer*>(instance);
	}
	
	cv::dnn::BatchNormLayerInt8* cv_dnn_Layer_to_BatchNormLayerInt8(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::BatchNormLayerInt8*>(instance);
	}
	
	cv::dnn::BlankLayer* cv_dnn_Layer_to_BlankLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::BlankLayer*>(instance);
	}
	
	cv::dnn::ChannelsPReLULayer* cv_dnn_Layer_to_ChannelsPReLULayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::ChannelsPReLULayer*>(instance);
	}
	
	cv::dnn::ConcatLayer* cv_dnn_Layer_to_ConcatLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::ConcatLayer*>(instance);
	}
	
	cv::dnn::ConstLayer* cv_dnn_Layer_to_ConstLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::ConstLayer*>(instance);
	}
	
	cv::dnn::ConvolutionLayer* cv_dnn_Layer_to_ConvolutionLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::ConvolutionLayer*>(instance);
	}
	
	cv::dnn::ConvolutionLayerInt8* cv_dnn_Layer_to_ConvolutionLayerInt8(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::ConvolutionLayerInt8*>(instance);
	}
	
	cv::dnn::CorrelationLayer* cv_dnn_Layer_to_CorrelationLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::CorrelationLayer*>(instance);
	}
	
	cv::dnn::CropAndResizeLayer* cv_dnn_Layer_to_CropAndResizeLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::CropAndResizeLayer*>(instance);
	}
	
	cv::dnn::CropLayer* cv_dnn_Layer_to_CropLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::CropLayer*>(instance);
	}
	
	cv::dnn::CumSumLayer* cv_dnn_Layer_to_CumSumLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::CumSumLayer*>(instance);
	}
	
	cv::dnn::DataAugmentationLayer* cv_dnn_Layer_to_DataAugmentationLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::DataAugmentationLayer*>(instance);
	}
	
	cv::dnn::DeconvolutionLayer* cv_dnn_Layer_to_DeconvolutionLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::DeconvolutionLayer*>(instance);
	}
	
	cv::dnn::DequantizeLayer* cv_dnn_Layer_to_DequantizeLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::DequantizeLayer*>(instance);
	}
	
	cv::dnn::DetectionOutputLayer* cv_dnn_Layer_to_DetectionOutputLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::DetectionOutputLayer*>(instance);
	}
	
	cv::dnn::ELULayer* cv_dnn_Layer_to_ELULayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::ELULayer*>(instance);
	}
	
	cv::dnn::EltwiseLayer* cv_dnn_Layer_to_EltwiseLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::EltwiseLayer*>(instance);
	}
	
	cv::dnn::EltwiseLayerInt8* cv_dnn_Layer_to_EltwiseLayerInt8(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::EltwiseLayerInt8*>(instance);
	}
	
	cv::dnn::ExpLayer* cv_dnn_Layer_to_ExpLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::ExpLayer*>(instance);
	}
	
	cv::dnn::FlattenLayer* cv_dnn_Layer_to_FlattenLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::FlattenLayer*>(instance);
	}
	
	cv::dnn::FlowWarpLayer* cv_dnn_Layer_to_FlowWarpLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::FlowWarpLayer*>(instance);
	}
	
	cv::dnn::GRULayer* cv_dnn_Layer_to_GRULayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::GRULayer*>(instance);
	}
	
	cv::dnn::InnerProductLayer* cv_dnn_Layer_to_InnerProductLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::InnerProductLayer*>(instance);
	}
	
	cv::dnn::InnerProductLayerInt8* cv_dnn_Layer_to_InnerProductLayerInt8(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::InnerProductLayerInt8*>(instance);
	}
	
	cv::dnn::InterpLayer* cv_dnn_Layer_to_InterpLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::InterpLayer*>(instance);
	}
	
	cv::dnn::LRNLayer* cv_dnn_Layer_to_LRNLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::LRNLayer*>(instance);
	}
	
	cv::dnn::LSTMLayer* cv_dnn_Layer_to_LSTMLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::LSTMLayer*>(instance);
	}
	
	cv::dnn::MVNLayer* cv_dnn_Layer_to_MVNLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::MVNLayer*>(instance);
	}
	
	cv::dnn::MaxUnpoolLayer* cv_dnn_Layer_to_MaxUnpoolLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::MaxUnpoolLayer*>(instance);
	}
	
	cv::dnn::MishLayer* cv_dnn_Layer_to_MishLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::MishLayer*>(instance);
	}
	
	cv::dnn::NormalizeBBoxLayer* cv_dnn_Layer_to_NormalizeBBoxLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::NormalizeBBoxLayer*>(instance);
	}
	
	cv::dnn::PaddingLayer* cv_dnn_Layer_to_PaddingLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::PaddingLayer*>(instance);
	}
	
	cv::dnn::PermuteLayer* cv_dnn_Layer_to_PermuteLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::PermuteLayer*>(instance);
	}
	
	cv::dnn::PoolingLayer* cv_dnn_Layer_to_PoolingLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::PoolingLayer*>(instance);
	}
	
	cv::dnn::PoolingLayerInt8* cv_dnn_Layer_to_PoolingLayerInt8(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::PoolingLayerInt8*>(instance);
	}
	
	cv::dnn::PowerLayer* cv_dnn_Layer_to_PowerLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::PowerLayer*>(instance);
	}
	
	cv::dnn::PriorBoxLayer* cv_dnn_Layer_to_PriorBoxLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::PriorBoxLayer*>(instance);
	}
	
	cv::dnn::ProposalLayer* cv_dnn_Layer_to_ProposalLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::ProposalLayer*>(instance);
	}
	
	cv::dnn::QuantizeLayer* cv_dnn_Layer_to_QuantizeLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::QuantizeLayer*>(instance);
	}
	
	cv::dnn::RNNLayer* cv_dnn_Layer_to_RNNLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::RNNLayer*>(instance);
	}
	
	cv::dnn::ReLU6Layer* cv_dnn_Layer_to_ReLU6Layer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::ReLU6Layer*>(instance);
	}
	
	cv::dnn::ReLULayer* cv_dnn_Layer_to_ReLULayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::ReLULayer*>(instance);
	}
	
	cv::dnn::RegionLayer* cv_dnn_Layer_to_RegionLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::RegionLayer*>(instance);
	}
	
	cv::dnn::ReorgLayer* cv_dnn_Layer_to_ReorgLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::ReorgLayer*>(instance);
	}
	
	cv::dnn::RequantizeLayer* cv_dnn_Layer_to_RequantizeLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::RequantizeLayer*>(instance);
	}
	
	cv::dnn::ReshapeLayer* cv_dnn_Layer_to_ReshapeLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::ReshapeLayer*>(instance);
	}
	
	cv::dnn::ResizeLayer* cv_dnn_Layer_to_ResizeLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::ResizeLayer*>(instance);
	}
	
	cv::dnn::ScaleLayer* cv_dnn_Layer_to_ScaleLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::ScaleLayer*>(instance);
	}
	
	cv::dnn::ScaleLayerInt8* cv_dnn_Layer_to_ScaleLayerInt8(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::ScaleLayerInt8*>(instance);
	}
	
	cv::dnn::ShiftLayer* cv_dnn_Layer_to_ShiftLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::ShiftLayer*>(instance);
	}
	
	cv::dnn::ShiftLayerInt8* cv_dnn_Layer_to_ShiftLayerInt8(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::ShiftLayerInt8*>(instance);
	}
	
	cv::dnn::ShuffleChannelLayer* cv_dnn_Layer_to_ShuffleChannelLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::ShuffleChannelLayer*>(instance);
	}
	
	cv::dnn::SigmoidLayer* cv_dnn_Layer_to_SigmoidLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::SigmoidLayer*>(instance);
	}
	
	cv::dnn::SliceLayer* cv_dnn_Layer_to_SliceLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::SliceLayer*>(instance);
	}
	
	cv::dnn::SoftmaxLayer* cv_dnn_Layer_to_SoftmaxLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::SoftmaxLayer*>(instance);
	}
	
	cv::dnn::SoftmaxLayerInt8* cv_dnn_Layer_to_SoftmaxLayerInt8(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::SoftmaxLayerInt8*>(instance);
	}
	
	cv::dnn::SplitLayer* cv_dnn_Layer_to_SplitLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::SplitLayer*>(instance);
	}
	
	cv::dnn::SwishLayer* cv_dnn_Layer_to_SwishLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::SwishLayer*>(instance);
	}
	
	cv::dnn::TanHLayer* cv_dnn_Layer_to_TanHLayer(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::dnn::TanHLayer*>(instance);
	}
	
	cv::Algorithm* cv_dnn_Layer_to_Algorithm(cv::dnn::Layer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	void cv_dnn_Layer_delete(cv::dnn::Layer* instance) {
			delete instance;
	}
	
	void cv_dnn_LayerFactory_registerLayer_const_StringR_Constructor(const char* type, cv::dnn::LayerFactory::Constructor constructor, ResultVoid* ocvrs_return) {
		try {
			cv::dnn::LayerFactory::registerLayer(std::string(type), constructor);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_LayerFactory_unregisterLayer_const_StringR(const char* type, ResultVoid* ocvrs_return) {
		try {
			cv::dnn::LayerFactory::unregisterLayer(std::string(type));
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_LayerFactory_createLayerInstance_const_StringR_LayerParamsR(const char* type, cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::Layer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::Layer> ret = cv::dnn::LayerFactory::createLayerInstance(std::string(type), *params);
			Ok(new cv::Ptr<cv::dnn::Layer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_LayerFactory_delete(cv::dnn::LayerFactory* instance) {
			delete instance;
	}
	
	void cv_dnn_LayerParams_new_const(Result<cv::dnn::LayerParams*>* ocvrs_return) {
		try {
			cv::dnn::LayerParams* ret = new cv::dnn::LayerParams();
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	std::vector<cv::Mat>* cv_dnn_LayerParams_propBlobs_const(const cv::dnn::LayerParams* instance) {
			std::vector<cv::Mat> ret = instance->blobs;
			return new std::vector<cv::Mat>(ret);
	}
	
	void cv_dnn_LayerParams_propBlobs_const_vectorLMatG(cv::dnn::LayerParams* instance, const std::vector<cv::Mat>* val) {
			instance->blobs = *val;
	}
	
	void* cv_dnn_LayerParams_propName_const(const cv::dnn::LayerParams* instance) {
			cv::String ret = instance->name;
			return ocvrs_create_string(ret.c_str());
	}
	
	void cv_dnn_LayerParams_propName_const_String(cv::dnn::LayerParams* instance, const char* val) {
			instance->name = std::string(val);
	}
	
	void* cv_dnn_LayerParams_propType_const(const cv::dnn::LayerParams* instance) {
			cv::String ret = instance->type;
			return ocvrs_create_string(ret.c_str());
	}
	
	void cv_dnn_LayerParams_propType_const_String(cv::dnn::LayerParams* instance, const char* val) {
			instance->type = std::string(val);
	}
	
	cv::dnn::Dict* cv_dnn_LayerParams_to_Dict(cv::dnn::LayerParams* instance) {
			return dynamic_cast<cv::dnn::Dict*>(instance);
	}
	
	void cv_dnn_LayerParams_delete(cv::dnn::LayerParams* instance) {
			delete instance;
	}
	
	void cv_dnn_MVNLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::MVNLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::MVNLayer> ret = cv::dnn::MVNLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::MVNLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	float cv_dnn_MVNLayer_propEps_const(const cv::dnn::MVNLayer* instance) {
			float ret = instance->eps;
			return ret;
	}
	
	void cv_dnn_MVNLayer_propEps_const_float(cv::dnn::MVNLayer* instance, const float val) {
			instance->eps = val;
	}
	
	bool cv_dnn_MVNLayer_propNormVariance_const(const cv::dnn::MVNLayer* instance) {
			bool ret = instance->normVariance;
			return ret;
	}
	
	void cv_dnn_MVNLayer_propNormVariance_const_bool(cv::dnn::MVNLayer* instance, const bool val) {
			instance->normVariance = val;
	}
	
	bool cv_dnn_MVNLayer_propAcrossChannels_const(const cv::dnn::MVNLayer* instance) {
			bool ret = instance->acrossChannels;
			return ret;
	}
	
	void cv_dnn_MVNLayer_propAcrossChannels_const_bool(cv::dnn::MVNLayer* instance, const bool val) {
			instance->acrossChannels = val;
	}
	
	cv::Algorithm* cv_dnn_MVNLayer_to_Algorithm(cv::dnn::MVNLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_MVNLayer_to_Layer(cv::dnn::MVNLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_MVNLayer_delete(cv::dnn::MVNLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_MaxUnpoolLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::MaxUnpoolLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::MaxUnpoolLayer> ret = cv::dnn::MaxUnpoolLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::MaxUnpoolLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_MaxUnpoolLayer_propPoolKernel_const(const cv::dnn::MaxUnpoolLayer* instance, cv::Size* ocvrs_return) {
			cv::Size ret = instance->poolKernel;
			*ocvrs_return = ret;
	}
	
	void cv_dnn_MaxUnpoolLayer_propPoolKernel_const_Size(cv::dnn::MaxUnpoolLayer* instance, const cv::Size* val) {
			instance->poolKernel = *val;
	}
	
	void cv_dnn_MaxUnpoolLayer_propPoolPad_const(const cv::dnn::MaxUnpoolLayer* instance, cv::Size* ocvrs_return) {
			cv::Size ret = instance->poolPad;
			*ocvrs_return = ret;
	}
	
	void cv_dnn_MaxUnpoolLayer_propPoolPad_const_Size(cv::dnn::MaxUnpoolLayer* instance, const cv::Size* val) {
			instance->poolPad = *val;
	}
	
	void cv_dnn_MaxUnpoolLayer_propPoolStride_const(const cv::dnn::MaxUnpoolLayer* instance, cv::Size* ocvrs_return) {
			cv::Size ret = instance->poolStride;
			*ocvrs_return = ret;
	}
	
	void cv_dnn_MaxUnpoolLayer_propPoolStride_const_Size(cv::dnn::MaxUnpoolLayer* instance, const cv::Size* val) {
			instance->poolStride = *val;
	}
	
	cv::Algorithm* cv_dnn_MaxUnpoolLayer_to_Algorithm(cv::dnn::MaxUnpoolLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_MaxUnpoolLayer_to_Layer(cv::dnn::MaxUnpoolLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_MaxUnpoolLayer_delete(cv::dnn::MaxUnpoolLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_MishLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::MishLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::MishLayer> ret = cv::dnn::MishLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::MishLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::dnn::ActivationLayer* cv_dnn_MishLayer_to_ActivationLayer(cv::dnn::MishLayer* instance) {
			return dynamic_cast<cv::dnn::ActivationLayer*>(instance);
	}
	
	cv::Algorithm* cv_dnn_MishLayer_to_Algorithm(cv::dnn::MishLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_MishLayer_to_Layer(cv::dnn::MishLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_MishLayer_delete(cv::dnn::MishLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_Model_Model(Result<cv::dnn::Model*>* ocvrs_return) {
		try {
			cv::dnn::Model* ret = new cv::dnn::Model();
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::dnn::Model* cv_dnn_Model_Model_const_ModelR(const cv::dnn::Model* unnamed) {
			cv::dnn::Model* ret = new cv::dnn::Model(*unnamed);
			return ret;
	}
	
	cv::dnn::Model* cv_dnn_Model_Model_ModelRR(cv::dnn::Model* unnamed) {
			cv::dnn::Model* ret = new cv::dnn::Model(std::move(*unnamed));
			return ret;
	}
	
	void cv_dnn_Model_operatorST_const_ModelR(cv::dnn::Model* instance, const cv::dnn::Model* unnamed) {
			instance->operator=(*unnamed);
	}
	
	void cv_dnn_Model_operatorST_ModelRR(cv::dnn::Model* instance, cv::dnn::Model* unnamed) {
			instance->operator=(std::move(*unnamed));
	}
	
	void cv_dnn_Model_Model_const_StringR_const_StringR(const char* model, const char* config, Result<cv::dnn::Model*>* ocvrs_return) {
		try {
			cv::dnn::Model* ret = new cv::dnn::Model(std::string(model), std::string(config));
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Model_Model_const_StringR(const char* model, Result<cv::dnn::Model*>* ocvrs_return) {
		try {
			cv::dnn::Model* ret = new cv::dnn::Model(std::string(model));
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Model_Model_const_NetR(const cv::dnn::Net* network, Result<cv::dnn::Model*>* ocvrs_return) {
		try {
			cv::dnn::Model* ret = new cv::dnn::Model(*network);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Model_setInputSize_const_SizeR(cv::dnn::Model* instance, const cv::Size* size, Result<cv::dnn::Model*>* ocvrs_return) {
		try {
			cv::dnn::Model ret = instance->setInputSize(*size);
			Ok(new cv::dnn::Model(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Model_setInputSize_int_int(cv::dnn::Model* instance, int width, int height, Result<cv::dnn::Model*>* ocvrs_return) {
		try {
			cv::dnn::Model ret = instance->setInputSize(width, height);
			Ok(new cv::dnn::Model(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Model_setInputMean_const_ScalarR(cv::dnn::Model* instance, const cv::Scalar* mean, Result<cv::dnn::Model*>* ocvrs_return) {
		try {
			cv::dnn::Model ret = instance->setInputMean(*mean);
			Ok(new cv::dnn::Model(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Model_setInputScale_double(cv::dnn::Model* instance, double scale, Result<cv::dnn::Model*>* ocvrs_return) {
		try {
			cv::dnn::Model ret = instance->setInputScale(scale);
			Ok(new cv::dnn::Model(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Model_setInputCrop_bool(cv::dnn::Model* instance, bool crop, Result<cv::dnn::Model*>* ocvrs_return) {
		try {
			cv::dnn::Model ret = instance->setInputCrop(crop);
			Ok(new cv::dnn::Model(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Model_setInputSwapRB_bool(cv::dnn::Model* instance, bool swapRB, Result<cv::dnn::Model*>* ocvrs_return) {
		try {
			cv::dnn::Model ret = instance->setInputSwapRB(swapRB);
			Ok(new cv::dnn::Model(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Model_setInputParams_double_const_SizeR_const_ScalarR_bool_bool(cv::dnn::Model* instance, double scale, const cv::Size* size, const cv::Scalar* mean, bool swapRB, bool crop, ResultVoid* ocvrs_return) {
		try {
			instance->setInputParams(scale, *size, *mean, swapRB, crop);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Model_setInputParams(cv::dnn::Model* instance, ResultVoid* ocvrs_return) {
		try {
			instance->setInputParams();
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Model_predict_const_const__InputArrayR_const__OutputArrayR(const cv::dnn::Model* instance, const cv::_InputArray* frame, const cv::_OutputArray* outs, ResultVoid* ocvrs_return) {
		try {
			instance->predict(*frame, *outs);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Model_setPreferableBackend_Backend(cv::dnn::Model* instance, cv::dnn::Backend backendId, Result<cv::dnn::Model*>* ocvrs_return) {
		try {
			cv::dnn::Model ret = instance->setPreferableBackend(backendId);
			Ok(new cv::dnn::Model(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Model_setPreferableTarget_Target(cv::dnn::Model* instance, cv::dnn::Target targetId, Result<cv::dnn::Model*>* ocvrs_return) {
		try {
			cv::dnn::Model ret = instance->setPreferableTarget(targetId);
			Ok(new cv::dnn::Model(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Model_getNetwork__const(const cv::dnn::Model* instance, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = instance->getNetwork_();
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Model_getNetwork_(cv::dnn::Model* instance, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = instance->getNetwork_();
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::dnn::Model* cv_dnn_Model_implicitClone_const(const cv::dnn::Model* instance) {
			return new cv::dnn::Model(*instance);
	}
	
	void cv_dnn_Model_delete(cv::dnn::Model* instance) {
			delete instance;
	}
	
	void cv_dnn_Net_Net(Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net* ret = new cv::dnn::Net();
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_readFromModelOptimizer_const_StringR_const_StringR(const char* xml, const char* bin, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::Net::readFromModelOptimizer(std::string(xml), std::string(bin));
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_readFromModelOptimizer_const_vectorLunsigned_charGR_const_vectorLunsigned_charGR(const std::vector<unsigned char>* bufferModelConfig, const std::vector<unsigned char>* bufferWeights, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::Net::readFromModelOptimizer(*bufferModelConfig, *bufferWeights);
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_readFromModelOptimizer_const_unsigned_charX_size_t_const_unsigned_charX_size_t(const unsigned char* bufferModelConfigPtr, size_t bufferModelConfigSize, const unsigned char* bufferWeightsPtr, size_t bufferWeightsSize, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = cv::dnn::Net::readFromModelOptimizer(bufferModelConfigPtr, bufferModelConfigSize, bufferWeightsPtr, bufferWeightsSize);
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_empty_const(const cv::dnn::Net* instance, Result<bool>* ocvrs_return) {
		try {
			bool ret = instance->empty();
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_dump(cv::dnn::Net* instance, Result<void*>* ocvrs_return) {
		try {
			cv::String ret = instance->dump();
			Ok(ocvrs_create_string(ret.c_str()), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_dumpToFile_const_StringR(cv::dnn::Net* instance, const char* path, ResultVoid* ocvrs_return) {
		try {
			instance->dumpToFile(std::string(path));
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_addLayer_const_StringR_const_StringR_const_intR_LayerParamsR(cv::dnn::Net* instance, const char* name, const char* type, const int* dtype, cv::dnn::LayerParams* params, Result<int>* ocvrs_return) {
		try {
			int ret = instance->addLayer(std::string(name), std::string(type), *dtype, *params);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_addLayer_const_StringR_const_StringR_LayerParamsR(cv::dnn::Net* instance, const char* name, const char* type, cv::dnn::LayerParams* params, Result<int>* ocvrs_return) {
		try {
			int ret = instance->addLayer(std::string(name), std::string(type), *params);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_addLayerToPrev_const_StringR_const_StringR_const_intR_LayerParamsR(cv::dnn::Net* instance, const char* name, const char* type, const int* dtype, cv::dnn::LayerParams* params, Result<int>* ocvrs_return) {
		try {
			int ret = instance->addLayerToPrev(std::string(name), std::string(type), *dtype, *params);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_addLayerToPrev_const_StringR_const_StringR_LayerParamsR(cv::dnn::Net* instance, const char* name, const char* type, cv::dnn::LayerParams* params, Result<int>* ocvrs_return) {
		try {
			int ret = instance->addLayerToPrev(std::string(name), std::string(type), *params);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_getLayerId_const_StringR(cv::dnn::Net* instance, const char* layer, Result<int>* ocvrs_return) {
		try {
			int ret = instance->getLayerId(std::string(layer));
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_getLayerNames_const(const cv::dnn::Net* instance, Result<std::vector<cv::String>*>* ocvrs_return) {
		try {
			std::vector<cv::String> ret = instance->getLayerNames();
			Ok(new std::vector<cv::String>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_getLayer_LayerId(cv::dnn::Net* instance, cv::dnn::Net::LayerId* layerId, Result<cv::Ptr<cv::dnn::Layer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::Layer> ret = instance->getLayer(*layerId);
			Ok(new cv::Ptr<cv::dnn::Layer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_getLayerInputs_LayerId(cv::dnn::Net* instance, cv::dnn::Net::LayerId* layerId, Result<std::vector<cv::Ptr<cv::dnn::Layer>>*>* ocvrs_return) {
		try {
			std::vector<cv::Ptr<cv::dnn::Layer>> ret = instance->getLayerInputs(*layerId);
			Ok(new std::vector<cv::Ptr<cv::dnn::Layer>>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_connect_String_String(cv::dnn::Net* instance, const char* outPin, const char* inpPin, ResultVoid* ocvrs_return) {
		try {
			instance->connect(std::string(outPin), std::string(inpPin));
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_connect_int_int_int_int(cv::dnn::Net* instance, int outLayerId, int outNum, int inpLayerId, int inpNum, ResultVoid* ocvrs_return) {
		try {
			instance->connect(outLayerId, outNum, inpLayerId, inpNum);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_setInputsNames_const_vectorLStringGR(cv::dnn::Net* instance, const std::vector<cv::String>* inputBlobNames, ResultVoid* ocvrs_return) {
		try {
			instance->setInputsNames(*inputBlobNames);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_setInputShape_const_StringR_const_MatShapeR(cv::dnn::Net* instance, const char* inputName, const cv::dnn::MatShape* shape, ResultVoid* ocvrs_return) {
		try {
			instance->setInputShape(std::string(inputName), *shape);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_forward_const_StringR(cv::dnn::Net* instance, const char* outputName, Result<cv::Mat*>* ocvrs_return) {
		try {
			cv::Mat ret = instance->forward(std::string(outputName));
			Ok(new cv::Mat(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_forward(cv::dnn::Net* instance, Result<cv::Mat*>* ocvrs_return) {
		try {
			cv::Mat ret = instance->forward();
			Ok(new cv::Mat(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_forwardAsync_const_StringR(cv::dnn::Net* instance, const char* outputName, Result<cv::AsyncArray*>* ocvrs_return) {
		try {
			cv::AsyncArray ret = instance->forwardAsync(std::string(outputName));
			Ok(new cv::AsyncArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_forwardAsync(cv::dnn::Net* instance, Result<cv::AsyncArray*>* ocvrs_return) {
		try {
			cv::AsyncArray ret = instance->forwardAsync();
			Ok(new cv::AsyncArray(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_forward_const__OutputArrayR_const_StringR(cv::dnn::Net* instance, const cv::_OutputArray* outputBlobs, const char* outputName, ResultVoid* ocvrs_return) {
		try {
			instance->forward(*outputBlobs, std::string(outputName));
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_forward_const__OutputArrayR(cv::dnn::Net* instance, const cv::_OutputArray* outputBlobs, ResultVoid* ocvrs_return) {
		try {
			instance->forward(*outputBlobs);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_forward_const__OutputArrayR_const_vectorLStringGR(cv::dnn::Net* instance, const cv::_OutputArray* outputBlobs, const std::vector<cv::String>* outBlobNames, ResultVoid* ocvrs_return) {
		try {
			instance->forward(*outputBlobs, *outBlobNames);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_forward_vectorLvectorLMatGGR_const_vectorLStringGR(cv::dnn::Net* instance, std::vector<std::vector<cv::Mat>>* outputBlobs, const std::vector<cv::String>* outBlobNames, ResultVoid* ocvrs_return) {
		try {
			instance->forward(*outputBlobs, *outBlobNames);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_quantize_const__InputArrayR_int_int(cv::dnn::Net* instance, const cv::_InputArray* calibData, int inputsDtype, int outputsDtype, Result<cv::dnn::Net*>* ocvrs_return) {
		try {
			cv::dnn::Net ret = instance->quantize(*calibData, inputsDtype, outputsDtype);
			Ok(new cv::dnn::Net(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_getInputDetails_const_vectorLfloatGR_vectorLintGR(const cv::dnn::Net* instance, std::vector<float>* scales, std::vector<int>* zeropoints, ResultVoid* ocvrs_return) {
		try {
			instance->getInputDetails(*scales, *zeropoints);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_getOutputDetails_const_vectorLfloatGR_vectorLintGR(const cv::dnn::Net* instance, std::vector<float>* scales, std::vector<int>* zeropoints, ResultVoid* ocvrs_return) {
		try {
			instance->getOutputDetails(*scales, *zeropoints);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_setHalideScheduler_const_StringR(cv::dnn::Net* instance, const char* scheduler, ResultVoid* ocvrs_return) {
		try {
			instance->setHalideScheduler(std::string(scheduler));
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_setPreferableBackend_int(cv::dnn::Net* instance, int backendId, ResultVoid* ocvrs_return) {
		try {
			instance->setPreferableBackend(backendId);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_setPreferableTarget_int(cv::dnn::Net* instance, int targetId, ResultVoid* ocvrs_return) {
		try {
			instance->setPreferableTarget(targetId);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_setInput_const__InputArrayR_const_StringR_double_const_ScalarR(cv::dnn::Net* instance, const cv::_InputArray* blob, const char* name, double scalefactor, const cv::Scalar* mean, ResultVoid* ocvrs_return) {
		try {
			instance->setInput(*blob, std::string(name), scalefactor, *mean);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_setInput_const__InputArrayR(cv::dnn::Net* instance, const cv::_InputArray* blob, ResultVoid* ocvrs_return) {
		try {
			instance->setInput(*blob);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_setParam_LayerId_int_const_MatR(cv::dnn::Net* instance, cv::dnn::Net::LayerId* layer, int numParam, const cv::Mat* blob, ResultVoid* ocvrs_return) {
		try {
			instance->setParam(*layer, numParam, *blob);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_getParam_LayerId_int(cv::dnn::Net* instance, cv::dnn::Net::LayerId* layer, int numParam, Result<cv::Mat*>* ocvrs_return) {
		try {
			cv::Mat ret = instance->getParam(*layer, numParam);
			Ok(new cv::Mat(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_getParam_LayerId(cv::dnn::Net* instance, cv::dnn::Net::LayerId* layer, Result<cv::Mat*>* ocvrs_return) {
		try {
			cv::Mat ret = instance->getParam(*layer);
			Ok(new cv::Mat(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_getUnconnectedOutLayers_const(const cv::dnn::Net* instance, Result<std::vector<int>*>* ocvrs_return) {
		try {
			std::vector<int> ret = instance->getUnconnectedOutLayers();
			Ok(new std::vector<int>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_getUnconnectedOutLayersNames_const(const cv::dnn::Net* instance, Result<std::vector<cv::String>*>* ocvrs_return) {
		try {
			std::vector<cv::String> ret = instance->getUnconnectedOutLayersNames();
			Ok(new std::vector<cv::String>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_getLayersShapes_const_const_vectorLMatShapeGR_vectorLintGR_vectorLvectorLMatShapeGGR_vectorLvectorLMatShapeGGR(const cv::dnn::Net* instance, const std::vector<cv::dnn::MatShape>* netInputShapes, std::vector<int>* layersIds, std::vector<std::vector<cv::dnn::MatShape>>* inLayersShapes, std::vector<std::vector<cv::dnn::MatShape>>* outLayersShapes, ResultVoid* ocvrs_return) {
		try {
			instance->getLayersShapes(*netInputShapes, *layersIds, *inLayersShapes, *outLayersShapes);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_getLayersShapes_const_const_MatShapeR_vectorLintGR_vectorLvectorLMatShapeGGR_vectorLvectorLMatShapeGGR(const cv::dnn::Net* instance, const cv::dnn::MatShape* netInputShape, std::vector<int>* layersIds, std::vector<std::vector<cv::dnn::MatShape>>* inLayersShapes, std::vector<std::vector<cv::dnn::MatShape>>* outLayersShapes, ResultVoid* ocvrs_return) {
		try {
			instance->getLayersShapes(*netInputShape, *layersIds, *inLayersShapes, *outLayersShapes);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_getLayerShapes_const_const_MatShapeR_const_int_vectorLMatShapeGR_vectorLMatShapeGR(const cv::dnn::Net* instance, const cv::dnn::MatShape* netInputShape, const int layerId, std::vector<cv::dnn::MatShape>* inLayerShapes, std::vector<cv::dnn::MatShape>* outLayerShapes, ResultVoid* ocvrs_return) {
		try {
			instance->getLayerShapes(*netInputShape, layerId, *inLayerShapes, *outLayerShapes);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_getLayerShapes_const_const_vectorLMatShapeGR_const_int_vectorLMatShapeGR_vectorLMatShapeGR(const cv::dnn::Net* instance, const std::vector<cv::dnn::MatShape>* netInputShapes, const int layerId, std::vector<cv::dnn::MatShape>* inLayerShapes, std::vector<cv::dnn::MatShape>* outLayerShapes, ResultVoid* ocvrs_return) {
		try {
			instance->getLayerShapes(*netInputShapes, layerId, *inLayerShapes, *outLayerShapes);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_getFLOPS_const_const_vectorLMatShapeGR(const cv::dnn::Net* instance, const std::vector<cv::dnn::MatShape>* netInputShapes, Result<int64_t>* ocvrs_return) {
		try {
			int64_t ret = instance->getFLOPS(*netInputShapes);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_getFLOPS_const_const_MatShapeR(const cv::dnn::Net* instance, const cv::dnn::MatShape* netInputShape, Result<int64_t>* ocvrs_return) {
		try {
			int64_t ret = instance->getFLOPS(*netInputShape);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_getFLOPS_const_const_int_const_vectorLMatShapeGR(const cv::dnn::Net* instance, const int layerId, const std::vector<cv::dnn::MatShape>* netInputShapes, Result<int64_t>* ocvrs_return) {
		try {
			int64_t ret = instance->getFLOPS(layerId, *netInputShapes);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_getFLOPS_const_const_int_const_MatShapeR(const cv::dnn::Net* instance, const int layerId, const cv::dnn::MatShape* netInputShape, Result<int64_t>* ocvrs_return) {
		try {
			int64_t ret = instance->getFLOPS(layerId, *netInputShape);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_getLayerTypes_const_vectorLStringGR(const cv::dnn::Net* instance, std::vector<cv::String>* layersTypes, ResultVoid* ocvrs_return) {
		try {
			instance->getLayerTypes(*layersTypes);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_getLayersCount_const_const_StringR(const cv::dnn::Net* instance, const char* layerType, Result<int>* ocvrs_return) {
		try {
			int ret = instance->getLayersCount(std::string(layerType));
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_getMemoryConsumption_const_const_vectorLMatShapeGR_size_tR_size_tR(const cv::dnn::Net* instance, const std::vector<cv::dnn::MatShape>* netInputShapes, size_t* weights, size_t* blobs, ResultVoid* ocvrs_return) {
		try {
			instance->getMemoryConsumption(*netInputShapes, *weights, *blobs);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_getMemoryConsumption_const_const_MatShapeR_size_tR_size_tR(const cv::dnn::Net* instance, const cv::dnn::MatShape* netInputShape, size_t* weights, size_t* blobs, ResultVoid* ocvrs_return) {
		try {
			instance->getMemoryConsumption(*netInputShape, *weights, *blobs);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_getMemoryConsumption_const_const_int_const_vectorLMatShapeGR_size_tR_size_tR(const cv::dnn::Net* instance, const int layerId, const std::vector<cv::dnn::MatShape>* netInputShapes, size_t* weights, size_t* blobs, ResultVoid* ocvrs_return) {
		try {
			instance->getMemoryConsumption(layerId, *netInputShapes, *weights, *blobs);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_getMemoryConsumption_const_const_int_const_MatShapeR_size_tR_size_tR(const cv::dnn::Net* instance, const int layerId, const cv::dnn::MatShape* netInputShape, size_t* weights, size_t* blobs, ResultVoid* ocvrs_return) {
		try {
			instance->getMemoryConsumption(layerId, *netInputShape, *weights, *blobs);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_getMemoryConsumption_const_const_vectorLMatShapeGR_vectorLintGR_vectorLsize_tGR_vectorLsize_tGR(const cv::dnn::Net* instance, const std::vector<cv::dnn::MatShape>* netInputShapes, std::vector<int>* layerIds, std::vector<size_t>* weights, std::vector<size_t>* blobs, ResultVoid* ocvrs_return) {
		try {
			instance->getMemoryConsumption(*netInputShapes, *layerIds, *weights, *blobs);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_getMemoryConsumption_const_const_MatShapeR_vectorLintGR_vectorLsize_tGR_vectorLsize_tGR(const cv::dnn::Net* instance, const cv::dnn::MatShape* netInputShape, std::vector<int>* layerIds, std::vector<size_t>* weights, std::vector<size_t>* blobs, ResultVoid* ocvrs_return) {
		try {
			instance->getMemoryConsumption(*netInputShape, *layerIds, *weights, *blobs);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_enableFusion_bool(cv::dnn::Net* instance, bool fusion, ResultVoid* ocvrs_return) {
		try {
			instance->enableFusion(fusion);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_Net_getPerfProfile_vectorLdoubleGR(cv::dnn::Net* instance, std::vector<double>* timings, Result<int64_t>* ocvrs_return) {
		try {
			int64_t ret = instance->getPerfProfile(*timings);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::dnn::Net* cv_dnn_Net_implicitClone_const(const cv::dnn::Net* instance) {
			return new cv::dnn::Net(*instance);
	}
	
	void cv_dnn_Net_delete(cv::dnn::Net* instance) {
			delete instance;
	}
	
	void cv_dnn_NormalizeBBoxLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::NormalizeBBoxLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::NormalizeBBoxLayer> ret = cv::dnn::NormalizeBBoxLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::NormalizeBBoxLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	float cv_dnn_NormalizeBBoxLayer_propPnorm_const(const cv::dnn::NormalizeBBoxLayer* instance) {
			float ret = instance->pnorm;
			return ret;
	}
	
	void cv_dnn_NormalizeBBoxLayer_propPnorm_const_float(cv::dnn::NormalizeBBoxLayer* instance, const float val) {
			instance->pnorm = val;
	}
	
	float cv_dnn_NormalizeBBoxLayer_propEpsilon_const(const cv::dnn::NormalizeBBoxLayer* instance) {
			float ret = instance->epsilon;
			return ret;
	}
	
	void cv_dnn_NormalizeBBoxLayer_propEpsilon_const_float(cv::dnn::NormalizeBBoxLayer* instance, const float val) {
			instance->epsilon = val;
	}
	
	bool cv_dnn_NormalizeBBoxLayer_propAcrossSpatial_const(const cv::dnn::NormalizeBBoxLayer* instance) {
			bool ret = instance->acrossSpatial;
			return ret;
	}
	
	void cv_dnn_NormalizeBBoxLayer_propAcrossSpatial_const_bool(cv::dnn::NormalizeBBoxLayer* instance, const bool val) {
			instance->acrossSpatial = val;
	}
	
	cv::Algorithm* cv_dnn_NormalizeBBoxLayer_to_Algorithm(cv::dnn::NormalizeBBoxLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_NormalizeBBoxLayer_to_Layer(cv::dnn::NormalizeBBoxLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_NormalizeBBoxLayer_delete(cv::dnn::NormalizeBBoxLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_PaddingLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::PaddingLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::PaddingLayer> ret = cv::dnn::PaddingLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::PaddingLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::Algorithm* cv_dnn_PaddingLayer_to_Algorithm(cv::dnn::PaddingLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_PaddingLayer_to_Layer(cv::dnn::PaddingLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_PaddingLayer_delete(cv::dnn::PaddingLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_PermuteLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::PermuteLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::PermuteLayer> ret = cv::dnn::PermuteLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::PermuteLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::Algorithm* cv_dnn_PermuteLayer_to_Algorithm(cv::dnn::PermuteLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_PermuteLayer_to_Layer(cv::dnn::PermuteLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_PermuteLayer_delete(cv::dnn::PermuteLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_PoolingLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::PoolingLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::PoolingLayer> ret = cv::dnn::PoolingLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::PoolingLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	int cv_dnn_PoolingLayer_propType_const(const cv::dnn::PoolingLayer* instance) {
			int ret = instance->type;
			return ret;
	}
	
	void cv_dnn_PoolingLayer_propType_const_int(cv::dnn::PoolingLayer* instance, const int val) {
			instance->type = val;
	}
	
	std::vector<size_t>* cv_dnn_PoolingLayer_propKernel_size_const(const cv::dnn::PoolingLayer* instance) {
			std::vector<size_t> ret = instance->kernel_size;
			return new std::vector<size_t>(ret);
	}
	
	void cv_dnn_PoolingLayer_propKernel_size_const_vectorLsize_tG(cv::dnn::PoolingLayer* instance, const std::vector<size_t>* val) {
			instance->kernel_size = *val;
	}
	
	std::vector<size_t>* cv_dnn_PoolingLayer_propStrides_const(const cv::dnn::PoolingLayer* instance) {
			std::vector<size_t> ret = instance->strides;
			return new std::vector<size_t>(ret);
	}
	
	void cv_dnn_PoolingLayer_propStrides_const_vectorLsize_tG(cv::dnn::PoolingLayer* instance, const std::vector<size_t>* val) {
			instance->strides = *val;
	}
	
	std::vector<size_t>* cv_dnn_PoolingLayer_propPads_begin_const(const cv::dnn::PoolingLayer* instance) {
			std::vector<size_t> ret = instance->pads_begin;
			return new std::vector<size_t>(ret);
	}
	
	void cv_dnn_PoolingLayer_propPads_begin_const_vectorLsize_tG(cv::dnn::PoolingLayer* instance, const std::vector<size_t>* val) {
			instance->pads_begin = *val;
	}
	
	std::vector<size_t>* cv_dnn_PoolingLayer_propPads_end_const(const cv::dnn::PoolingLayer* instance) {
			std::vector<size_t> ret = instance->pads_end;
			return new std::vector<size_t>(ret);
	}
	
	void cv_dnn_PoolingLayer_propPads_end_const_vectorLsize_tG(cv::dnn::PoolingLayer* instance, const std::vector<size_t>* val) {
			instance->pads_end = *val;
	}
	
	bool cv_dnn_PoolingLayer_propGlobalPooling_const(const cv::dnn::PoolingLayer* instance) {
			bool ret = instance->globalPooling;
			return ret;
	}
	
	void cv_dnn_PoolingLayer_propGlobalPooling_const_bool(cv::dnn::PoolingLayer* instance, const bool val) {
			instance->globalPooling = val;
	}
	
	std::vector<bool>* cv_dnn_PoolingLayer_propIsGlobalPooling_const(const cv::dnn::PoolingLayer* instance) {
			std::vector<bool> ret = instance->isGlobalPooling;
			return new std::vector<bool>(ret);
	}
	
	void cv_dnn_PoolingLayer_propIsGlobalPooling_const_vectorLboolG(cv::dnn::PoolingLayer* instance, const std::vector<bool>* val) {
			instance->isGlobalPooling = *val;
	}
	
	bool cv_dnn_PoolingLayer_propComputeMaxIdx_const(const cv::dnn::PoolingLayer* instance) {
			bool ret = instance->computeMaxIdx;
			return ret;
	}
	
	void cv_dnn_PoolingLayer_propComputeMaxIdx_const_bool(cv::dnn::PoolingLayer* instance, const bool val) {
			instance->computeMaxIdx = val;
	}
	
	void* cv_dnn_PoolingLayer_propPadMode_const(const cv::dnn::PoolingLayer* instance) {
			cv::String ret = instance->padMode;
			return ocvrs_create_string(ret.c_str());
	}
	
	void cv_dnn_PoolingLayer_propPadMode_const_String(cv::dnn::PoolingLayer* instance, const char* val) {
			instance->padMode = std::string(val);
	}
	
	bool cv_dnn_PoolingLayer_propCeilMode_const(const cv::dnn::PoolingLayer* instance) {
			bool ret = instance->ceilMode;
			return ret;
	}
	
	void cv_dnn_PoolingLayer_propCeilMode_const_bool(cv::dnn::PoolingLayer* instance, const bool val) {
			instance->ceilMode = val;
	}
	
	bool cv_dnn_PoolingLayer_propAvePoolPaddedArea_const(const cv::dnn::PoolingLayer* instance) {
			bool ret = instance->avePoolPaddedArea;
			return ret;
	}
	
	void cv_dnn_PoolingLayer_propAvePoolPaddedArea_const_bool(cv::dnn::PoolingLayer* instance, const bool val) {
			instance->avePoolPaddedArea = val;
	}
	
	void cv_dnn_PoolingLayer_propPooledSize_const(const cv::dnn::PoolingLayer* instance, cv::Size* ocvrs_return) {
			cv::Size ret = instance->pooledSize;
			*ocvrs_return = ret;
	}
	
	void cv_dnn_PoolingLayer_propPooledSize_const_Size(cv::dnn::PoolingLayer* instance, const cv::Size* val) {
			instance->pooledSize = *val;
	}
	
	float cv_dnn_PoolingLayer_propSpatialScale_const(const cv::dnn::PoolingLayer* instance) {
			float ret = instance->spatialScale;
			return ret;
	}
	
	void cv_dnn_PoolingLayer_propSpatialScale_const_float(cv::dnn::PoolingLayer* instance, const float val) {
			instance->spatialScale = val;
	}
	
	int cv_dnn_PoolingLayer_propPsRoiOutChannels_const(const cv::dnn::PoolingLayer* instance) {
			int ret = instance->psRoiOutChannels;
			return ret;
	}
	
	void cv_dnn_PoolingLayer_propPsRoiOutChannels_const_int(cv::dnn::PoolingLayer* instance, const int val) {
			instance->psRoiOutChannels = val;
	}
	
	cv::Algorithm* cv_dnn_PoolingLayer_to_Algorithm(cv::dnn::PoolingLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_PoolingLayer_to_Layer(cv::dnn::PoolingLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_PoolingLayer_delete(cv::dnn::PoolingLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_PoolingLayerInt8_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::PoolingLayerInt8>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::PoolingLayerInt8> ret = cv::dnn::PoolingLayerInt8::create(*params);
			Ok(new cv::Ptr<cv::dnn::PoolingLayerInt8>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	int cv_dnn_PoolingLayerInt8_propInput_zp_const(const cv::dnn::PoolingLayerInt8* instance) {
			int ret = instance->input_zp;
			return ret;
	}
	
	void cv_dnn_PoolingLayerInt8_propInput_zp_const_int(cv::dnn::PoolingLayerInt8* instance, const int val) {
			instance->input_zp = val;
	}
	
	int cv_dnn_PoolingLayerInt8_propOutput_zp_const(const cv::dnn::PoolingLayerInt8* instance) {
			int ret = instance->output_zp;
			return ret;
	}
	
	void cv_dnn_PoolingLayerInt8_propOutput_zp_const_int(cv::dnn::PoolingLayerInt8* instance, const int val) {
			instance->output_zp = val;
	}
	
	cv::Algorithm* cv_dnn_PoolingLayerInt8_to_Algorithm(cv::dnn::PoolingLayerInt8* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_PoolingLayerInt8_to_Layer(cv::dnn::PoolingLayerInt8* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	cv::dnn::PoolingLayer* cv_dnn_PoolingLayerInt8_to_PoolingLayer(cv::dnn::PoolingLayerInt8* instance) {
			return dynamic_cast<cv::dnn::PoolingLayer*>(instance);
	}
	
	void cv_dnn_PoolingLayerInt8_delete(cv::dnn::PoolingLayerInt8* instance) {
			delete instance;
	}
	
	void cv_dnn_PowerLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::PowerLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::PowerLayer> ret = cv::dnn::PowerLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::PowerLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	float cv_dnn_PowerLayer_propPower_const(const cv::dnn::PowerLayer* instance) {
			float ret = instance->power;
			return ret;
	}
	
	void cv_dnn_PowerLayer_propPower_const_float(cv::dnn::PowerLayer* instance, const float val) {
			instance->power = val;
	}
	
	float cv_dnn_PowerLayer_propScale_const(const cv::dnn::PowerLayer* instance) {
			float ret = instance->scale;
			return ret;
	}
	
	void cv_dnn_PowerLayer_propScale_const_float(cv::dnn::PowerLayer* instance, const float val) {
			instance->scale = val;
	}
	
	float cv_dnn_PowerLayer_propShift_const(const cv::dnn::PowerLayer* instance) {
			float ret = instance->shift;
			return ret;
	}
	
	void cv_dnn_PowerLayer_propShift_const_float(cv::dnn::PowerLayer* instance, const float val) {
			instance->shift = val;
	}
	
	cv::dnn::ActivationLayer* cv_dnn_PowerLayer_to_ActivationLayer(cv::dnn::PowerLayer* instance) {
			return dynamic_cast<cv::dnn::ActivationLayer*>(instance);
	}
	
	cv::Algorithm* cv_dnn_PowerLayer_to_Algorithm(cv::dnn::PowerLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_PowerLayer_to_Layer(cv::dnn::PowerLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_PowerLayer_delete(cv::dnn::PowerLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_PriorBoxLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::PriorBoxLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::PriorBoxLayer> ret = cv::dnn::PriorBoxLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::PriorBoxLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::Algorithm* cv_dnn_PriorBoxLayer_to_Algorithm(cv::dnn::PriorBoxLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_PriorBoxLayer_to_Layer(cv::dnn::PriorBoxLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_PriorBoxLayer_delete(cv::dnn::PriorBoxLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_ProposalLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::ProposalLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::ProposalLayer> ret = cv::dnn::ProposalLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::ProposalLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::Algorithm* cv_dnn_ProposalLayer_to_Algorithm(cv::dnn::ProposalLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_ProposalLayer_to_Layer(cv::dnn::ProposalLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_ProposalLayer_delete(cv::dnn::ProposalLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_QuantizeLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::QuantizeLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::QuantizeLayer> ret = cv::dnn::QuantizeLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::QuantizeLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	float cv_dnn_QuantizeLayer_propScale_const(const cv::dnn::QuantizeLayer* instance) {
			float ret = instance->scale;
			return ret;
	}
	
	void cv_dnn_QuantizeLayer_propScale_const_float(cv::dnn::QuantizeLayer* instance, const float val) {
			instance->scale = val;
	}
	
	int cv_dnn_QuantizeLayer_propZeropoint_const(const cv::dnn::QuantizeLayer* instance) {
			int ret = instance->zeropoint;
			return ret;
	}
	
	void cv_dnn_QuantizeLayer_propZeropoint_const_int(cv::dnn::QuantizeLayer* instance, const int val) {
			instance->zeropoint = val;
	}
	
	cv::Algorithm* cv_dnn_QuantizeLayer_to_Algorithm(cv::dnn::QuantizeLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_QuantizeLayer_to_Layer(cv::dnn::QuantizeLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_QuantizeLayer_delete(cv::dnn::QuantizeLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_RNNLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::RNNLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::RNNLayer> ret = cv::dnn::RNNLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::RNNLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_RNNLayer_setWeights_const_MatR_const_MatR_const_MatR_const_MatR_const_MatR(cv::dnn::RNNLayer* instance, const cv::Mat* Wxh, const cv::Mat* bh, const cv::Mat* Whh, const cv::Mat* Who, const cv::Mat* bo, ResultVoid* ocvrs_return) {
		try {
			instance->setWeights(*Wxh, *bh, *Whh, *Who, *bo);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_RNNLayer_setProduceHiddenOutput_bool(cv::dnn::RNNLayer* instance, bool produce, ResultVoid* ocvrs_return) {
		try {
			instance->setProduceHiddenOutput(produce);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_RNNLayer_setProduceHiddenOutput(cv::dnn::RNNLayer* instance, ResultVoid* ocvrs_return) {
		try {
			instance->setProduceHiddenOutput();
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::Algorithm* cv_dnn_RNNLayer_to_Algorithm(cv::dnn::RNNLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_RNNLayer_to_Layer(cv::dnn::RNNLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_RNNLayer_delete(cv::dnn::RNNLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_ReLU6Layer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::ReLU6Layer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::ReLU6Layer> ret = cv::dnn::ReLU6Layer::create(*params);
			Ok(new cv::Ptr<cv::dnn::ReLU6Layer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	float cv_dnn_ReLU6Layer_propMinValue_const(const cv::dnn::ReLU6Layer* instance) {
			float ret = instance->minValue;
			return ret;
	}
	
	void cv_dnn_ReLU6Layer_propMinValue_const_float(cv::dnn::ReLU6Layer* instance, const float val) {
			instance->minValue = val;
	}
	
	float cv_dnn_ReLU6Layer_propMaxValue_const(const cv::dnn::ReLU6Layer* instance) {
			float ret = instance->maxValue;
			return ret;
	}
	
	void cv_dnn_ReLU6Layer_propMaxValue_const_float(cv::dnn::ReLU6Layer* instance, const float val) {
			instance->maxValue = val;
	}
	
	cv::dnn::ActivationLayer* cv_dnn_ReLU6Layer_to_ActivationLayer(cv::dnn::ReLU6Layer* instance) {
			return dynamic_cast<cv::dnn::ActivationLayer*>(instance);
	}
	
	cv::Algorithm* cv_dnn_ReLU6Layer_to_Algorithm(cv::dnn::ReLU6Layer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_ReLU6Layer_to_Layer(cv::dnn::ReLU6Layer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_ReLU6Layer_delete(cv::dnn::ReLU6Layer* instance) {
			delete instance;
	}
	
	void cv_dnn_ReLULayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::ReLULayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::ReLULayer> ret = cv::dnn::ReLULayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::ReLULayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	float cv_dnn_ReLULayer_propNegativeSlope_const(const cv::dnn::ReLULayer* instance) {
			float ret = instance->negativeSlope;
			return ret;
	}
	
	void cv_dnn_ReLULayer_propNegativeSlope_const_float(cv::dnn::ReLULayer* instance, const float val) {
			instance->negativeSlope = val;
	}
	
	cv::dnn::ActivationLayer* cv_dnn_ReLULayer_to_ActivationLayer(cv::dnn::ReLULayer* instance) {
			return dynamic_cast<cv::dnn::ActivationLayer*>(instance);
	}
	
	cv::Algorithm* cv_dnn_ReLULayer_to_Algorithm(cv::dnn::ReLULayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_ReLULayer_to_Layer(cv::dnn::ReLULayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_ReLULayer_delete(cv::dnn::ReLULayer* instance) {
			delete instance;
	}
	
	void cv_dnn_RegionLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::RegionLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::RegionLayer> ret = cv::dnn::RegionLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::RegionLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	float cv_dnn_RegionLayer_propNmsThreshold_const(const cv::dnn::RegionLayer* instance) {
			float ret = instance->nmsThreshold;
			return ret;
	}
	
	void cv_dnn_RegionLayer_propNmsThreshold_const_float(cv::dnn::RegionLayer* instance, const float val) {
			instance->nmsThreshold = val;
	}
	
	cv::Algorithm* cv_dnn_RegionLayer_to_Algorithm(cv::dnn::RegionLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_RegionLayer_to_Layer(cv::dnn::RegionLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_RegionLayer_delete(cv::dnn::RegionLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_ReorgLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::ReorgLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::ReorgLayer> ret = cv::dnn::ReorgLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::ReorgLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::Algorithm* cv_dnn_ReorgLayer_to_Algorithm(cv::dnn::ReorgLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_ReorgLayer_to_Layer(cv::dnn::ReorgLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_ReorgLayer_delete(cv::dnn::ReorgLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_RequantizeLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::RequantizeLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::RequantizeLayer> ret = cv::dnn::RequantizeLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::RequantizeLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	float cv_dnn_RequantizeLayer_propScale_const(const cv::dnn::RequantizeLayer* instance) {
			float ret = instance->scale;
			return ret;
	}
	
	void cv_dnn_RequantizeLayer_propScale_const_float(cv::dnn::RequantizeLayer* instance, const float val) {
			instance->scale = val;
	}
	
	float cv_dnn_RequantizeLayer_propShift_const(const cv::dnn::RequantizeLayer* instance) {
			float ret = instance->shift;
			return ret;
	}
	
	void cv_dnn_RequantizeLayer_propShift_const_float(cv::dnn::RequantizeLayer* instance, const float val) {
			instance->shift = val;
	}
	
	cv::Algorithm* cv_dnn_RequantizeLayer_to_Algorithm(cv::dnn::RequantizeLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_RequantizeLayer_to_Layer(cv::dnn::RequantizeLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_RequantizeLayer_delete(cv::dnn::RequantizeLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_ReshapeLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::ReshapeLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::ReshapeLayer> ret = cv::dnn::ReshapeLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::ReshapeLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::dnn::MatShape* cv_dnn_ReshapeLayer_propNewShapeDesc_const(const cv::dnn::ReshapeLayer* instance) {
			cv::dnn::MatShape ret = instance->newShapeDesc;
			return new cv::dnn::MatShape(ret);
	}
	
	void cv_dnn_ReshapeLayer_propNewShapeDesc_const_MatShape(cv::dnn::ReshapeLayer* instance, const cv::dnn::MatShape* val) {
			instance->newShapeDesc = *val;
	}
	
	cv::Range* cv_dnn_ReshapeLayer_propNewShapeRange_const(const cv::dnn::ReshapeLayer* instance) {
			cv::Range ret = instance->newShapeRange;
			return new cv::Range(ret);
	}
	
	void cv_dnn_ReshapeLayer_propNewShapeRange_const_Range(cv::dnn::ReshapeLayer* instance, const cv::Range* val) {
			instance->newShapeRange = *val;
	}
	
	cv::Algorithm* cv_dnn_ReshapeLayer_to_Algorithm(cv::dnn::ReshapeLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_ReshapeLayer_to_Layer(cv::dnn::ReshapeLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_ReshapeLayer_delete(cv::dnn::ReshapeLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_ResizeLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::ResizeLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::ResizeLayer> ret = cv::dnn::ResizeLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::ResizeLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::Algorithm* cv_dnn_ResizeLayer_to_Algorithm(cv::dnn::ResizeLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_ResizeLayer_to_Layer(cv::dnn::ResizeLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_ResizeLayer_delete(cv::dnn::ResizeLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_ScaleLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::ScaleLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::ScaleLayer> ret = cv::dnn::ScaleLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::ScaleLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	bool cv_dnn_ScaleLayer_propHasBias_const(const cv::dnn::ScaleLayer* instance) {
			bool ret = instance->hasBias;
			return ret;
	}
	
	void cv_dnn_ScaleLayer_propHasBias_const_bool(cv::dnn::ScaleLayer* instance, const bool val) {
			instance->hasBias = val;
	}
	
	int cv_dnn_ScaleLayer_propAxis_const(const cv::dnn::ScaleLayer* instance) {
			int ret = instance->axis;
			return ret;
	}
	
	void cv_dnn_ScaleLayer_propAxis_const_int(cv::dnn::ScaleLayer* instance, const int val) {
			instance->axis = val;
	}
	
	cv::Algorithm* cv_dnn_ScaleLayer_to_Algorithm(cv::dnn::ScaleLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_ScaleLayer_to_Layer(cv::dnn::ScaleLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_ScaleLayer_delete(cv::dnn::ScaleLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_ScaleLayerInt8_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::ScaleLayerInt8>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::ScaleLayerInt8> ret = cv::dnn::ScaleLayerInt8::create(*params);
			Ok(new cv::Ptr<cv::dnn::ScaleLayerInt8>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	float cv_dnn_ScaleLayerInt8_propOutput_sc_const(const cv::dnn::ScaleLayerInt8* instance) {
			float ret = instance->output_sc;
			return ret;
	}
	
	void cv_dnn_ScaleLayerInt8_propOutput_sc_const_float(cv::dnn::ScaleLayerInt8* instance, const float val) {
			instance->output_sc = val;
	}
	
	int cv_dnn_ScaleLayerInt8_propOutput_zp_const(const cv::dnn::ScaleLayerInt8* instance) {
			int ret = instance->output_zp;
			return ret;
	}
	
	void cv_dnn_ScaleLayerInt8_propOutput_zp_const_int(cv::dnn::ScaleLayerInt8* instance, const int val) {
			instance->output_zp = val;
	}
	
	cv::Algorithm* cv_dnn_ScaleLayerInt8_to_Algorithm(cv::dnn::ScaleLayerInt8* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_ScaleLayerInt8_to_Layer(cv::dnn::ScaleLayerInt8* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	cv::dnn::ScaleLayer* cv_dnn_ScaleLayerInt8_to_ScaleLayer(cv::dnn::ScaleLayerInt8* instance) {
			return dynamic_cast<cv::dnn::ScaleLayer*>(instance);
	}
	
	void cv_dnn_ScaleLayerInt8_delete(cv::dnn::ScaleLayerInt8* instance) {
			delete instance;
	}
	
	void cv_dnn_SegmentationModel_SegmentationModel_const_StringR_const_StringR(const char* model, const char* config, Result<cv::dnn::SegmentationModel*>* ocvrs_return) {
		try {
			cv::dnn::SegmentationModel* ret = new cv::dnn::SegmentationModel(std::string(model), std::string(config));
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_SegmentationModel_SegmentationModel_const_StringR(const char* model, Result<cv::dnn::SegmentationModel*>* ocvrs_return) {
		try {
			cv::dnn::SegmentationModel* ret = new cv::dnn::SegmentationModel(std::string(model));
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_SegmentationModel_SegmentationModel_const_NetR(const cv::dnn::Net* network, Result<cv::dnn::SegmentationModel*>* ocvrs_return) {
		try {
			cv::dnn::SegmentationModel* ret = new cv::dnn::SegmentationModel(*network);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_SegmentationModel_segment_const__InputArrayR_const__OutputArrayR(cv::dnn::SegmentationModel* instance, const cv::_InputArray* frame, const cv::_OutputArray* mask, ResultVoid* ocvrs_return) {
		try {
			instance->segment(*frame, *mask);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::dnn::SegmentationModel* cv_dnn_SegmentationModel_implicitClone_const(const cv::dnn::SegmentationModel* instance) {
			return new cv::dnn::SegmentationModel(*instance);
	}
	
	cv::dnn::Model* cv_dnn_SegmentationModel_to_Model(cv::dnn::SegmentationModel* instance) {
			return dynamic_cast<cv::dnn::Model*>(instance);
	}
	
	void cv_dnn_SegmentationModel_delete(cv::dnn::SegmentationModel* instance) {
			delete instance;
	}
	
	void cv_dnn_ShiftLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::Layer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::Layer> ret = cv::dnn::ShiftLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::Layer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::Algorithm* cv_dnn_ShiftLayer_to_Algorithm(cv::dnn::ShiftLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_ShiftLayer_to_Layer(cv::dnn::ShiftLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_ShiftLayer_delete(cv::dnn::ShiftLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_ShiftLayerInt8_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::Layer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::Layer> ret = cv::dnn::ShiftLayerInt8::create(*params);
			Ok(new cv::Ptr<cv::dnn::Layer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::Algorithm* cv_dnn_ShiftLayerInt8_to_Algorithm(cv::dnn::ShiftLayerInt8* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_ShiftLayerInt8_to_Layer(cv::dnn::ShiftLayerInt8* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_ShiftLayerInt8_delete(cv::dnn::ShiftLayerInt8* instance) {
			delete instance;
	}
	
	void cv_dnn_ShuffleChannelLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::Layer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::Layer> ret = cv::dnn::ShuffleChannelLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::Layer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	int cv_dnn_ShuffleChannelLayer_propGroup_const(const cv::dnn::ShuffleChannelLayer* instance) {
			int ret = instance->group;
			return ret;
	}
	
	void cv_dnn_ShuffleChannelLayer_propGroup_const_int(cv::dnn::ShuffleChannelLayer* instance, const int val) {
			instance->group = val;
	}
	
	cv::Algorithm* cv_dnn_ShuffleChannelLayer_to_Algorithm(cv::dnn::ShuffleChannelLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_ShuffleChannelLayer_to_Layer(cv::dnn::ShuffleChannelLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_ShuffleChannelLayer_delete(cv::dnn::ShuffleChannelLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_SigmoidLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::SigmoidLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::SigmoidLayer> ret = cv::dnn::SigmoidLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::SigmoidLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::dnn::ActivationLayer* cv_dnn_SigmoidLayer_to_ActivationLayer(cv::dnn::SigmoidLayer* instance) {
			return dynamic_cast<cv::dnn::ActivationLayer*>(instance);
	}
	
	cv::Algorithm* cv_dnn_SigmoidLayer_to_Algorithm(cv::dnn::SigmoidLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_SigmoidLayer_to_Layer(cv::dnn::SigmoidLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_SigmoidLayer_delete(cv::dnn::SigmoidLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_SliceLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::SliceLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::SliceLayer> ret = cv::dnn::SliceLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::SliceLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	std::vector<std::vector<cv::Range>>* cv_dnn_SliceLayer_propSliceRanges_const(const cv::dnn::SliceLayer* instance) {
			std::vector<std::vector<cv::Range>> ret = instance->sliceRanges;
			return new std::vector<std::vector<cv::Range>>(ret);
	}
	
	void cv_dnn_SliceLayer_propSliceRanges_const_vectorLvectorLRangeGG(cv::dnn::SliceLayer* instance, const std::vector<std::vector<cv::Range>>* val) {
			instance->sliceRanges = *val;
	}
	
	std::vector<std::vector<int>>* cv_dnn_SliceLayer_propSliceSteps_const(const cv::dnn::SliceLayer* instance) {
			std::vector<std::vector<int>> ret = instance->sliceSteps;
			return new std::vector<std::vector<int>>(ret);
	}
	
	void cv_dnn_SliceLayer_propSliceSteps_const_vectorLvectorLintGG(cv::dnn::SliceLayer* instance, const std::vector<std::vector<int>>* val) {
			instance->sliceSteps = *val;
	}
	
	int cv_dnn_SliceLayer_propAxis_const(const cv::dnn::SliceLayer* instance) {
			int ret = instance->axis;
			return ret;
	}
	
	void cv_dnn_SliceLayer_propAxis_const_int(cv::dnn::SliceLayer* instance, const int val) {
			instance->axis = val;
	}
	
	int cv_dnn_SliceLayer_propNum_split_const(const cv::dnn::SliceLayer* instance) {
			int ret = instance->num_split;
			return ret;
	}
	
	void cv_dnn_SliceLayer_propNum_split_const_int(cv::dnn::SliceLayer* instance, const int val) {
			instance->num_split = val;
	}
	
	cv::Algorithm* cv_dnn_SliceLayer_to_Algorithm(cv::dnn::SliceLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_SliceLayer_to_Layer(cv::dnn::SliceLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_SliceLayer_delete(cv::dnn::SliceLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_SoftmaxLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::SoftmaxLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::SoftmaxLayer> ret = cv::dnn::SoftmaxLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::SoftmaxLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	bool cv_dnn_SoftmaxLayer_propLogSoftMax_const(const cv::dnn::SoftmaxLayer* instance) {
			bool ret = instance->logSoftMax;
			return ret;
	}
	
	void cv_dnn_SoftmaxLayer_propLogSoftMax_const_bool(cv::dnn::SoftmaxLayer* instance, const bool val) {
			instance->logSoftMax = val;
	}
	
	cv::Algorithm* cv_dnn_SoftmaxLayer_to_Algorithm(cv::dnn::SoftmaxLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_SoftmaxLayer_to_Layer(cv::dnn::SoftmaxLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_SoftmaxLayer_delete(cv::dnn::SoftmaxLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_SoftmaxLayerInt8_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::SoftmaxLayerInt8>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::SoftmaxLayerInt8> ret = cv::dnn::SoftmaxLayerInt8::create(*params);
			Ok(new cv::Ptr<cv::dnn::SoftmaxLayerInt8>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	float cv_dnn_SoftmaxLayerInt8_propOutput_sc_const(const cv::dnn::SoftmaxLayerInt8* instance) {
			float ret = instance->output_sc;
			return ret;
	}
	
	void cv_dnn_SoftmaxLayerInt8_propOutput_sc_const_float(cv::dnn::SoftmaxLayerInt8* instance, const float val) {
			instance->output_sc = val;
	}
	
	int cv_dnn_SoftmaxLayerInt8_propOutput_zp_const(const cv::dnn::SoftmaxLayerInt8* instance) {
			int ret = instance->output_zp;
			return ret;
	}
	
	void cv_dnn_SoftmaxLayerInt8_propOutput_zp_const_int(cv::dnn::SoftmaxLayerInt8* instance, const int val) {
			instance->output_zp = val;
	}
	
	cv::Algorithm* cv_dnn_SoftmaxLayerInt8_to_Algorithm(cv::dnn::SoftmaxLayerInt8* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_SoftmaxLayerInt8_to_Layer(cv::dnn::SoftmaxLayerInt8* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	cv::dnn::SoftmaxLayer* cv_dnn_SoftmaxLayerInt8_to_SoftmaxLayer(cv::dnn::SoftmaxLayerInt8* instance) {
			return dynamic_cast<cv::dnn::SoftmaxLayer*>(instance);
	}
	
	void cv_dnn_SoftmaxLayerInt8_delete(cv::dnn::SoftmaxLayerInt8* instance) {
			delete instance;
	}
	
	void cv_dnn_SplitLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::SplitLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::SplitLayer> ret = cv::dnn::SplitLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::SplitLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	int cv_dnn_SplitLayer_propOutputsCount_const(const cv::dnn::SplitLayer* instance) {
			int ret = instance->outputsCount;
			return ret;
	}
	
	void cv_dnn_SplitLayer_propOutputsCount_const_int(cv::dnn::SplitLayer* instance, const int val) {
			instance->outputsCount = val;
	}
	
	cv::Algorithm* cv_dnn_SplitLayer_to_Algorithm(cv::dnn::SplitLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_SplitLayer_to_Layer(cv::dnn::SplitLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_SplitLayer_delete(cv::dnn::SplitLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_SwishLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::SwishLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::SwishLayer> ret = cv::dnn::SwishLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::SwishLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::dnn::ActivationLayer* cv_dnn_SwishLayer_to_ActivationLayer(cv::dnn::SwishLayer* instance) {
			return dynamic_cast<cv::dnn::ActivationLayer*>(instance);
	}
	
	cv::Algorithm* cv_dnn_SwishLayer_to_Algorithm(cv::dnn::SwishLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_SwishLayer_to_Layer(cv::dnn::SwishLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_SwishLayer_delete(cv::dnn::SwishLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_TanHLayer_create_const_LayerParamsR(const cv::dnn::LayerParams* params, Result<cv::Ptr<cv::dnn::TanHLayer>*>* ocvrs_return) {
		try {
			cv::Ptr<cv::dnn::TanHLayer> ret = cv::dnn::TanHLayer::create(*params);
			Ok(new cv::Ptr<cv::dnn::TanHLayer>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::dnn::ActivationLayer* cv_dnn_TanHLayer_to_ActivationLayer(cv::dnn::TanHLayer* instance) {
			return dynamic_cast<cv::dnn::ActivationLayer*>(instance);
	}
	
	cv::Algorithm* cv_dnn_TanHLayer_to_Algorithm(cv::dnn::TanHLayer* instance) {
			return dynamic_cast<cv::Algorithm*>(instance);
	}
	
	cv::dnn::Layer* cv_dnn_TanHLayer_to_Layer(cv::dnn::TanHLayer* instance) {
			return dynamic_cast<cv::dnn::Layer*>(instance);
	}
	
	void cv_dnn_TanHLayer_delete(cv::dnn::TanHLayer* instance) {
			delete instance;
	}
	
	void cv_dnn_TextDetectionModel_detect_const_const__InputArrayR_vectorLvectorLPointGGR_vectorLfloatGR(const cv::dnn::TextDetectionModel* instance, const cv::_InputArray* frame, std::vector<std::vector<cv::Point>>* detections, std::vector<float>* confidences, ResultVoid* ocvrs_return) {
		try {
			instance->detect(*frame, *detections, *confidences);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextDetectionModel_detect_const_const__InputArrayR_vectorLvectorLPointGGR(const cv::dnn::TextDetectionModel* instance, const cv::_InputArray* frame, std::vector<std::vector<cv::Point>>* detections, ResultVoid* ocvrs_return) {
		try {
			instance->detect(*frame, *detections);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextDetectionModel_detectTextRectangles_const_const__InputArrayR_vectorLRotatedRectGR_vectorLfloatGR(const cv::dnn::TextDetectionModel* instance, const cv::_InputArray* frame, std::vector<cv::RotatedRect>* detections, std::vector<float>* confidences, ResultVoid* ocvrs_return) {
		try {
			instance->detectTextRectangles(*frame, *detections, *confidences);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextDetectionModel_detectTextRectangles_const_const__InputArrayR_vectorLRotatedRectGR(const cv::dnn::TextDetectionModel* instance, const cv::_InputArray* frame, std::vector<cv::RotatedRect>* detections, ResultVoid* ocvrs_return) {
		try {
			instance->detectTextRectangles(*frame, *detections);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::dnn::Model* cv_dnn_TextDetectionModel_to_Model(cv::dnn::TextDetectionModel* instance) {
			return dynamic_cast<cv::dnn::Model*>(instance);
	}
	
	void cv_dnn_TextDetectionModel_delete(cv::dnn::TextDetectionModel* instance) {
			delete instance;
	}
	
	void cv_dnn_TextDetectionModel_DB_TextDetectionModel_DB(Result<cv::dnn::TextDetectionModel_DB*>* ocvrs_return) {
		try {
			cv::dnn::TextDetectionModel_DB* ret = new cv::dnn::TextDetectionModel_DB();
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextDetectionModel_DB_TextDetectionModel_DB_const_NetR(const cv::dnn::Net* network, Result<cv::dnn::TextDetectionModel_DB*>* ocvrs_return) {
		try {
			cv::dnn::TextDetectionModel_DB* ret = new cv::dnn::TextDetectionModel_DB(*network);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextDetectionModel_DB_TextDetectionModel_DB_const_stringR_const_stringR(const char* model, const char* config, Result<cv::dnn::TextDetectionModel_DB*>* ocvrs_return) {
		try {
			cv::dnn::TextDetectionModel_DB* ret = new cv::dnn::TextDetectionModel_DB(std::string(model), std::string(config));
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextDetectionModel_DB_TextDetectionModel_DB_const_stringR(const char* model, Result<cv::dnn::TextDetectionModel_DB*>* ocvrs_return) {
		try {
			cv::dnn::TextDetectionModel_DB* ret = new cv::dnn::TextDetectionModel_DB(std::string(model));
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextDetectionModel_DB_setBinaryThreshold_float(cv::dnn::TextDetectionModel_DB* instance, float binaryThreshold, Result<cv::dnn::TextDetectionModel_DB*>* ocvrs_return) {
		try {
			cv::dnn::TextDetectionModel_DB ret = instance->setBinaryThreshold(binaryThreshold);
			Ok(new cv::dnn::TextDetectionModel_DB(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextDetectionModel_DB_getBinaryThreshold_const(const cv::dnn::TextDetectionModel_DB* instance, Result<float>* ocvrs_return) {
		try {
			float ret = instance->getBinaryThreshold();
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextDetectionModel_DB_setPolygonThreshold_float(cv::dnn::TextDetectionModel_DB* instance, float polygonThreshold, Result<cv::dnn::TextDetectionModel_DB*>* ocvrs_return) {
		try {
			cv::dnn::TextDetectionModel_DB ret = instance->setPolygonThreshold(polygonThreshold);
			Ok(new cv::dnn::TextDetectionModel_DB(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextDetectionModel_DB_getPolygonThreshold_const(const cv::dnn::TextDetectionModel_DB* instance, Result<float>* ocvrs_return) {
		try {
			float ret = instance->getPolygonThreshold();
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextDetectionModel_DB_setUnclipRatio_double(cv::dnn::TextDetectionModel_DB* instance, double unclipRatio, Result<cv::dnn::TextDetectionModel_DB*>* ocvrs_return) {
		try {
			cv::dnn::TextDetectionModel_DB ret = instance->setUnclipRatio(unclipRatio);
			Ok(new cv::dnn::TextDetectionModel_DB(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextDetectionModel_DB_getUnclipRatio_const(const cv::dnn::TextDetectionModel_DB* instance, Result<double>* ocvrs_return) {
		try {
			double ret = instance->getUnclipRatio();
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextDetectionModel_DB_setMaxCandidates_int(cv::dnn::TextDetectionModel_DB* instance, int maxCandidates, Result<cv::dnn::TextDetectionModel_DB*>* ocvrs_return) {
		try {
			cv::dnn::TextDetectionModel_DB ret = instance->setMaxCandidates(maxCandidates);
			Ok(new cv::dnn::TextDetectionModel_DB(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextDetectionModel_DB_getMaxCandidates_const(const cv::dnn::TextDetectionModel_DB* instance, Result<int>* ocvrs_return) {
		try {
			int ret = instance->getMaxCandidates();
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::dnn::TextDetectionModel_DB* cv_dnn_TextDetectionModel_DB_implicitClone_const(const cv::dnn::TextDetectionModel_DB* instance) {
			return new cv::dnn::TextDetectionModel_DB(*instance);
	}
	
	cv::dnn::Model* cv_dnn_TextDetectionModel_DB_to_Model(cv::dnn::TextDetectionModel_DB* instance) {
			return dynamic_cast<cv::dnn::Model*>(instance);
	}
	
	cv::dnn::TextDetectionModel* cv_dnn_TextDetectionModel_DB_to_TextDetectionModel(cv::dnn::TextDetectionModel_DB* instance) {
			return dynamic_cast<cv::dnn::TextDetectionModel*>(instance);
	}
	
	void cv_dnn_TextDetectionModel_DB_delete(cv::dnn::TextDetectionModel_DB* instance) {
			delete instance;
	}
	
	void cv_dnn_TextDetectionModel_EAST_TextDetectionModel_EAST(Result<cv::dnn::TextDetectionModel_EAST*>* ocvrs_return) {
		try {
			cv::dnn::TextDetectionModel_EAST* ret = new cv::dnn::TextDetectionModel_EAST();
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextDetectionModel_EAST_TextDetectionModel_EAST_const_NetR(const cv::dnn::Net* network, Result<cv::dnn::TextDetectionModel_EAST*>* ocvrs_return) {
		try {
			cv::dnn::TextDetectionModel_EAST* ret = new cv::dnn::TextDetectionModel_EAST(*network);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextDetectionModel_EAST_TextDetectionModel_EAST_const_stringR_const_stringR(const char* model, const char* config, Result<cv::dnn::TextDetectionModel_EAST*>* ocvrs_return) {
		try {
			cv::dnn::TextDetectionModel_EAST* ret = new cv::dnn::TextDetectionModel_EAST(std::string(model), std::string(config));
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextDetectionModel_EAST_TextDetectionModel_EAST_const_stringR(const char* model, Result<cv::dnn::TextDetectionModel_EAST*>* ocvrs_return) {
		try {
			cv::dnn::TextDetectionModel_EAST* ret = new cv::dnn::TextDetectionModel_EAST(std::string(model));
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextDetectionModel_EAST_setConfidenceThreshold_float(cv::dnn::TextDetectionModel_EAST* instance, float confThreshold, Result<cv::dnn::TextDetectionModel_EAST*>* ocvrs_return) {
		try {
			cv::dnn::TextDetectionModel_EAST ret = instance->setConfidenceThreshold(confThreshold);
			Ok(new cv::dnn::TextDetectionModel_EAST(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextDetectionModel_EAST_getConfidenceThreshold_const(const cv::dnn::TextDetectionModel_EAST* instance, Result<float>* ocvrs_return) {
		try {
			float ret = instance->getConfidenceThreshold();
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextDetectionModel_EAST_setNMSThreshold_float(cv::dnn::TextDetectionModel_EAST* instance, float nmsThreshold, Result<cv::dnn::TextDetectionModel_EAST*>* ocvrs_return) {
		try {
			cv::dnn::TextDetectionModel_EAST ret = instance->setNMSThreshold(nmsThreshold);
			Ok(new cv::dnn::TextDetectionModel_EAST(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextDetectionModel_EAST_getNMSThreshold_const(const cv::dnn::TextDetectionModel_EAST* instance, Result<float>* ocvrs_return) {
		try {
			float ret = instance->getNMSThreshold();
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::dnn::TextDetectionModel_EAST* cv_dnn_TextDetectionModel_EAST_implicitClone_const(const cv::dnn::TextDetectionModel_EAST* instance) {
			return new cv::dnn::TextDetectionModel_EAST(*instance);
	}
	
	cv::dnn::Model* cv_dnn_TextDetectionModel_EAST_to_Model(cv::dnn::TextDetectionModel_EAST* instance) {
			return dynamic_cast<cv::dnn::Model*>(instance);
	}
	
	cv::dnn::TextDetectionModel* cv_dnn_TextDetectionModel_EAST_to_TextDetectionModel(cv::dnn::TextDetectionModel_EAST* instance) {
			return dynamic_cast<cv::dnn::TextDetectionModel*>(instance);
	}
	
	void cv_dnn_TextDetectionModel_EAST_delete(cv::dnn::TextDetectionModel_EAST* instance) {
			delete instance;
	}
	
	void cv_dnn_TextRecognitionModel_TextRecognitionModel(Result<cv::dnn::TextRecognitionModel*>* ocvrs_return) {
		try {
			cv::dnn::TextRecognitionModel* ret = new cv::dnn::TextRecognitionModel();
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextRecognitionModel_TextRecognitionModel_const_NetR(const cv::dnn::Net* network, Result<cv::dnn::TextRecognitionModel*>* ocvrs_return) {
		try {
			cv::dnn::TextRecognitionModel* ret = new cv::dnn::TextRecognitionModel(*network);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextRecognitionModel_TextRecognitionModel_const_stringR_const_stringR(const char* model, const char* config, Result<cv::dnn::TextRecognitionModel*>* ocvrs_return) {
		try {
			cv::dnn::TextRecognitionModel* ret = new cv::dnn::TextRecognitionModel(std::string(model), std::string(config));
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextRecognitionModel_TextRecognitionModel_const_stringR(const char* model, Result<cv::dnn::TextRecognitionModel*>* ocvrs_return) {
		try {
			cv::dnn::TextRecognitionModel* ret = new cv::dnn::TextRecognitionModel(std::string(model));
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextRecognitionModel_setDecodeType_const_stringR(cv::dnn::TextRecognitionModel* instance, const char* decodeType, Result<cv::dnn::TextRecognitionModel*>* ocvrs_return) {
		try {
			cv::dnn::TextRecognitionModel ret = instance->setDecodeType(std::string(decodeType));
			Ok(new cv::dnn::TextRecognitionModel(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextRecognitionModel_getDecodeType_const(const cv::dnn::TextRecognitionModel* instance, Result<void*>* ocvrs_return) {
		try {
			const std::string ret = instance->getDecodeType();
			Ok(ocvrs_create_string(ret.c_str()), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextRecognitionModel_setDecodeOptsCTCPrefixBeamSearch_int_int(cv::dnn::TextRecognitionModel* instance, int beamSize, int vocPruneSize, Result<cv::dnn::TextRecognitionModel*>* ocvrs_return) {
		try {
			cv::dnn::TextRecognitionModel ret = instance->setDecodeOptsCTCPrefixBeamSearch(beamSize, vocPruneSize);
			Ok(new cv::dnn::TextRecognitionModel(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextRecognitionModel_setDecodeOptsCTCPrefixBeamSearch_int(cv::dnn::TextRecognitionModel* instance, int beamSize, Result<cv::dnn::TextRecognitionModel*>* ocvrs_return) {
		try {
			cv::dnn::TextRecognitionModel ret = instance->setDecodeOptsCTCPrefixBeamSearch(beamSize);
			Ok(new cv::dnn::TextRecognitionModel(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextRecognitionModel_setVocabulary_const_vectorLstringGR(cv::dnn::TextRecognitionModel* instance, const std::vector<std::string>* vocabulary, Result<cv::dnn::TextRecognitionModel*>* ocvrs_return) {
		try {
			cv::dnn::TextRecognitionModel ret = instance->setVocabulary(*vocabulary);
			Ok(new cv::dnn::TextRecognitionModel(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextRecognitionModel_getVocabulary_const(const cv::dnn::TextRecognitionModel* instance, Result<std::vector<std::string>*>* ocvrs_return) {
		try {
			const std::vector<std::string> ret = instance->getVocabulary();
			Ok(new const std::vector<std::string>(ret), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextRecognitionModel_recognize_const_const__InputArrayR(const cv::dnn::TextRecognitionModel* instance, const cv::_InputArray* frame, Result<void*>* ocvrs_return) {
		try {
			std::string ret = instance->recognize(*frame);
			Ok(ocvrs_create_string(ret.c_str()), ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn_TextRecognitionModel_recognize_const_const__InputArrayR_const__InputArrayR_vectorLstringGR(const cv::dnn::TextRecognitionModel* instance, const cv::_InputArray* frame, const cv::_InputArray* roiRects, std::vector<std::string>* results, ResultVoid* ocvrs_return) {
		try {
			instance->recognize(*frame, *roiRects, *results);
			Ok(ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::dnn::TextRecognitionModel* cv_dnn_TextRecognitionModel_implicitClone_const(const cv::dnn::TextRecognitionModel* instance) {
			return new cv::dnn::TextRecognitionModel(*instance);
	}
	
	cv::dnn::Model* cv_dnn_TextRecognitionModel_to_Model(cv::dnn::TextRecognitionModel* instance) {
			return dynamic_cast<cv::dnn::Model*>(instance);
	}
	
	void cv_dnn_TextRecognitionModel_delete(cv::dnn::TextRecognitionModel* instance) {
			delete instance;
	}
	
	void cv_dnn__Range__Range_const_RangeR(const cv::Range* r, Result<cv::dnn::_Range*>* ocvrs_return) {
		try {
			cv::dnn::_Range* ret = new cv::dnn::_Range(*r);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn__Range__Range_int_int(int start_, int size_, Result<cv::dnn::_Range*>* ocvrs_return) {
		try {
			cv::dnn::_Range* ret = new cv::dnn::_Range(start_, size_);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	void cv_dnn__Range__Range_int(int start_, Result<cv::dnn::_Range*>* ocvrs_return) {
		try {
			cv::dnn::_Range* ret = new cv::dnn::_Range(start_);
			Ok(ret, ocvrs_return);
		} OCVRS_CATCH(ocvrs_return);
	}
	
	cv::Range* cv_dnn__Range_to_Range(cv::dnn::_Range* instance) {
			return dynamic_cast<cv::Range*>(instance);
	}
	
	void cv_dnn__Range_delete(cv::dnn::_Range* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::AffineTransformer* cv_PtrLcv_AffineTransformerG_getInnerPtr_const(const cv::Ptr<cv::AffineTransformer>* instance) {
			return instance->get();
	}
	
	cv::AffineTransformer* cv_PtrLcv_AffineTransformerG_getInnerPtrMut(cv::Ptr<cv::AffineTransformer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_AffineTransformerG_delete(cv::Ptr<cv::AffineTransformer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_AffineTransformerG_to_PtrOfAlgorithm(cv::Ptr<cv::AffineTransformer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::ShapeTransformer>* cv_PtrLcv_AffineTransformerG_to_PtrOfShapeTransformer(cv::Ptr<cv::AffineTransformer>* instance) {
			return new cv::Ptr<cv::ShapeTransformer>(instance->dynamicCast<cv::ShapeTransformer>());
	}
	
}

extern "C" {
	const cv::ChiHistogramCostExtractor* cv_PtrLcv_ChiHistogramCostExtractorG_getInnerPtr_const(const cv::Ptr<cv::ChiHistogramCostExtractor>* instance) {
			return instance->get();
	}
	
	cv::ChiHistogramCostExtractor* cv_PtrLcv_ChiHistogramCostExtractorG_getInnerPtrMut(cv::Ptr<cv::ChiHistogramCostExtractor>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ChiHistogramCostExtractorG_delete(cv::Ptr<cv::ChiHistogramCostExtractor>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ChiHistogramCostExtractorG_to_PtrOfAlgorithm(cv::Ptr<cv::ChiHistogramCostExtractor>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::HistogramCostExtractor>* cv_PtrLcv_ChiHistogramCostExtractorG_to_PtrOfHistogramCostExtractor(cv::Ptr<cv::ChiHistogramCostExtractor>* instance) {
			return new cv::Ptr<cv::HistogramCostExtractor>(instance->dynamicCast<cv::HistogramCostExtractor>());
	}
	
}

extern "C" {
	const cv::EMDHistogramCostExtractor* cv_PtrLcv_EMDHistogramCostExtractorG_getInnerPtr_const(const cv::Ptr<cv::EMDHistogramCostExtractor>* instance) {
			return instance->get();
	}
	
	cv::EMDHistogramCostExtractor* cv_PtrLcv_EMDHistogramCostExtractorG_getInnerPtrMut(cv::Ptr<cv::EMDHistogramCostExtractor>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_EMDHistogramCostExtractorG_delete(cv::Ptr<cv::EMDHistogramCostExtractor>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_EMDHistogramCostExtractorG_to_PtrOfAlgorithm(cv::Ptr<cv::EMDHistogramCostExtractor>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::HistogramCostExtractor>* cv_PtrLcv_EMDHistogramCostExtractorG_to_PtrOfHistogramCostExtractor(cv::Ptr<cv::EMDHistogramCostExtractor>* instance) {
			return new cv::Ptr<cv::HistogramCostExtractor>(instance->dynamicCast<cv::HistogramCostExtractor>());
	}
	
}

extern "C" {
	const cv::EMDL1HistogramCostExtractor* cv_PtrLcv_EMDL1HistogramCostExtractorG_getInnerPtr_const(const cv::Ptr<cv::EMDL1HistogramCostExtractor>* instance) {
			return instance->get();
	}
	
	cv::EMDL1HistogramCostExtractor* cv_PtrLcv_EMDL1HistogramCostExtractorG_getInnerPtrMut(cv::Ptr<cv::EMDL1HistogramCostExtractor>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_EMDL1HistogramCostExtractorG_delete(cv::Ptr<cv::EMDL1HistogramCostExtractor>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_EMDL1HistogramCostExtractorG_to_PtrOfAlgorithm(cv::Ptr<cv::EMDL1HistogramCostExtractor>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::HistogramCostExtractor>* cv_PtrLcv_EMDL1HistogramCostExtractorG_to_PtrOfHistogramCostExtractor(cv::Ptr<cv::EMDL1HistogramCostExtractor>* instance) {
			return new cv::Ptr<cv::HistogramCostExtractor>(instance->dynamicCast<cv::HistogramCostExtractor>());
	}
	
}

extern "C" {
	const cv::HausdorffDistanceExtractor* cv_PtrLcv_HausdorffDistanceExtractorG_getInnerPtr_const(const cv::Ptr<cv::HausdorffDistanceExtractor>* instance) {
			return instance->get();
	}
	
	cv::HausdorffDistanceExtractor* cv_PtrLcv_HausdorffDistanceExtractorG_getInnerPtrMut(cv::Ptr<cv::HausdorffDistanceExtractor>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_HausdorffDistanceExtractorG_delete(cv::Ptr<cv::HausdorffDistanceExtractor>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_HausdorffDistanceExtractorG_to_PtrOfAlgorithm(cv::Ptr<cv::HausdorffDistanceExtractor>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::ShapeDistanceExtractor>* cv_PtrLcv_HausdorffDistanceExtractorG_to_PtrOfShapeDistanceExtractor(cv::Ptr<cv::HausdorffDistanceExtractor>* instance) {
			return new cv::Ptr<cv::ShapeDistanceExtractor>(instance->dynamicCast<cv::ShapeDistanceExtractor>());
	}
	
}

extern "C" {
	const cv::HistogramCostExtractor* cv_PtrLcv_HistogramCostExtractorG_getInnerPtr_const(const cv::Ptr<cv::HistogramCostExtractor>* instance) {
			return instance->get();
	}
	
	cv::HistogramCostExtractor* cv_PtrLcv_HistogramCostExtractorG_getInnerPtrMut(cv::Ptr<cv::HistogramCostExtractor>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_HistogramCostExtractorG_delete(cv::Ptr<cv::HistogramCostExtractor>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_HistogramCostExtractorG_to_PtrOfAlgorithm(cv::Ptr<cv::HistogramCostExtractor>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::NormHistogramCostExtractor* cv_PtrLcv_NormHistogramCostExtractorG_getInnerPtr_const(const cv::Ptr<cv::NormHistogramCostExtractor>* instance) {
			return instance->get();
	}
	
	cv::NormHistogramCostExtractor* cv_PtrLcv_NormHistogramCostExtractorG_getInnerPtrMut(cv::Ptr<cv::NormHistogramCostExtractor>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_NormHistogramCostExtractorG_delete(cv::Ptr<cv::NormHistogramCostExtractor>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_NormHistogramCostExtractorG_to_PtrOfAlgorithm(cv::Ptr<cv::NormHistogramCostExtractor>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::HistogramCostExtractor>* cv_PtrLcv_NormHistogramCostExtractorG_to_PtrOfHistogramCostExtractor(cv::Ptr<cv::NormHistogramCostExtractor>* instance) {
			return new cv::Ptr<cv::HistogramCostExtractor>(instance->dynamicCast<cv::HistogramCostExtractor>());
	}
	
}

extern "C" {
	const cv::ShapeContextDistanceExtractor* cv_PtrLcv_ShapeContextDistanceExtractorG_getInnerPtr_const(const cv::Ptr<cv::ShapeContextDistanceExtractor>* instance) {
			return instance->get();
	}
	
	cv::ShapeContextDistanceExtractor* cv_PtrLcv_ShapeContextDistanceExtractorG_getInnerPtrMut(cv::Ptr<cv::ShapeContextDistanceExtractor>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ShapeContextDistanceExtractorG_delete(cv::Ptr<cv::ShapeContextDistanceExtractor>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ShapeContextDistanceExtractorG_to_PtrOfAlgorithm(cv::Ptr<cv::ShapeContextDistanceExtractor>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::ShapeDistanceExtractor>* cv_PtrLcv_ShapeContextDistanceExtractorG_to_PtrOfShapeDistanceExtractor(cv::Ptr<cv::ShapeContextDistanceExtractor>* instance) {
			return new cv::Ptr<cv::ShapeDistanceExtractor>(instance->dynamicCast<cv::ShapeDistanceExtractor>());
	}
	
}

extern "C" {
	const cv::ShapeDistanceExtractor* cv_PtrLcv_ShapeDistanceExtractorG_getInnerPtr_const(const cv::Ptr<cv::ShapeDistanceExtractor>* instance) {
			return instance->get();
	}
	
	cv::ShapeDistanceExtractor* cv_PtrLcv_ShapeDistanceExtractorG_getInnerPtrMut(cv::Ptr<cv::ShapeDistanceExtractor>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ShapeDistanceExtractorG_delete(cv::Ptr<cv::ShapeDistanceExtractor>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ShapeDistanceExtractorG_to_PtrOfAlgorithm(cv::Ptr<cv::ShapeDistanceExtractor>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::ShapeTransformer* cv_PtrLcv_ShapeTransformerG_getInnerPtr_const(const cv::Ptr<cv::ShapeTransformer>* instance) {
			return instance->get();
	}
	
	cv::ShapeTransformer* cv_PtrLcv_ShapeTransformerG_getInnerPtrMut(cv::Ptr<cv::ShapeTransformer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ShapeTransformerG_delete(cv::Ptr<cv::ShapeTransformer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ShapeTransformerG_to_PtrOfAlgorithm(cv::Ptr<cv::ShapeTransformer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::ThinPlateSplineShapeTransformer* cv_PtrLcv_ThinPlateSplineShapeTransformerG_getInnerPtr_const(const cv::Ptr<cv::ThinPlateSplineShapeTransformer>* instance) {
			return instance->get();
	}
	
	cv::ThinPlateSplineShapeTransformer* cv_PtrLcv_ThinPlateSplineShapeTransformerG_getInnerPtrMut(cv::Ptr<cv::ThinPlateSplineShapeTransformer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_ThinPlateSplineShapeTransformerG_delete(cv::Ptr<cv::ThinPlateSplineShapeTransformer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_ThinPlateSplineShapeTransformerG_to_PtrOfAlgorithm(cv::Ptr<cv::ThinPlateSplineShapeTransformer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::ShapeTransformer>* cv_PtrLcv_ThinPlateSplineShapeTransformerG_to_PtrOfShapeTransformer(cv::Ptr<cv::ThinPlateSplineShapeTransformer>* instance) {
			return new cv::Ptr<cv::ShapeTransformer>(instance->dynamicCast<cv::ShapeTransformer>());
	}
	
}


extern "C" {
	const cv::optflow::DenseRLOFOpticalFlow* cv_PtrLcv_optflow_DenseRLOFOpticalFlowG_getInnerPtr_const(const cv::Ptr<cv::optflow::DenseRLOFOpticalFlow>* instance) {
			return instance->get();
	}
	
	cv::optflow::DenseRLOFOpticalFlow* cv_PtrLcv_optflow_DenseRLOFOpticalFlowG_getInnerPtrMut(cv::Ptr<cv::optflow::DenseRLOFOpticalFlow>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_optflow_DenseRLOFOpticalFlowG_delete(cv::Ptr<cv::optflow::DenseRLOFOpticalFlow>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_optflow_DenseRLOFOpticalFlowG_to_PtrOfAlgorithm(cv::Ptr<cv::optflow::DenseRLOFOpticalFlow>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::DenseOpticalFlow>* cv_PtrLcv_optflow_DenseRLOFOpticalFlowG_to_PtrOfDenseOpticalFlow(cv::Ptr<cv::optflow::DenseRLOFOpticalFlow>* instance) {
			return new cv::Ptr<cv::DenseOpticalFlow>(instance->dynamicCast<cv::DenseOpticalFlow>());
	}
	
}

extern "C" {
	const cv::optflow::DualTVL1OpticalFlow* cv_PtrLcv_optflow_DualTVL1OpticalFlowG_getInnerPtr_const(const cv::Ptr<cv::optflow::DualTVL1OpticalFlow>* instance) {
			return instance->get();
	}
	
	cv::optflow::DualTVL1OpticalFlow* cv_PtrLcv_optflow_DualTVL1OpticalFlowG_getInnerPtrMut(cv::Ptr<cv::optflow::DualTVL1OpticalFlow>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_optflow_DualTVL1OpticalFlowG_delete(cv::Ptr<cv::optflow::DualTVL1OpticalFlow>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_optflow_DualTVL1OpticalFlowG_to_PtrOfAlgorithm(cv::Ptr<cv::optflow::DualTVL1OpticalFlow>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::DenseOpticalFlow>* cv_PtrLcv_optflow_DualTVL1OpticalFlowG_to_PtrOfDenseOpticalFlow(cv::Ptr<cv::optflow::DualTVL1OpticalFlow>* instance) {
			return new cv::Ptr<cv::DenseOpticalFlow>(instance->dynamicCast<cv::DenseOpticalFlow>());
	}
	
}

extern "C" {
	const cv::optflow::GPCTrainingSamples* cv_PtrLcv_optflow_GPCTrainingSamplesG_getInnerPtr_const(const cv::Ptr<cv::optflow::GPCTrainingSamples>* instance) {
			return instance->get();
	}
	
	cv::optflow::GPCTrainingSamples* cv_PtrLcv_optflow_GPCTrainingSamplesG_getInnerPtrMut(cv::Ptr<cv::optflow::GPCTrainingSamples>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_optflow_GPCTrainingSamplesG_delete(cv::Ptr<cv::optflow::GPCTrainingSamples>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::optflow::GPCTrainingSamples>* cv_PtrLcv_optflow_GPCTrainingSamplesG_new_const_GPCTrainingSamples(cv::optflow::GPCTrainingSamples* val) {
			return new cv::Ptr<cv::optflow::GPCTrainingSamples>(val);
	}
	
}

extern "C" {
	const cv::optflow::GPCTree* cv_PtrLcv_optflow_GPCTreeG_getInnerPtr_const(const cv::Ptr<cv::optflow::GPCTree>* instance) {
			return instance->get();
	}
	
	cv::optflow::GPCTree* cv_PtrLcv_optflow_GPCTreeG_getInnerPtrMut(cv::Ptr<cv::optflow::GPCTree>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_optflow_GPCTreeG_delete(cv::Ptr<cv::optflow::GPCTree>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_optflow_GPCTreeG_to_PtrOfAlgorithm(cv::Ptr<cv::optflow::GPCTree>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::optflow::GPCTree>* cv_PtrLcv_optflow_GPCTreeG_new_const_GPCTree(cv::optflow::GPCTree* val) {
			return new cv::Ptr<cv::optflow::GPCTree>(val);
	}
	
}

extern "C" {
	const cv::optflow::OpticalFlowPCAFlow* cv_PtrLcv_optflow_OpticalFlowPCAFlowG_getInnerPtr_const(const cv::Ptr<cv::optflow::OpticalFlowPCAFlow>* instance) {
			return instance->get();
	}
	
	cv::optflow::OpticalFlowPCAFlow* cv_PtrLcv_optflow_OpticalFlowPCAFlowG_getInnerPtrMut(cv::Ptr<cv::optflow::OpticalFlowPCAFlow>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_optflow_OpticalFlowPCAFlowG_delete(cv::Ptr<cv::optflow::OpticalFlowPCAFlow>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_optflow_OpticalFlowPCAFlowG_to_PtrOfAlgorithm(cv::Ptr<cv::optflow::OpticalFlowPCAFlow>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::DenseOpticalFlow>* cv_PtrLcv_optflow_OpticalFlowPCAFlowG_to_PtrOfDenseOpticalFlow(cv::Ptr<cv::optflow::OpticalFlowPCAFlow>* instance) {
			return new cv::Ptr<cv::DenseOpticalFlow>(instance->dynamicCast<cv::DenseOpticalFlow>());
	}
	
	cv::Ptr<cv::optflow::OpticalFlowPCAFlow>* cv_PtrLcv_optflow_OpticalFlowPCAFlowG_new_const_OpticalFlowPCAFlow(cv::optflow::OpticalFlowPCAFlow* val) {
			return new cv::Ptr<cv::optflow::OpticalFlowPCAFlow>(val);
	}
	
}

extern "C" {
	const cv::optflow::PCAPrior* cv_PtrLcv_optflow_PCAPriorG_getInnerPtr_const(const cv::Ptr<cv::optflow::PCAPrior>* instance) {
			return instance->get();
	}
	
	cv::optflow::PCAPrior* cv_PtrLcv_optflow_PCAPriorG_getInnerPtrMut(cv::Ptr<cv::optflow::PCAPrior>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_optflow_PCAPriorG_delete(cv::Ptr<cv::optflow::PCAPrior>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::optflow::PCAPrior>* cv_PtrLcv_optflow_PCAPriorG_new_const_PCAPrior(cv::optflow::PCAPrior* val) {
			return new cv::Ptr<cv::optflow::PCAPrior>(val);
	}
	
}

extern "C" {
	const cv::optflow::RLOFOpticalFlowParameter* cv_PtrLcv_optflow_RLOFOpticalFlowParameterG_getInnerPtr_const(const cv::Ptr<cv::optflow::RLOFOpticalFlowParameter>* instance) {
			return instance->get();
	}
	
	cv::optflow::RLOFOpticalFlowParameter* cv_PtrLcv_optflow_RLOFOpticalFlowParameterG_getInnerPtrMut(cv::Ptr<cv::optflow::RLOFOpticalFlowParameter>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_optflow_RLOFOpticalFlowParameterG_delete(cv::Ptr<cv::optflow::RLOFOpticalFlowParameter>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::optflow::RLOFOpticalFlowParameter>* cv_PtrLcv_optflow_RLOFOpticalFlowParameterG_new_const_RLOFOpticalFlowParameter(cv::optflow::RLOFOpticalFlowParameter* val) {
			return new cv::Ptr<cv::optflow::RLOFOpticalFlowParameter>(val);
	}
	
}

extern "C" {
	const cv::optflow::SparseRLOFOpticalFlow* cv_PtrLcv_optflow_SparseRLOFOpticalFlowG_getInnerPtr_const(const cv::Ptr<cv::optflow::SparseRLOFOpticalFlow>* instance) {
			return instance->get();
	}
	
	cv::optflow::SparseRLOFOpticalFlow* cv_PtrLcv_optflow_SparseRLOFOpticalFlowG_getInnerPtrMut(cv::Ptr<cv::optflow::SparseRLOFOpticalFlow>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_optflow_SparseRLOFOpticalFlowG_delete(cv::Ptr<cv::optflow::SparseRLOFOpticalFlow>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_optflow_SparseRLOFOpticalFlowG_to_PtrOfAlgorithm(cv::Ptr<cv::optflow::SparseRLOFOpticalFlow>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::SparseOpticalFlow>* cv_PtrLcv_optflow_SparseRLOFOpticalFlowG_to_PtrOfSparseOpticalFlow(cv::Ptr<cv::optflow::SparseRLOFOpticalFlow>* instance) {
			return new cv::Ptr<cv::SparseOpticalFlow>(instance->dynamicCast<cv::SparseOpticalFlow>());
	}
	
}

extern "C" {
	std::vector<cv::optflow::GPCPatchDescriptor>* std_vectorLcv_optflow_GPCPatchDescriptorG_new_const() {
			std::vector<cv::optflow::GPCPatchDescriptor>* ret = new std::vector<cv::optflow::GPCPatchDescriptor>();
			return ret;
	}
	
	void std_vectorLcv_optflow_GPCPatchDescriptorG_delete(std::vector<cv::optflow::GPCPatchDescriptor>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_optflow_GPCPatchDescriptorG_len_const(const std::vector<cv::optflow::GPCPatchDescriptor>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_optflow_GPCPatchDescriptorG_isEmpty_const(const std::vector<cv::optflow::GPCPatchDescriptor>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_optflow_GPCPatchDescriptorG_capacity_const(const std::vector<cv::optflow::GPCPatchDescriptor>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_optflow_GPCPatchDescriptorG_shrinkToFit(std::vector<cv::optflow::GPCPatchDescriptor>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_optflow_GPCPatchDescriptorG_reserve_size_t(std::vector<cv::optflow::GPCPatchDescriptor>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_optflow_GPCPatchDescriptorG_remove_size_t(std::vector<cv::optflow::GPCPatchDescriptor>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_optflow_GPCPatchDescriptorG_swap_size_t_size_t(std::vector<cv::optflow::GPCPatchDescriptor>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_optflow_GPCPatchDescriptorG_clear(std::vector<cv::optflow::GPCPatchDescriptor>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_optflow_GPCPatchDescriptorG_push_const_GPCPatchDescriptor(std::vector<cv::optflow::GPCPatchDescriptor>* instance, const cv::optflow::GPCPatchDescriptor* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_optflow_GPCPatchDescriptorG_insert_size_t_const_GPCPatchDescriptor(std::vector<cv::optflow::GPCPatchDescriptor>* instance, size_t index, const cv::optflow::GPCPatchDescriptor* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_optflow_GPCPatchDescriptorG_get_const_size_t(const std::vector<cv::optflow::GPCPatchDescriptor>* instance, size_t index, cv::optflow::GPCPatchDescriptor** ocvrs_return) {
			cv::optflow::GPCPatchDescriptor ret = (*instance)[index];
			*ocvrs_return = new cv::optflow::GPCPatchDescriptor(ret);
	}
	
	void std_vectorLcv_optflow_GPCPatchDescriptorG_set_size_t_const_GPCPatchDescriptor(std::vector<cv::optflow::GPCPatchDescriptor>* instance, size_t index, const cv::optflow::GPCPatchDescriptor* val) {
			(*instance)[index] = *val;
	}
	
}


extern "C" {
	std::vector<cv::optflow::GPCPatchSample>* std_vectorLcv_optflow_GPCPatchSampleG_new_const() {
			std::vector<cv::optflow::GPCPatchSample>* ret = new std::vector<cv::optflow::GPCPatchSample>();
			return ret;
	}
	
	void std_vectorLcv_optflow_GPCPatchSampleG_delete(std::vector<cv::optflow::GPCPatchSample>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_optflow_GPCPatchSampleG_len_const(const std::vector<cv::optflow::GPCPatchSample>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_optflow_GPCPatchSampleG_isEmpty_const(const std::vector<cv::optflow::GPCPatchSample>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_optflow_GPCPatchSampleG_capacity_const(const std::vector<cv::optflow::GPCPatchSample>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_optflow_GPCPatchSampleG_shrinkToFit(std::vector<cv::optflow::GPCPatchSample>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_optflow_GPCPatchSampleG_reserve_size_t(std::vector<cv::optflow::GPCPatchSample>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_optflow_GPCPatchSampleG_remove_size_t(std::vector<cv::optflow::GPCPatchSample>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_optflow_GPCPatchSampleG_swap_size_t_size_t(std::vector<cv::optflow::GPCPatchSample>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_optflow_GPCPatchSampleG_clear(std::vector<cv::optflow::GPCPatchSample>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_optflow_GPCPatchSampleG_push_const_GPCPatchSample(std::vector<cv::optflow::GPCPatchSample>* instance, const cv::optflow::GPCPatchSample* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_optflow_GPCPatchSampleG_insert_size_t_const_GPCPatchSample(std::vector<cv::optflow::GPCPatchSample>* instance, size_t index, const cv::optflow::GPCPatchSample* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_optflow_GPCPatchSampleG_get_const_size_t(const std::vector<cv::optflow::GPCPatchSample>* instance, size_t index, cv::optflow::GPCPatchSample** ocvrs_return) {
			cv::optflow::GPCPatchSample ret = (*instance)[index];
			*ocvrs_return = new cv::optflow::GPCPatchSample(ret);
	}
	
	void std_vectorLcv_optflow_GPCPatchSampleG_set_size_t_const_GPCPatchSample(std::vector<cv::optflow::GPCPatchSample>* instance, size_t index, const cv::optflow::GPCPatchSample* val) {
			(*instance)[index] = *val;
	}
	
}



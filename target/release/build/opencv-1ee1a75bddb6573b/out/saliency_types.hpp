extern "C" {
	const cv::saliency::MotionSaliency* cv_PtrLcv_saliency_MotionSaliencyG_getInnerPtr_const(const cv::Ptr<cv::saliency::MotionSaliency>* instance) {
			return instance->get();
	}
	
	cv::saliency::MotionSaliency* cv_PtrLcv_saliency_MotionSaliencyG_getInnerPtrMut(cv::Ptr<cv::saliency::MotionSaliency>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_saliency_MotionSaliencyG_delete(cv::Ptr<cv::saliency::MotionSaliency>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_saliency_MotionSaliencyG_to_PtrOfAlgorithm(cv::Ptr<cv::saliency::MotionSaliency>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::saliency::Saliency>* cv_PtrLcv_saliency_MotionSaliencyG_to_PtrOfSaliency(cv::Ptr<cv::saliency::MotionSaliency>* instance) {
			return new cv::Ptr<cv::saliency::Saliency>(instance->dynamicCast<cv::saliency::Saliency>());
	}
	
}

extern "C" {
	const cv::saliency::MotionSaliencyBinWangApr2014* cv_PtrLcv_saliency_MotionSaliencyBinWangApr2014G_getInnerPtr_const(const cv::Ptr<cv::saliency::MotionSaliencyBinWangApr2014>* instance) {
			return instance->get();
	}
	
	cv::saliency::MotionSaliencyBinWangApr2014* cv_PtrLcv_saliency_MotionSaliencyBinWangApr2014G_getInnerPtrMut(cv::Ptr<cv::saliency::MotionSaliencyBinWangApr2014>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_saliency_MotionSaliencyBinWangApr2014G_delete(cv::Ptr<cv::saliency::MotionSaliencyBinWangApr2014>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_saliency_MotionSaliencyBinWangApr2014G_to_PtrOfAlgorithm(cv::Ptr<cv::saliency::MotionSaliencyBinWangApr2014>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::saliency::MotionSaliency>* cv_PtrLcv_saliency_MotionSaliencyBinWangApr2014G_to_PtrOfMotionSaliency(cv::Ptr<cv::saliency::MotionSaliencyBinWangApr2014>* instance) {
			return new cv::Ptr<cv::saliency::MotionSaliency>(instance->dynamicCast<cv::saliency::MotionSaliency>());
	}
	
	cv::Ptr<cv::saliency::Saliency>* cv_PtrLcv_saliency_MotionSaliencyBinWangApr2014G_to_PtrOfSaliency(cv::Ptr<cv::saliency::MotionSaliencyBinWangApr2014>* instance) {
			return new cv::Ptr<cv::saliency::Saliency>(instance->dynamicCast<cv::saliency::Saliency>());
	}
	
	cv::Ptr<cv::saliency::MotionSaliencyBinWangApr2014>* cv_PtrLcv_saliency_MotionSaliencyBinWangApr2014G_new_const_MotionSaliencyBinWangApr2014(cv::saliency::MotionSaliencyBinWangApr2014* val) {
			return new cv::Ptr<cv::saliency::MotionSaliencyBinWangApr2014>(val);
	}
	
}

extern "C" {
	const cv::saliency::Objectness* cv_PtrLcv_saliency_ObjectnessG_getInnerPtr_const(const cv::Ptr<cv::saliency::Objectness>* instance) {
			return instance->get();
	}
	
	cv::saliency::Objectness* cv_PtrLcv_saliency_ObjectnessG_getInnerPtrMut(cv::Ptr<cv::saliency::Objectness>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_saliency_ObjectnessG_delete(cv::Ptr<cv::saliency::Objectness>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_saliency_ObjectnessG_to_PtrOfAlgorithm(cv::Ptr<cv::saliency::Objectness>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::saliency::Saliency>* cv_PtrLcv_saliency_ObjectnessG_to_PtrOfSaliency(cv::Ptr<cv::saliency::Objectness>* instance) {
			return new cv::Ptr<cv::saliency::Saliency>(instance->dynamicCast<cv::saliency::Saliency>());
	}
	
}

extern "C" {
	const cv::saliency::ObjectnessBING* cv_PtrLcv_saliency_ObjectnessBINGG_getInnerPtr_const(const cv::Ptr<cv::saliency::ObjectnessBING>* instance) {
			return instance->get();
	}
	
	cv::saliency::ObjectnessBING* cv_PtrLcv_saliency_ObjectnessBINGG_getInnerPtrMut(cv::Ptr<cv::saliency::ObjectnessBING>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_saliency_ObjectnessBINGG_delete(cv::Ptr<cv::saliency::ObjectnessBING>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_saliency_ObjectnessBINGG_to_PtrOfAlgorithm(cv::Ptr<cv::saliency::ObjectnessBING>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::saliency::Objectness>* cv_PtrLcv_saliency_ObjectnessBINGG_to_PtrOfObjectness(cv::Ptr<cv::saliency::ObjectnessBING>* instance) {
			return new cv::Ptr<cv::saliency::Objectness>(instance->dynamicCast<cv::saliency::Objectness>());
	}
	
	cv::Ptr<cv::saliency::Saliency>* cv_PtrLcv_saliency_ObjectnessBINGG_to_PtrOfSaliency(cv::Ptr<cv::saliency::ObjectnessBING>* instance) {
			return new cv::Ptr<cv::saliency::Saliency>(instance->dynamicCast<cv::saliency::Saliency>());
	}
	
	cv::Ptr<cv::saliency::ObjectnessBING>* cv_PtrLcv_saliency_ObjectnessBINGG_new_const_ObjectnessBING(cv::saliency::ObjectnessBING* val) {
			return new cv::Ptr<cv::saliency::ObjectnessBING>(val);
	}
	
}

extern "C" {
	const cv::saliency::Saliency* cv_PtrLcv_saliency_SaliencyG_getInnerPtr_const(const cv::Ptr<cv::saliency::Saliency>* instance) {
			return instance->get();
	}
	
	cv::saliency::Saliency* cv_PtrLcv_saliency_SaliencyG_getInnerPtrMut(cv::Ptr<cv::saliency::Saliency>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_saliency_SaliencyG_delete(cv::Ptr<cv::saliency::Saliency>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_saliency_SaliencyG_to_PtrOfAlgorithm(cv::Ptr<cv::saliency::Saliency>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::saliency::StaticSaliency* cv_PtrLcv_saliency_StaticSaliencyG_getInnerPtr_const(const cv::Ptr<cv::saliency::StaticSaliency>* instance) {
			return instance->get();
	}
	
	cv::saliency::StaticSaliency* cv_PtrLcv_saliency_StaticSaliencyG_getInnerPtrMut(cv::Ptr<cv::saliency::StaticSaliency>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_saliency_StaticSaliencyG_delete(cv::Ptr<cv::saliency::StaticSaliency>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_saliency_StaticSaliencyG_to_PtrOfAlgorithm(cv::Ptr<cv::saliency::StaticSaliency>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::saliency::Saliency>* cv_PtrLcv_saliency_StaticSaliencyG_to_PtrOfSaliency(cv::Ptr<cv::saliency::StaticSaliency>* instance) {
			return new cv::Ptr<cv::saliency::Saliency>(instance->dynamicCast<cv::saliency::Saliency>());
	}
	
}

extern "C" {
	const cv::saliency::StaticSaliencyFineGrained* cv_PtrLcv_saliency_StaticSaliencyFineGrainedG_getInnerPtr_const(const cv::Ptr<cv::saliency::StaticSaliencyFineGrained>* instance) {
			return instance->get();
	}
	
	cv::saliency::StaticSaliencyFineGrained* cv_PtrLcv_saliency_StaticSaliencyFineGrainedG_getInnerPtrMut(cv::Ptr<cv::saliency::StaticSaliencyFineGrained>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_saliency_StaticSaliencyFineGrainedG_delete(cv::Ptr<cv::saliency::StaticSaliencyFineGrained>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_saliency_StaticSaliencyFineGrainedG_to_PtrOfAlgorithm(cv::Ptr<cv::saliency::StaticSaliencyFineGrained>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::saliency::Saliency>* cv_PtrLcv_saliency_StaticSaliencyFineGrainedG_to_PtrOfSaliency(cv::Ptr<cv::saliency::StaticSaliencyFineGrained>* instance) {
			return new cv::Ptr<cv::saliency::Saliency>(instance->dynamicCast<cv::saliency::Saliency>());
	}
	
	cv::Ptr<cv::saliency::StaticSaliency>* cv_PtrLcv_saliency_StaticSaliencyFineGrainedG_to_PtrOfStaticSaliency(cv::Ptr<cv::saliency::StaticSaliencyFineGrained>* instance) {
			return new cv::Ptr<cv::saliency::StaticSaliency>(instance->dynamicCast<cv::saliency::StaticSaliency>());
	}
	
	cv::Ptr<cv::saliency::StaticSaliencyFineGrained>* cv_PtrLcv_saliency_StaticSaliencyFineGrainedG_new_const_StaticSaliencyFineGrained(cv::saliency::StaticSaliencyFineGrained* val) {
			return new cv::Ptr<cv::saliency::StaticSaliencyFineGrained>(val);
	}
	
}

extern "C" {
	const cv::saliency::StaticSaliencySpectralResidual* cv_PtrLcv_saliency_StaticSaliencySpectralResidualG_getInnerPtr_const(const cv::Ptr<cv::saliency::StaticSaliencySpectralResidual>* instance) {
			return instance->get();
	}
	
	cv::saliency::StaticSaliencySpectralResidual* cv_PtrLcv_saliency_StaticSaliencySpectralResidualG_getInnerPtrMut(cv::Ptr<cv::saliency::StaticSaliencySpectralResidual>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_saliency_StaticSaliencySpectralResidualG_delete(cv::Ptr<cv::saliency::StaticSaliencySpectralResidual>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_saliency_StaticSaliencySpectralResidualG_to_PtrOfAlgorithm(cv::Ptr<cv::saliency::StaticSaliencySpectralResidual>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::saliency::Saliency>* cv_PtrLcv_saliency_StaticSaliencySpectralResidualG_to_PtrOfSaliency(cv::Ptr<cv::saliency::StaticSaliencySpectralResidual>* instance) {
			return new cv::Ptr<cv::saliency::Saliency>(instance->dynamicCast<cv::saliency::Saliency>());
	}
	
	cv::Ptr<cv::saliency::StaticSaliency>* cv_PtrLcv_saliency_StaticSaliencySpectralResidualG_to_PtrOfStaticSaliency(cv::Ptr<cv::saliency::StaticSaliencySpectralResidual>* instance) {
			return new cv::Ptr<cv::saliency::StaticSaliency>(instance->dynamicCast<cv::saliency::StaticSaliency>());
	}
	
	cv::Ptr<cv::saliency::StaticSaliencySpectralResidual>* cv_PtrLcv_saliency_StaticSaliencySpectralResidualG_new_const_StaticSaliencySpectralResidual(cv::saliency::StaticSaliencySpectralResidual* val) {
			return new cv::Ptr<cv::saliency::StaticSaliencySpectralResidual>(val);
	}
	
}


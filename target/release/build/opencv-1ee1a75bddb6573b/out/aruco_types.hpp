extern "C" {
	const cv::aruco::Board* cv_PtrLcv_aruco_BoardG_getInnerPtr_const(const cv::Ptr<cv::aruco::Board>* instance) {
			return instance->get();
	}
	
	cv::aruco::Board* cv_PtrLcv_aruco_BoardG_getInnerPtrMut(cv::Ptr<cv::aruco::Board>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_aruco_BoardG_delete(cv::Ptr<cv::aruco::Board>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::aruco::Board>* cv_PtrLcv_aruco_BoardG_new_const_Board(cv::aruco::Board* val) {
			return new cv::Ptr<cv::aruco::Board>(val);
	}
	
}

extern "C" {
	const cv::aruco::CharucoBoard* cv_PtrLcv_aruco_CharucoBoardG_getInnerPtr_const(const cv::Ptr<cv::aruco::CharucoBoard>* instance) {
			return instance->get();
	}
	
	cv::aruco::CharucoBoard* cv_PtrLcv_aruco_CharucoBoardG_getInnerPtrMut(cv::Ptr<cv::aruco::CharucoBoard>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_aruco_CharucoBoardG_delete(cv::Ptr<cv::aruco::CharucoBoard>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::aruco::Board>* cv_PtrLcv_aruco_CharucoBoardG_to_PtrOfBoard(cv::Ptr<cv::aruco::CharucoBoard>* instance) {
			return new cv::Ptr<cv::aruco::Board>(instance->dynamicCast<cv::aruco::Board>());
	}
	
	cv::Ptr<cv::aruco::CharucoBoard>* cv_PtrLcv_aruco_CharucoBoardG_new_const_CharucoBoard(cv::aruco::CharucoBoard* val) {
			return new cv::Ptr<cv::aruco::CharucoBoard>(val);
	}
	
}

extern "C" {
	const cv::aruco::DetectorParameters* cv_PtrLcv_aruco_DetectorParametersG_getInnerPtr_const(const cv::Ptr<cv::aruco::DetectorParameters>* instance) {
			return instance->get();
	}
	
	cv::aruco::DetectorParameters* cv_PtrLcv_aruco_DetectorParametersG_getInnerPtrMut(cv::Ptr<cv::aruco::DetectorParameters>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_aruco_DetectorParametersG_delete(cv::Ptr<cv::aruco::DetectorParameters>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::aruco::DetectorParameters>* cv_PtrLcv_aruco_DetectorParametersG_new_const_DetectorParameters(cv::aruco::DetectorParameters* val) {
			return new cv::Ptr<cv::aruco::DetectorParameters>(val);
	}
	
}

extern "C" {
	const cv::aruco::Dictionary* cv_PtrLcv_aruco_DictionaryG_getInnerPtr_const(const cv::Ptr<cv::aruco::Dictionary>* instance) {
			return instance->get();
	}
	
	cv::aruco::Dictionary* cv_PtrLcv_aruco_DictionaryG_getInnerPtrMut(cv::Ptr<cv::aruco::Dictionary>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_aruco_DictionaryG_delete(cv::Ptr<cv::aruco::Dictionary>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::aruco::Dictionary>* cv_PtrLcv_aruco_DictionaryG_new_const_Dictionary(cv::aruco::Dictionary* val) {
			return new cv::Ptr<cv::aruco::Dictionary>(val);
	}
	
}

extern "C" {
	const cv::aruco::GridBoard* cv_PtrLcv_aruco_GridBoardG_getInnerPtr_const(const cv::Ptr<cv::aruco::GridBoard>* instance) {
			return instance->get();
	}
	
	cv::aruco::GridBoard* cv_PtrLcv_aruco_GridBoardG_getInnerPtrMut(cv::Ptr<cv::aruco::GridBoard>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_aruco_GridBoardG_delete(cv::Ptr<cv::aruco::GridBoard>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::aruco::Board>* cv_PtrLcv_aruco_GridBoardG_to_PtrOfBoard(cv::Ptr<cv::aruco::GridBoard>* instance) {
			return new cv::Ptr<cv::aruco::Board>(instance->dynamicCast<cv::aruco::Board>());
	}
	
	cv::Ptr<cv::aruco::GridBoard>* cv_PtrLcv_aruco_GridBoardG_new_const_GridBoard(cv::aruco::GridBoard* val) {
			return new cv::Ptr<cv::aruco::GridBoard>(val);
	}
	
}


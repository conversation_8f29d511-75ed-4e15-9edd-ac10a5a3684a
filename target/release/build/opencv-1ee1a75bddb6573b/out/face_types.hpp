extern "C" {
	const cv::face::BIF* cv_PtrLcv_face_BIFG_getInnerPtr_const(const cv::Ptr<cv::face::BIF>* instance) {
			return instance->get();
	}
	
	cv::face::BIF* cv_PtrLcv_face_BIFG_getInnerPtrMut(cv::Ptr<cv::face::BIF>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_face_BIFG_delete(cv::Ptr<cv::face::BIF>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_face_BIFG_to_PtrOfAlgorithm(cv::Ptr<cv::face::BIF>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::face::BasicFaceRecognizer* cv_PtrLcv_face_BasicFaceRecognizerG_getInnerPtr_const(const cv::Ptr<cv::face::BasicFaceRecognizer>* instance) {
			return instance->get();
	}
	
	cv::face::BasicFaceRecognizer* cv_PtrLcv_face_BasicFaceRecognizerG_getInnerPtrMut(cv::Ptr<cv::face::BasicFaceRecognizer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_face_BasicFaceRecognizerG_delete(cv::Ptr<cv::face::BasicFaceRecognizer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_face_BasicFaceRecognizerG_to_PtrOfAlgorithm(cv::Ptr<cv::face::BasicFaceRecognizer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::face::FaceRecognizer>* cv_PtrLcv_face_BasicFaceRecognizerG_to_PtrOfFaceRecognizer(cv::Ptr<cv::face::BasicFaceRecognizer>* instance) {
			return new cv::Ptr<cv::face::FaceRecognizer>(instance->dynamicCast<cv::face::FaceRecognizer>());
	}
	
}

extern "C" {
	const cv::face::EigenFaceRecognizer* cv_PtrLcv_face_EigenFaceRecognizerG_getInnerPtr_const(const cv::Ptr<cv::face::EigenFaceRecognizer>* instance) {
			return instance->get();
	}
	
	cv::face::EigenFaceRecognizer* cv_PtrLcv_face_EigenFaceRecognizerG_getInnerPtrMut(cv::Ptr<cv::face::EigenFaceRecognizer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_face_EigenFaceRecognizerG_delete(cv::Ptr<cv::face::EigenFaceRecognizer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_face_EigenFaceRecognizerG_to_PtrOfAlgorithm(cv::Ptr<cv::face::EigenFaceRecognizer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::face::BasicFaceRecognizer>* cv_PtrLcv_face_EigenFaceRecognizerG_to_PtrOfBasicFaceRecognizer(cv::Ptr<cv::face::EigenFaceRecognizer>* instance) {
			return new cv::Ptr<cv::face::BasicFaceRecognizer>(instance->dynamicCast<cv::face::BasicFaceRecognizer>());
	}
	
	cv::Ptr<cv::face::FaceRecognizer>* cv_PtrLcv_face_EigenFaceRecognizerG_to_PtrOfFaceRecognizer(cv::Ptr<cv::face::EigenFaceRecognizer>* instance) {
			return new cv::Ptr<cv::face::FaceRecognizer>(instance->dynamicCast<cv::face::FaceRecognizer>());
	}
	
}

extern "C" {
	const cv::face::FaceRecognizer* cv_PtrLcv_face_FaceRecognizerG_getInnerPtr_const(const cv::Ptr<cv::face::FaceRecognizer>* instance) {
			return instance->get();
	}
	
	cv::face::FaceRecognizer* cv_PtrLcv_face_FaceRecognizerG_getInnerPtrMut(cv::Ptr<cv::face::FaceRecognizer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_face_FaceRecognizerG_delete(cv::Ptr<cv::face::FaceRecognizer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_face_FaceRecognizerG_to_PtrOfAlgorithm(cv::Ptr<cv::face::FaceRecognizer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::face::Facemark* cv_PtrLcv_face_FacemarkG_getInnerPtr_const(const cv::Ptr<cv::face::Facemark>* instance) {
			return instance->get();
	}
	
	cv::face::Facemark* cv_PtrLcv_face_FacemarkG_getInnerPtrMut(cv::Ptr<cv::face::Facemark>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_face_FacemarkG_delete(cv::Ptr<cv::face::Facemark>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_face_FacemarkG_to_PtrOfAlgorithm(cv::Ptr<cv::face::Facemark>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::face::FacemarkAAM* cv_PtrLcv_face_FacemarkAAMG_getInnerPtr_const(const cv::Ptr<cv::face::FacemarkAAM>* instance) {
			return instance->get();
	}
	
	cv::face::FacemarkAAM* cv_PtrLcv_face_FacemarkAAMG_getInnerPtrMut(cv::Ptr<cv::face::FacemarkAAM>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_face_FacemarkAAMG_delete(cv::Ptr<cv::face::FacemarkAAM>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_face_FacemarkAAMG_to_PtrOfAlgorithm(cv::Ptr<cv::face::FacemarkAAM>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::face::Facemark>* cv_PtrLcv_face_FacemarkAAMG_to_PtrOfFacemark(cv::Ptr<cv::face::FacemarkAAM>* instance) {
			return new cv::Ptr<cv::face::Facemark>(instance->dynamicCast<cv::face::Facemark>());
	}
	
	cv::Ptr<cv::face::FacemarkTrain>* cv_PtrLcv_face_FacemarkAAMG_to_PtrOfFacemarkTrain(cv::Ptr<cv::face::FacemarkAAM>* instance) {
			return new cv::Ptr<cv::face::FacemarkTrain>(instance->dynamicCast<cv::face::FacemarkTrain>());
	}
	
}

extern "C" {
	const cv::face::FacemarkKazemi* cv_PtrLcv_face_FacemarkKazemiG_getInnerPtr_const(const cv::Ptr<cv::face::FacemarkKazemi>* instance) {
			return instance->get();
	}
	
	cv::face::FacemarkKazemi* cv_PtrLcv_face_FacemarkKazemiG_getInnerPtrMut(cv::Ptr<cv::face::FacemarkKazemi>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_face_FacemarkKazemiG_delete(cv::Ptr<cv::face::FacemarkKazemi>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_face_FacemarkKazemiG_to_PtrOfAlgorithm(cv::Ptr<cv::face::FacemarkKazemi>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::face::Facemark>* cv_PtrLcv_face_FacemarkKazemiG_to_PtrOfFacemark(cv::Ptr<cv::face::FacemarkKazemi>* instance) {
			return new cv::Ptr<cv::face::Facemark>(instance->dynamicCast<cv::face::Facemark>());
	}
	
}

extern "C" {
	const cv::face::FacemarkLBF* cv_PtrLcv_face_FacemarkLBFG_getInnerPtr_const(const cv::Ptr<cv::face::FacemarkLBF>* instance) {
			return instance->get();
	}
	
	cv::face::FacemarkLBF* cv_PtrLcv_face_FacemarkLBFG_getInnerPtrMut(cv::Ptr<cv::face::FacemarkLBF>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_face_FacemarkLBFG_delete(cv::Ptr<cv::face::FacemarkLBF>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_face_FacemarkLBFG_to_PtrOfAlgorithm(cv::Ptr<cv::face::FacemarkLBF>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::face::Facemark>* cv_PtrLcv_face_FacemarkLBFG_to_PtrOfFacemark(cv::Ptr<cv::face::FacemarkLBF>* instance) {
			return new cv::Ptr<cv::face::Facemark>(instance->dynamicCast<cv::face::Facemark>());
	}
	
	cv::Ptr<cv::face::FacemarkTrain>* cv_PtrLcv_face_FacemarkLBFG_to_PtrOfFacemarkTrain(cv::Ptr<cv::face::FacemarkLBF>* instance) {
			return new cv::Ptr<cv::face::FacemarkTrain>(instance->dynamicCast<cv::face::FacemarkTrain>());
	}
	
}

extern "C" {
	const cv::face::FacemarkTrain* cv_PtrLcv_face_FacemarkTrainG_getInnerPtr_const(const cv::Ptr<cv::face::FacemarkTrain>* instance) {
			return instance->get();
	}
	
	cv::face::FacemarkTrain* cv_PtrLcv_face_FacemarkTrainG_getInnerPtrMut(cv::Ptr<cv::face::FacemarkTrain>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_face_FacemarkTrainG_delete(cv::Ptr<cv::face::FacemarkTrain>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_face_FacemarkTrainG_to_PtrOfAlgorithm(cv::Ptr<cv::face::FacemarkTrain>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::face::Facemark>* cv_PtrLcv_face_FacemarkTrainG_to_PtrOfFacemark(cv::Ptr<cv::face::FacemarkTrain>* instance) {
			return new cv::Ptr<cv::face::Facemark>(instance->dynamicCast<cv::face::Facemark>());
	}
	
}

extern "C" {
	const cv::face::FisherFaceRecognizer* cv_PtrLcv_face_FisherFaceRecognizerG_getInnerPtr_const(const cv::Ptr<cv::face::FisherFaceRecognizer>* instance) {
			return instance->get();
	}
	
	cv::face::FisherFaceRecognizer* cv_PtrLcv_face_FisherFaceRecognizerG_getInnerPtrMut(cv::Ptr<cv::face::FisherFaceRecognizer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_face_FisherFaceRecognizerG_delete(cv::Ptr<cv::face::FisherFaceRecognizer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_face_FisherFaceRecognizerG_to_PtrOfAlgorithm(cv::Ptr<cv::face::FisherFaceRecognizer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::face::BasicFaceRecognizer>* cv_PtrLcv_face_FisherFaceRecognizerG_to_PtrOfBasicFaceRecognizer(cv::Ptr<cv::face::FisherFaceRecognizer>* instance) {
			return new cv::Ptr<cv::face::BasicFaceRecognizer>(instance->dynamicCast<cv::face::BasicFaceRecognizer>());
	}
	
	cv::Ptr<cv::face::FaceRecognizer>* cv_PtrLcv_face_FisherFaceRecognizerG_to_PtrOfFaceRecognizer(cv::Ptr<cv::face::FisherFaceRecognizer>* instance) {
			return new cv::Ptr<cv::face::FaceRecognizer>(instance->dynamicCast<cv::face::FaceRecognizer>());
	}
	
}

extern "C" {
	const cv::face::LBPHFaceRecognizer* cv_PtrLcv_face_LBPHFaceRecognizerG_getInnerPtr_const(const cv::Ptr<cv::face::LBPHFaceRecognizer>* instance) {
			return instance->get();
	}
	
	cv::face::LBPHFaceRecognizer* cv_PtrLcv_face_LBPHFaceRecognizerG_getInnerPtrMut(cv::Ptr<cv::face::LBPHFaceRecognizer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_face_LBPHFaceRecognizerG_delete(cv::Ptr<cv::face::LBPHFaceRecognizer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_face_LBPHFaceRecognizerG_to_PtrOfAlgorithm(cv::Ptr<cv::face::LBPHFaceRecognizer>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::face::FaceRecognizer>* cv_PtrLcv_face_LBPHFaceRecognizerG_to_PtrOfFaceRecognizer(cv::Ptr<cv::face::LBPHFaceRecognizer>* instance) {
			return new cv::Ptr<cv::face::FaceRecognizer>(instance->dynamicCast<cv::face::FaceRecognizer>());
	}
	
}

extern "C" {
	const cv::face::MACE* cv_PtrLcv_face_MACEG_getInnerPtr_const(const cv::Ptr<cv::face::MACE>* instance) {
			return instance->get();
	}
	
	cv::face::MACE* cv_PtrLcv_face_MACEG_getInnerPtrMut(cv::Ptr<cv::face::MACE>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_face_MACEG_delete(cv::Ptr<cv::face::MACE>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_face_MACEG_to_PtrOfAlgorithm(cv::Ptr<cv::face::MACE>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::face::PredictCollector* cv_PtrLcv_face_PredictCollectorG_getInnerPtr_const(const cv::Ptr<cv::face::PredictCollector>* instance) {
			return instance->get();
	}
	
	cv::face::PredictCollector* cv_PtrLcv_face_PredictCollectorG_getInnerPtrMut(cv::Ptr<cv::face::PredictCollector>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_face_PredictCollectorG_delete(cv::Ptr<cv::face::PredictCollector>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::face::StandardCollector* cv_PtrLcv_face_StandardCollectorG_getInnerPtr_const(const cv::Ptr<cv::face::StandardCollector>* instance) {
			return instance->get();
	}
	
	cv::face::StandardCollector* cv_PtrLcv_face_StandardCollectorG_getInnerPtrMut(cv::Ptr<cv::face::StandardCollector>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_face_StandardCollectorG_delete(cv::Ptr<cv::face::StandardCollector>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::face::PredictCollector>* cv_PtrLcv_face_StandardCollectorG_to_PtrOfPredictCollector(cv::Ptr<cv::face::StandardCollector>* instance) {
			return new cv::Ptr<cv::face::PredictCollector>(instance->dynamicCast<cv::face::PredictCollector>());
	}
	
	cv::Ptr<cv::face::StandardCollector>* cv_PtrLcv_face_StandardCollectorG_new_const_StandardCollector(cv::face::StandardCollector* val) {
			return new cv::Ptr<cv::face::StandardCollector>(val);
	}
	
}

extern "C" {
	std::vector<cv::face::FacemarkAAM::Config>* std_vectorLcv_face_FacemarkAAM_ConfigG_new_const() {
			std::vector<cv::face::FacemarkAAM::Config>* ret = new std::vector<cv::face::FacemarkAAM::Config>();
			return ret;
	}
	
	void std_vectorLcv_face_FacemarkAAM_ConfigG_delete(std::vector<cv::face::FacemarkAAM::Config>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_face_FacemarkAAM_ConfigG_len_const(const std::vector<cv::face::FacemarkAAM::Config>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_face_FacemarkAAM_ConfigG_isEmpty_const(const std::vector<cv::face::FacemarkAAM::Config>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_face_FacemarkAAM_ConfigG_capacity_const(const std::vector<cv::face::FacemarkAAM::Config>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_face_FacemarkAAM_ConfigG_shrinkToFit(std::vector<cv::face::FacemarkAAM::Config>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_face_FacemarkAAM_ConfigG_reserve_size_t(std::vector<cv::face::FacemarkAAM::Config>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_face_FacemarkAAM_ConfigG_remove_size_t(std::vector<cv::face::FacemarkAAM::Config>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_face_FacemarkAAM_ConfigG_swap_size_t_size_t(std::vector<cv::face::FacemarkAAM::Config>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_face_FacemarkAAM_ConfigG_clear(std::vector<cv::face::FacemarkAAM::Config>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_face_FacemarkAAM_ConfigG_push_const_Config(std::vector<cv::face::FacemarkAAM::Config>* instance, const cv::face::FacemarkAAM::Config* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_face_FacemarkAAM_ConfigG_insert_size_t_const_Config(std::vector<cv::face::FacemarkAAM::Config>* instance, size_t index, const cv::face::FacemarkAAM::Config* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_face_FacemarkAAM_ConfigG_get_const_size_t(const std::vector<cv::face::FacemarkAAM::Config>* instance, size_t index, cv::face::FacemarkAAM::Config** ocvrs_return) {
			cv::face::FacemarkAAM::Config ret = (*instance)[index];
			*ocvrs_return = new cv::face::FacemarkAAM::Config(ret);
	}
	
	void std_vectorLcv_face_FacemarkAAM_ConfigG_set_size_t_const_Config(std::vector<cv::face::FacemarkAAM::Config>* instance, size_t index, const cv::face::FacemarkAAM::Config* val) {
			(*instance)[index] = *val;
	}
	
}


extern "C" {
	std::vector<cv::face::FacemarkAAM::Model::Texture>* std_vectorLcv_face_FacemarkAAM_Model_TextureG_new_const() {
			std::vector<cv::face::FacemarkAAM::Model::Texture>* ret = new std::vector<cv::face::FacemarkAAM::Model::Texture>();
			return ret;
	}
	
	void std_vectorLcv_face_FacemarkAAM_Model_TextureG_delete(std::vector<cv::face::FacemarkAAM::Model::Texture>* instance) {
			delete instance;
	}
	
	size_t std_vectorLcv_face_FacemarkAAM_Model_TextureG_len_const(const std::vector<cv::face::FacemarkAAM::Model::Texture>* instance) {
			size_t ret = instance->size();
			return ret;
	}
	
	bool std_vectorLcv_face_FacemarkAAM_Model_TextureG_isEmpty_const(const std::vector<cv::face::FacemarkAAM::Model::Texture>* instance) {
			bool ret = instance->empty();
			return ret;
	}
	
	size_t std_vectorLcv_face_FacemarkAAM_Model_TextureG_capacity_const(const std::vector<cv::face::FacemarkAAM::Model::Texture>* instance) {
			size_t ret = instance->capacity();
			return ret;
	}
	
	void std_vectorLcv_face_FacemarkAAM_Model_TextureG_shrinkToFit(std::vector<cv::face::FacemarkAAM::Model::Texture>* instance) {
			instance->shrink_to_fit();
	}
	
	void std_vectorLcv_face_FacemarkAAM_Model_TextureG_reserve_size_t(std::vector<cv::face::FacemarkAAM::Model::Texture>* instance, size_t additional) {
			instance->reserve(instance->size() + additional);
	}
	
	void std_vectorLcv_face_FacemarkAAM_Model_TextureG_remove_size_t(std::vector<cv::face::FacemarkAAM::Model::Texture>* instance, size_t index) {
			instance->erase(instance->begin() + index);
	}
	
	void std_vectorLcv_face_FacemarkAAM_Model_TextureG_swap_size_t_size_t(std::vector<cv::face::FacemarkAAM::Model::Texture>* instance, size_t index1, size_t index2) {
			std::swap((*instance)[index1], (*instance)[index2]);
	}
	
	void std_vectorLcv_face_FacemarkAAM_Model_TextureG_clear(std::vector<cv::face::FacemarkAAM::Model::Texture>* instance) {
			instance->clear();
	}
	
	void std_vectorLcv_face_FacemarkAAM_Model_TextureG_push_const_Texture(std::vector<cv::face::FacemarkAAM::Model::Texture>* instance, const cv::face::FacemarkAAM::Model::Texture* val) {
			instance->push_back(*val);
	}
	
	void std_vectorLcv_face_FacemarkAAM_Model_TextureG_insert_size_t_const_Texture(std::vector<cv::face::FacemarkAAM::Model::Texture>* instance, size_t index, const cv::face::FacemarkAAM::Model::Texture* val) {
			instance->insert(instance->begin() + index, *val);
	}
	
	void std_vectorLcv_face_FacemarkAAM_Model_TextureG_get_const_size_t(const std::vector<cv::face::FacemarkAAM::Model::Texture>* instance, size_t index, cv::face::FacemarkAAM::Model::Texture** ocvrs_return) {
			cv::face::FacemarkAAM::Model::Texture ret = (*instance)[index];
			*ocvrs_return = new cv::face::FacemarkAAM::Model::Texture(ret);
	}
	
	void std_vectorLcv_face_FacemarkAAM_Model_TextureG_set_size_t_const_Texture(std::vector<cv::face::FacemarkAAM::Model::Texture>* instance, size_t index, const cv::face::FacemarkAAM::Model::Texture* val) {
			(*instance)[index] = *val;
	}
	
}



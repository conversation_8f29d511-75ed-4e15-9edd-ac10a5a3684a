extern "C" {
	const cv::videostab::ColorAverageInpainter* cv_PtrLcv_videostab_ColorAverageInpainterG_getInnerPtr_const(const cv::Ptr<cv::videostab::ColorAverageInpainter>* instance) {
			return instance->get();
	}
	
	cv::videostab::ColorAverageInpainter* cv_PtrLcv_videostab_ColorAverageInpainterG_getInnerPtrMut(cv::Ptr<cv::videostab::ColorAverageInpainter>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_ColorAverageInpainterG_delete(cv::Ptr<cv::videostab::ColorAverageInpainter>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::InpainterBase>* cv_PtrLcv_videostab_ColorAverageInpainterG_to_PtrOfInpainterBase(cv::Ptr<cv::videostab::ColorAverageInpainter>* instance) {
			return new cv::Ptr<cv::videostab::InpainterBase>(instance->dynamicCast<cv::videostab::InpainterBase>());
	}
	
	cv::Ptr<cv::videostab::ColorAverageInpainter>* cv_PtrLcv_videostab_ColorAverageInpainterG_new_const_ColorAverageInpainter(cv::videostab::ColorAverageInpainter* val) {
			return new cv::Ptr<cv::videostab::ColorAverageInpainter>(val);
	}
	
}

extern "C" {
	const cv::videostab::ColorInpainter* cv_PtrLcv_videostab_ColorInpainterG_getInnerPtr_const(const cv::Ptr<cv::videostab::ColorInpainter>* instance) {
			return instance->get();
	}
	
	cv::videostab::ColorInpainter* cv_PtrLcv_videostab_ColorInpainterG_getInnerPtrMut(cv::Ptr<cv::videostab::ColorInpainter>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_ColorInpainterG_delete(cv::Ptr<cv::videostab::ColorInpainter>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::InpainterBase>* cv_PtrLcv_videostab_ColorInpainterG_to_PtrOfInpainterBase(cv::Ptr<cv::videostab::ColorInpainter>* instance) {
			return new cv::Ptr<cv::videostab::InpainterBase>(instance->dynamicCast<cv::videostab::InpainterBase>());
	}
	
	cv::Ptr<cv::videostab::ColorInpainter>* cv_PtrLcv_videostab_ColorInpainterG_new_const_ColorInpainter(cv::videostab::ColorInpainter* val) {
			return new cv::Ptr<cv::videostab::ColorInpainter>(val);
	}
	
}

extern "C" {
	const cv::videostab::ConsistentMosaicInpainter* cv_PtrLcv_videostab_ConsistentMosaicInpainterG_getInnerPtr_const(const cv::Ptr<cv::videostab::ConsistentMosaicInpainter>* instance) {
			return instance->get();
	}
	
	cv::videostab::ConsistentMosaicInpainter* cv_PtrLcv_videostab_ConsistentMosaicInpainterG_getInnerPtrMut(cv::Ptr<cv::videostab::ConsistentMosaicInpainter>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_ConsistentMosaicInpainterG_delete(cv::Ptr<cv::videostab::ConsistentMosaicInpainter>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::InpainterBase>* cv_PtrLcv_videostab_ConsistentMosaicInpainterG_to_PtrOfInpainterBase(cv::Ptr<cv::videostab::ConsistentMosaicInpainter>* instance) {
			return new cv::Ptr<cv::videostab::InpainterBase>(instance->dynamicCast<cv::videostab::InpainterBase>());
	}
	
	cv::Ptr<cv::videostab::ConsistentMosaicInpainter>* cv_PtrLcv_videostab_ConsistentMosaicInpainterG_new_const_ConsistentMosaicInpainter(cv::videostab::ConsistentMosaicInpainter* val) {
			return new cv::Ptr<cv::videostab::ConsistentMosaicInpainter>(val);
	}
	
}

extern "C" {
	const cv::videostab::DeblurerBase* cv_PtrLcv_videostab_DeblurerBaseG_getInnerPtr_const(const cv::Ptr<cv::videostab::DeblurerBase>* instance) {
			return instance->get();
	}
	
	cv::videostab::DeblurerBase* cv_PtrLcv_videostab_DeblurerBaseG_getInnerPtrMut(cv::Ptr<cv::videostab::DeblurerBase>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_DeblurerBaseG_delete(cv::Ptr<cv::videostab::DeblurerBase>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::videostab::FromFileMotionReader* cv_PtrLcv_videostab_FromFileMotionReaderG_getInnerPtr_const(const cv::Ptr<cv::videostab::FromFileMotionReader>* instance) {
			return instance->get();
	}
	
	cv::videostab::FromFileMotionReader* cv_PtrLcv_videostab_FromFileMotionReaderG_getInnerPtrMut(cv::Ptr<cv::videostab::FromFileMotionReader>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_FromFileMotionReaderG_delete(cv::Ptr<cv::videostab::FromFileMotionReader>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::ImageMotionEstimatorBase>* cv_PtrLcv_videostab_FromFileMotionReaderG_to_PtrOfImageMotionEstimatorBase(cv::Ptr<cv::videostab::FromFileMotionReader>* instance) {
			return new cv::Ptr<cv::videostab::ImageMotionEstimatorBase>(instance->dynamicCast<cv::videostab::ImageMotionEstimatorBase>());
	}
	
	cv::Ptr<cv::videostab::FromFileMotionReader>* cv_PtrLcv_videostab_FromFileMotionReaderG_new_const_FromFileMotionReader(cv::videostab::FromFileMotionReader* val) {
			return new cv::Ptr<cv::videostab::FromFileMotionReader>(val);
	}
	
}

extern "C" {
	const cv::videostab::GaussianMotionFilter* cv_PtrLcv_videostab_GaussianMotionFilterG_getInnerPtr_const(const cv::Ptr<cv::videostab::GaussianMotionFilter>* instance) {
			return instance->get();
	}
	
	cv::videostab::GaussianMotionFilter* cv_PtrLcv_videostab_GaussianMotionFilterG_getInnerPtrMut(cv::Ptr<cv::videostab::GaussianMotionFilter>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_GaussianMotionFilterG_delete(cv::Ptr<cv::videostab::GaussianMotionFilter>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::IMotionStabilizer>* cv_PtrLcv_videostab_GaussianMotionFilterG_to_PtrOfIMotionStabilizer(cv::Ptr<cv::videostab::GaussianMotionFilter>* instance) {
			return new cv::Ptr<cv::videostab::IMotionStabilizer>(instance->dynamicCast<cv::videostab::IMotionStabilizer>());
	}
	
	cv::Ptr<cv::videostab::MotionFilterBase>* cv_PtrLcv_videostab_GaussianMotionFilterG_to_PtrOfMotionFilterBase(cv::Ptr<cv::videostab::GaussianMotionFilter>* instance) {
			return new cv::Ptr<cv::videostab::MotionFilterBase>(instance->dynamicCast<cv::videostab::MotionFilterBase>());
	}
	
	cv::Ptr<cv::videostab::GaussianMotionFilter>* cv_PtrLcv_videostab_GaussianMotionFilterG_new_const_GaussianMotionFilter(cv::videostab::GaussianMotionFilter* val) {
			return new cv::Ptr<cv::videostab::GaussianMotionFilter>(val);
	}
	
}

extern "C" {
	const cv::videostab::IDenseOptFlowEstimator* cv_PtrLcv_videostab_IDenseOptFlowEstimatorG_getInnerPtr_const(const cv::Ptr<cv::videostab::IDenseOptFlowEstimator>* instance) {
			return instance->get();
	}
	
	cv::videostab::IDenseOptFlowEstimator* cv_PtrLcv_videostab_IDenseOptFlowEstimatorG_getInnerPtrMut(cv::Ptr<cv::videostab::IDenseOptFlowEstimator>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_IDenseOptFlowEstimatorG_delete(cv::Ptr<cv::videostab::IDenseOptFlowEstimator>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::videostab::IFrameSource* cv_PtrLcv_videostab_IFrameSourceG_getInnerPtr_const(const cv::Ptr<cv::videostab::IFrameSource>* instance) {
			return instance->get();
	}
	
	cv::videostab::IFrameSource* cv_PtrLcv_videostab_IFrameSourceG_getInnerPtrMut(cv::Ptr<cv::videostab::IFrameSource>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_IFrameSourceG_delete(cv::Ptr<cv::videostab::IFrameSource>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::videostab::ILog* cv_PtrLcv_videostab_ILogG_getInnerPtr_const(const cv::Ptr<cv::videostab::ILog>* instance) {
			return instance->get();
	}
	
	cv::videostab::ILog* cv_PtrLcv_videostab_ILogG_getInnerPtrMut(cv::Ptr<cv::videostab::ILog>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_ILogG_delete(cv::Ptr<cv::videostab::ILog>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::videostab::IMotionStabilizer* cv_PtrLcv_videostab_IMotionStabilizerG_getInnerPtr_const(const cv::Ptr<cv::videostab::IMotionStabilizer>* instance) {
			return instance->get();
	}
	
	cv::videostab::IMotionStabilizer* cv_PtrLcv_videostab_IMotionStabilizerG_getInnerPtrMut(cv::Ptr<cv::videostab::IMotionStabilizer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_IMotionStabilizerG_delete(cv::Ptr<cv::videostab::IMotionStabilizer>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::videostab::IOutlierRejector* cv_PtrLcv_videostab_IOutlierRejectorG_getInnerPtr_const(const cv::Ptr<cv::videostab::IOutlierRejector>* instance) {
			return instance->get();
	}
	
	cv::videostab::IOutlierRejector* cv_PtrLcv_videostab_IOutlierRejectorG_getInnerPtrMut(cv::Ptr<cv::videostab::IOutlierRejector>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_IOutlierRejectorG_delete(cv::Ptr<cv::videostab::IOutlierRejector>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::videostab::ISparseOptFlowEstimator* cv_PtrLcv_videostab_ISparseOptFlowEstimatorG_getInnerPtr_const(const cv::Ptr<cv::videostab::ISparseOptFlowEstimator>* instance) {
			return instance->get();
	}
	
	cv::videostab::ISparseOptFlowEstimator* cv_PtrLcv_videostab_ISparseOptFlowEstimatorG_getInnerPtrMut(cv::Ptr<cv::videostab::ISparseOptFlowEstimator>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_ISparseOptFlowEstimatorG_delete(cv::Ptr<cv::videostab::ISparseOptFlowEstimator>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::videostab::ImageMotionEstimatorBase* cv_PtrLcv_videostab_ImageMotionEstimatorBaseG_getInnerPtr_const(const cv::Ptr<cv::videostab::ImageMotionEstimatorBase>* instance) {
			return instance->get();
	}
	
	cv::videostab::ImageMotionEstimatorBase* cv_PtrLcv_videostab_ImageMotionEstimatorBaseG_getInnerPtrMut(cv::Ptr<cv::videostab::ImageMotionEstimatorBase>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_ImageMotionEstimatorBaseG_delete(cv::Ptr<cv::videostab::ImageMotionEstimatorBase>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::videostab::InpainterBase* cv_PtrLcv_videostab_InpainterBaseG_getInnerPtr_const(const cv::Ptr<cv::videostab::InpainterBase>* instance) {
			return instance->get();
	}
	
	cv::videostab::InpainterBase* cv_PtrLcv_videostab_InpainterBaseG_getInnerPtrMut(cv::Ptr<cv::videostab::InpainterBase>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_InpainterBaseG_delete(cv::Ptr<cv::videostab::InpainterBase>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::videostab::InpaintingPipeline* cv_PtrLcv_videostab_InpaintingPipelineG_getInnerPtr_const(const cv::Ptr<cv::videostab::InpaintingPipeline>* instance) {
			return instance->get();
	}
	
	cv::videostab::InpaintingPipeline* cv_PtrLcv_videostab_InpaintingPipelineG_getInnerPtrMut(cv::Ptr<cv::videostab::InpaintingPipeline>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_InpaintingPipelineG_delete(cv::Ptr<cv::videostab::InpaintingPipeline>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::InpainterBase>* cv_PtrLcv_videostab_InpaintingPipelineG_to_PtrOfInpainterBase(cv::Ptr<cv::videostab::InpaintingPipeline>* instance) {
			return new cv::Ptr<cv::videostab::InpainterBase>(instance->dynamicCast<cv::videostab::InpainterBase>());
	}
	
	cv::Ptr<cv::videostab::InpaintingPipeline>* cv_PtrLcv_videostab_InpaintingPipelineG_new_const_InpaintingPipeline(cv::videostab::InpaintingPipeline* val) {
			return new cv::Ptr<cv::videostab::InpaintingPipeline>(val);
	}
	
}

extern "C" {
	const cv::videostab::KeypointBasedMotionEstimator* cv_PtrLcv_videostab_KeypointBasedMotionEstimatorG_getInnerPtr_const(const cv::Ptr<cv::videostab::KeypointBasedMotionEstimator>* instance) {
			return instance->get();
	}
	
	cv::videostab::KeypointBasedMotionEstimator* cv_PtrLcv_videostab_KeypointBasedMotionEstimatorG_getInnerPtrMut(cv::Ptr<cv::videostab::KeypointBasedMotionEstimator>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_KeypointBasedMotionEstimatorG_delete(cv::Ptr<cv::videostab::KeypointBasedMotionEstimator>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::ImageMotionEstimatorBase>* cv_PtrLcv_videostab_KeypointBasedMotionEstimatorG_to_PtrOfImageMotionEstimatorBase(cv::Ptr<cv::videostab::KeypointBasedMotionEstimator>* instance) {
			return new cv::Ptr<cv::videostab::ImageMotionEstimatorBase>(instance->dynamicCast<cv::videostab::ImageMotionEstimatorBase>());
	}
	
	cv::Ptr<cv::videostab::KeypointBasedMotionEstimator>* cv_PtrLcv_videostab_KeypointBasedMotionEstimatorG_new_const_KeypointBasedMotionEstimator(cv::videostab::KeypointBasedMotionEstimator* val) {
			return new cv::Ptr<cv::videostab::KeypointBasedMotionEstimator>(val);
	}
	
}

extern "C" {
	const cv::videostab::LogToStdout* cv_PtrLcv_videostab_LogToStdoutG_getInnerPtr_const(const cv::Ptr<cv::videostab::LogToStdout>* instance) {
			return instance->get();
	}
	
	cv::videostab::LogToStdout* cv_PtrLcv_videostab_LogToStdoutG_getInnerPtrMut(cv::Ptr<cv::videostab::LogToStdout>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_LogToStdoutG_delete(cv::Ptr<cv::videostab::LogToStdout>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::ILog>* cv_PtrLcv_videostab_LogToStdoutG_to_PtrOfILog(cv::Ptr<cv::videostab::LogToStdout>* instance) {
			return new cv::Ptr<cv::videostab::ILog>(instance->dynamicCast<cv::videostab::ILog>());
	}
	
	cv::Ptr<cv::videostab::LogToStdout>* cv_PtrLcv_videostab_LogToStdoutG_new_const_LogToStdout(cv::videostab::LogToStdout* val) {
			return new cv::Ptr<cv::videostab::LogToStdout>(val);
	}
	
}

extern "C" {
	const cv::videostab::LpMotionStabilizer* cv_PtrLcv_videostab_LpMotionStabilizerG_getInnerPtr_const(const cv::Ptr<cv::videostab::LpMotionStabilizer>* instance) {
			return instance->get();
	}
	
	cv::videostab::LpMotionStabilizer* cv_PtrLcv_videostab_LpMotionStabilizerG_getInnerPtrMut(cv::Ptr<cv::videostab::LpMotionStabilizer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_LpMotionStabilizerG_delete(cv::Ptr<cv::videostab::LpMotionStabilizer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::IMotionStabilizer>* cv_PtrLcv_videostab_LpMotionStabilizerG_to_PtrOfIMotionStabilizer(cv::Ptr<cv::videostab::LpMotionStabilizer>* instance) {
			return new cv::Ptr<cv::videostab::IMotionStabilizer>(instance->dynamicCast<cv::videostab::IMotionStabilizer>());
	}
	
	cv::Ptr<cv::videostab::LpMotionStabilizer>* cv_PtrLcv_videostab_LpMotionStabilizerG_new_const_LpMotionStabilizer(cv::videostab::LpMotionStabilizer* val) {
			return new cv::Ptr<cv::videostab::LpMotionStabilizer>(val);
	}
	
}

extern "C" {
	const cv::videostab::MaskFrameSource* cv_PtrLcv_videostab_MaskFrameSourceG_getInnerPtr_const(const cv::Ptr<cv::videostab::MaskFrameSource>* instance) {
			return instance->get();
	}
	
	cv::videostab::MaskFrameSource* cv_PtrLcv_videostab_MaskFrameSourceG_getInnerPtrMut(cv::Ptr<cv::videostab::MaskFrameSource>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_MaskFrameSourceG_delete(cv::Ptr<cv::videostab::MaskFrameSource>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::IFrameSource>* cv_PtrLcv_videostab_MaskFrameSourceG_to_PtrOfIFrameSource(cv::Ptr<cv::videostab::MaskFrameSource>* instance) {
			return new cv::Ptr<cv::videostab::IFrameSource>(instance->dynamicCast<cv::videostab::IFrameSource>());
	}
	
	cv::Ptr<cv::videostab::MaskFrameSource>* cv_PtrLcv_videostab_MaskFrameSourceG_new_const_MaskFrameSource(cv::videostab::MaskFrameSource* val) {
			return new cv::Ptr<cv::videostab::MaskFrameSource>(val);
	}
	
}

extern "C" {
	const cv::videostab::MoreAccurateMotionWobbleSuppressor* cv_PtrLcv_videostab_MoreAccurateMotionWobbleSuppressorG_getInnerPtr_const(const cv::Ptr<cv::videostab::MoreAccurateMotionWobbleSuppressor>* instance) {
			return instance->get();
	}
	
	cv::videostab::MoreAccurateMotionWobbleSuppressor* cv_PtrLcv_videostab_MoreAccurateMotionWobbleSuppressorG_getInnerPtrMut(cv::Ptr<cv::videostab::MoreAccurateMotionWobbleSuppressor>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_MoreAccurateMotionWobbleSuppressorG_delete(cv::Ptr<cv::videostab::MoreAccurateMotionWobbleSuppressor>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::MoreAccurateMotionWobbleSuppressorBase>* cv_PtrLcv_videostab_MoreAccurateMotionWobbleSuppressorG_to_PtrOfMoreAccurateMotionWobbleSuppressorBase(cv::Ptr<cv::videostab::MoreAccurateMotionWobbleSuppressor>* instance) {
			return new cv::Ptr<cv::videostab::MoreAccurateMotionWobbleSuppressorBase>(instance->dynamicCast<cv::videostab::MoreAccurateMotionWobbleSuppressorBase>());
	}
	
	cv::Ptr<cv::videostab::WobbleSuppressorBase>* cv_PtrLcv_videostab_MoreAccurateMotionWobbleSuppressorG_to_PtrOfWobbleSuppressorBase(cv::Ptr<cv::videostab::MoreAccurateMotionWobbleSuppressor>* instance) {
			return new cv::Ptr<cv::videostab::WobbleSuppressorBase>(instance->dynamicCast<cv::videostab::WobbleSuppressorBase>());
	}
	
	cv::Ptr<cv::videostab::MoreAccurateMotionWobbleSuppressor>* cv_PtrLcv_videostab_MoreAccurateMotionWobbleSuppressorG_new_const_MoreAccurateMotionWobbleSuppressor(cv::videostab::MoreAccurateMotionWobbleSuppressor* val) {
			return new cv::Ptr<cv::videostab::MoreAccurateMotionWobbleSuppressor>(val);
	}
	
}

extern "C" {
	const cv::videostab::MoreAccurateMotionWobbleSuppressorBase* cv_PtrLcv_videostab_MoreAccurateMotionWobbleSuppressorBaseG_getInnerPtr_const(const cv::Ptr<cv::videostab::MoreAccurateMotionWobbleSuppressorBase>* instance) {
			return instance->get();
	}
	
	cv::videostab::MoreAccurateMotionWobbleSuppressorBase* cv_PtrLcv_videostab_MoreAccurateMotionWobbleSuppressorBaseG_getInnerPtrMut(cv::Ptr<cv::videostab::MoreAccurateMotionWobbleSuppressorBase>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_MoreAccurateMotionWobbleSuppressorBaseG_delete(cv::Ptr<cv::videostab::MoreAccurateMotionWobbleSuppressorBase>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::WobbleSuppressorBase>* cv_PtrLcv_videostab_MoreAccurateMotionWobbleSuppressorBaseG_to_PtrOfWobbleSuppressorBase(cv::Ptr<cv::videostab::MoreAccurateMotionWobbleSuppressorBase>* instance) {
			return new cv::Ptr<cv::videostab::WobbleSuppressorBase>(instance->dynamicCast<cv::videostab::WobbleSuppressorBase>());
	}
	
}

extern "C" {
	const cv::videostab::MotionEstimatorBase* cv_PtrLcv_videostab_MotionEstimatorBaseG_getInnerPtr_const(const cv::Ptr<cv::videostab::MotionEstimatorBase>* instance) {
			return instance->get();
	}
	
	cv::videostab::MotionEstimatorBase* cv_PtrLcv_videostab_MotionEstimatorBaseG_getInnerPtrMut(cv::Ptr<cv::videostab::MotionEstimatorBase>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_MotionEstimatorBaseG_delete(cv::Ptr<cv::videostab::MotionEstimatorBase>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::videostab::MotionEstimatorL1* cv_PtrLcv_videostab_MotionEstimatorL1G_getInnerPtr_const(const cv::Ptr<cv::videostab::MotionEstimatorL1>* instance) {
			return instance->get();
	}
	
	cv::videostab::MotionEstimatorL1* cv_PtrLcv_videostab_MotionEstimatorL1G_getInnerPtrMut(cv::Ptr<cv::videostab::MotionEstimatorL1>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_MotionEstimatorL1G_delete(cv::Ptr<cv::videostab::MotionEstimatorL1>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::MotionEstimatorBase>* cv_PtrLcv_videostab_MotionEstimatorL1G_to_PtrOfMotionEstimatorBase(cv::Ptr<cv::videostab::MotionEstimatorL1>* instance) {
			return new cv::Ptr<cv::videostab::MotionEstimatorBase>(instance->dynamicCast<cv::videostab::MotionEstimatorBase>());
	}
	
	cv::Ptr<cv::videostab::MotionEstimatorL1>* cv_PtrLcv_videostab_MotionEstimatorL1G_new_const_MotionEstimatorL1(cv::videostab::MotionEstimatorL1* val) {
			return new cv::Ptr<cv::videostab::MotionEstimatorL1>(val);
	}
	
}

extern "C" {
	const cv::videostab::MotionEstimatorRansacL2* cv_PtrLcv_videostab_MotionEstimatorRansacL2G_getInnerPtr_const(const cv::Ptr<cv::videostab::MotionEstimatorRansacL2>* instance) {
			return instance->get();
	}
	
	cv::videostab::MotionEstimatorRansacL2* cv_PtrLcv_videostab_MotionEstimatorRansacL2G_getInnerPtrMut(cv::Ptr<cv::videostab::MotionEstimatorRansacL2>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_MotionEstimatorRansacL2G_delete(cv::Ptr<cv::videostab::MotionEstimatorRansacL2>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::MotionEstimatorBase>* cv_PtrLcv_videostab_MotionEstimatorRansacL2G_to_PtrOfMotionEstimatorBase(cv::Ptr<cv::videostab::MotionEstimatorRansacL2>* instance) {
			return new cv::Ptr<cv::videostab::MotionEstimatorBase>(instance->dynamicCast<cv::videostab::MotionEstimatorBase>());
	}
	
	cv::Ptr<cv::videostab::MotionEstimatorRansacL2>* cv_PtrLcv_videostab_MotionEstimatorRansacL2G_new_const_MotionEstimatorRansacL2(cv::videostab::MotionEstimatorRansacL2* val) {
			return new cv::Ptr<cv::videostab::MotionEstimatorRansacL2>(val);
	}
	
}

extern "C" {
	const cv::videostab::MotionFilterBase* cv_PtrLcv_videostab_MotionFilterBaseG_getInnerPtr_const(const cv::Ptr<cv::videostab::MotionFilterBase>* instance) {
			return instance->get();
	}
	
	cv::videostab::MotionFilterBase* cv_PtrLcv_videostab_MotionFilterBaseG_getInnerPtrMut(cv::Ptr<cv::videostab::MotionFilterBase>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_MotionFilterBaseG_delete(cv::Ptr<cv::videostab::MotionFilterBase>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::IMotionStabilizer>* cv_PtrLcv_videostab_MotionFilterBaseG_to_PtrOfIMotionStabilizer(cv::Ptr<cv::videostab::MotionFilterBase>* instance) {
			return new cv::Ptr<cv::videostab::IMotionStabilizer>(instance->dynamicCast<cv::videostab::IMotionStabilizer>());
	}
	
}

extern "C" {
	const cv::videostab::MotionInpainter* cv_PtrLcv_videostab_MotionInpainterG_getInnerPtr_const(const cv::Ptr<cv::videostab::MotionInpainter>* instance) {
			return instance->get();
	}
	
	cv::videostab::MotionInpainter* cv_PtrLcv_videostab_MotionInpainterG_getInnerPtrMut(cv::Ptr<cv::videostab::MotionInpainter>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_MotionInpainterG_delete(cv::Ptr<cv::videostab::MotionInpainter>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::InpainterBase>* cv_PtrLcv_videostab_MotionInpainterG_to_PtrOfInpainterBase(cv::Ptr<cv::videostab::MotionInpainter>* instance) {
			return new cv::Ptr<cv::videostab::InpainterBase>(instance->dynamicCast<cv::videostab::InpainterBase>());
	}
	
	cv::Ptr<cv::videostab::MotionInpainter>* cv_PtrLcv_videostab_MotionInpainterG_new_const_MotionInpainter(cv::videostab::MotionInpainter* val) {
			return new cv::Ptr<cv::videostab::MotionInpainter>(val);
	}
	
}

extern "C" {
	const cv::videostab::MotionStabilizationPipeline* cv_PtrLcv_videostab_MotionStabilizationPipelineG_getInnerPtr_const(const cv::Ptr<cv::videostab::MotionStabilizationPipeline>* instance) {
			return instance->get();
	}
	
	cv::videostab::MotionStabilizationPipeline* cv_PtrLcv_videostab_MotionStabilizationPipelineG_getInnerPtrMut(cv::Ptr<cv::videostab::MotionStabilizationPipeline>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_MotionStabilizationPipelineG_delete(cv::Ptr<cv::videostab::MotionStabilizationPipeline>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::IMotionStabilizer>* cv_PtrLcv_videostab_MotionStabilizationPipelineG_to_PtrOfIMotionStabilizer(cv::Ptr<cv::videostab::MotionStabilizationPipeline>* instance) {
			return new cv::Ptr<cv::videostab::IMotionStabilizer>(instance->dynamicCast<cv::videostab::IMotionStabilizer>());
	}
	
	cv::Ptr<cv::videostab::MotionStabilizationPipeline>* cv_PtrLcv_videostab_MotionStabilizationPipelineG_new_const_MotionStabilizationPipeline(cv::videostab::MotionStabilizationPipeline* val) {
			return new cv::Ptr<cv::videostab::MotionStabilizationPipeline>(val);
	}
	
}

extern "C" {
	const cv::videostab::NullDeblurer* cv_PtrLcv_videostab_NullDeblurerG_getInnerPtr_const(const cv::Ptr<cv::videostab::NullDeblurer>* instance) {
			return instance->get();
	}
	
	cv::videostab::NullDeblurer* cv_PtrLcv_videostab_NullDeblurerG_getInnerPtrMut(cv::Ptr<cv::videostab::NullDeblurer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_NullDeblurerG_delete(cv::Ptr<cv::videostab::NullDeblurer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::DeblurerBase>* cv_PtrLcv_videostab_NullDeblurerG_to_PtrOfDeblurerBase(cv::Ptr<cv::videostab::NullDeblurer>* instance) {
			return new cv::Ptr<cv::videostab::DeblurerBase>(instance->dynamicCast<cv::videostab::DeblurerBase>());
	}
	
	cv::Ptr<cv::videostab::NullDeblurer>* cv_PtrLcv_videostab_NullDeblurerG_new_const_NullDeblurer(cv::videostab::NullDeblurer* val) {
			return new cv::Ptr<cv::videostab::NullDeblurer>(val);
	}
	
}

extern "C" {
	const cv::videostab::NullFrameSource* cv_PtrLcv_videostab_NullFrameSourceG_getInnerPtr_const(const cv::Ptr<cv::videostab::NullFrameSource>* instance) {
			return instance->get();
	}
	
	cv::videostab::NullFrameSource* cv_PtrLcv_videostab_NullFrameSourceG_getInnerPtrMut(cv::Ptr<cv::videostab::NullFrameSource>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_NullFrameSourceG_delete(cv::Ptr<cv::videostab::NullFrameSource>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::IFrameSource>* cv_PtrLcv_videostab_NullFrameSourceG_to_PtrOfIFrameSource(cv::Ptr<cv::videostab::NullFrameSource>* instance) {
			return new cv::Ptr<cv::videostab::IFrameSource>(instance->dynamicCast<cv::videostab::IFrameSource>());
	}
	
	cv::Ptr<cv::videostab::NullFrameSource>* cv_PtrLcv_videostab_NullFrameSourceG_new_const_NullFrameSource(cv::videostab::NullFrameSource* val) {
			return new cv::Ptr<cv::videostab::NullFrameSource>(val);
	}
	
}

extern "C" {
	const cv::videostab::NullInpainter* cv_PtrLcv_videostab_NullInpainterG_getInnerPtr_const(const cv::Ptr<cv::videostab::NullInpainter>* instance) {
			return instance->get();
	}
	
	cv::videostab::NullInpainter* cv_PtrLcv_videostab_NullInpainterG_getInnerPtrMut(cv::Ptr<cv::videostab::NullInpainter>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_NullInpainterG_delete(cv::Ptr<cv::videostab::NullInpainter>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::InpainterBase>* cv_PtrLcv_videostab_NullInpainterG_to_PtrOfInpainterBase(cv::Ptr<cv::videostab::NullInpainter>* instance) {
			return new cv::Ptr<cv::videostab::InpainterBase>(instance->dynamicCast<cv::videostab::InpainterBase>());
	}
	
	cv::Ptr<cv::videostab::NullInpainter>* cv_PtrLcv_videostab_NullInpainterG_new_const_NullInpainter(cv::videostab::NullInpainter* val) {
			return new cv::Ptr<cv::videostab::NullInpainter>(val);
	}
	
}

extern "C" {
	const cv::videostab::NullLog* cv_PtrLcv_videostab_NullLogG_getInnerPtr_const(const cv::Ptr<cv::videostab::NullLog>* instance) {
			return instance->get();
	}
	
	cv::videostab::NullLog* cv_PtrLcv_videostab_NullLogG_getInnerPtrMut(cv::Ptr<cv::videostab::NullLog>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_NullLogG_delete(cv::Ptr<cv::videostab::NullLog>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::ILog>* cv_PtrLcv_videostab_NullLogG_to_PtrOfILog(cv::Ptr<cv::videostab::NullLog>* instance) {
			return new cv::Ptr<cv::videostab::ILog>(instance->dynamicCast<cv::videostab::ILog>());
	}
	
	cv::Ptr<cv::videostab::NullLog>* cv_PtrLcv_videostab_NullLogG_new_const_NullLog(cv::videostab::NullLog* val) {
			return new cv::Ptr<cv::videostab::NullLog>(val);
	}
	
}

extern "C" {
	const cv::videostab::NullOutlierRejector* cv_PtrLcv_videostab_NullOutlierRejectorG_getInnerPtr_const(const cv::Ptr<cv::videostab::NullOutlierRejector>* instance) {
			return instance->get();
	}
	
	cv::videostab::NullOutlierRejector* cv_PtrLcv_videostab_NullOutlierRejectorG_getInnerPtrMut(cv::Ptr<cv::videostab::NullOutlierRejector>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_NullOutlierRejectorG_delete(cv::Ptr<cv::videostab::NullOutlierRejector>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::IOutlierRejector>* cv_PtrLcv_videostab_NullOutlierRejectorG_to_PtrOfIOutlierRejector(cv::Ptr<cv::videostab::NullOutlierRejector>* instance) {
			return new cv::Ptr<cv::videostab::IOutlierRejector>(instance->dynamicCast<cv::videostab::IOutlierRejector>());
	}
	
	cv::Ptr<cv::videostab::NullOutlierRejector>* cv_PtrLcv_videostab_NullOutlierRejectorG_new_const_NullOutlierRejector(cv::videostab::NullOutlierRejector* val) {
			return new cv::Ptr<cv::videostab::NullOutlierRejector>(val);
	}
	
}

extern "C" {
	const cv::videostab::NullWobbleSuppressor* cv_PtrLcv_videostab_NullWobbleSuppressorG_getInnerPtr_const(const cv::Ptr<cv::videostab::NullWobbleSuppressor>* instance) {
			return instance->get();
	}
	
	cv::videostab::NullWobbleSuppressor* cv_PtrLcv_videostab_NullWobbleSuppressorG_getInnerPtrMut(cv::Ptr<cv::videostab::NullWobbleSuppressor>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_NullWobbleSuppressorG_delete(cv::Ptr<cv::videostab::NullWobbleSuppressor>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::WobbleSuppressorBase>* cv_PtrLcv_videostab_NullWobbleSuppressorG_to_PtrOfWobbleSuppressorBase(cv::Ptr<cv::videostab::NullWobbleSuppressor>* instance) {
			return new cv::Ptr<cv::videostab::WobbleSuppressorBase>(instance->dynamicCast<cv::videostab::WobbleSuppressorBase>());
	}
	
	cv::Ptr<cv::videostab::NullWobbleSuppressor>* cv_PtrLcv_videostab_NullWobbleSuppressorG_new_const_NullWobbleSuppressor(cv::videostab::NullWobbleSuppressor* val) {
			return new cv::Ptr<cv::videostab::NullWobbleSuppressor>(val);
	}
	
}

extern "C" {
	const cv::videostab::OnePassStabilizer* cv_PtrLcv_videostab_OnePassStabilizerG_getInnerPtr_const(const cv::Ptr<cv::videostab::OnePassStabilizer>* instance) {
			return instance->get();
	}
	
	cv::videostab::OnePassStabilizer* cv_PtrLcv_videostab_OnePassStabilizerG_getInnerPtrMut(cv::Ptr<cv::videostab::OnePassStabilizer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_OnePassStabilizerG_delete(cv::Ptr<cv::videostab::OnePassStabilizer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::IFrameSource>* cv_PtrLcv_videostab_OnePassStabilizerG_to_PtrOfIFrameSource(cv::Ptr<cv::videostab::OnePassStabilizer>* instance) {
			return new cv::Ptr<cv::videostab::IFrameSource>(instance->dynamicCast<cv::videostab::IFrameSource>());
	}
	
	cv::Ptr<cv::videostab::StabilizerBase>* cv_PtrLcv_videostab_OnePassStabilizerG_to_PtrOfStabilizerBase(cv::Ptr<cv::videostab::OnePassStabilizer>* instance) {
			return new cv::Ptr<cv::videostab::StabilizerBase>(instance->dynamicCast<cv::videostab::StabilizerBase>());
	}
	
	cv::Ptr<cv::videostab::OnePassStabilizer>* cv_PtrLcv_videostab_OnePassStabilizerG_new_const_OnePassStabilizer(cv::videostab::OnePassStabilizer* val) {
			return new cv::Ptr<cv::videostab::OnePassStabilizer>(val);
	}
	
}

extern "C" {
	const cv::videostab::PyrLkOptFlowEstimatorBase* cv_PtrLcv_videostab_PyrLkOptFlowEstimatorBaseG_getInnerPtr_const(const cv::Ptr<cv::videostab::PyrLkOptFlowEstimatorBase>* instance) {
			return instance->get();
	}
	
	cv::videostab::PyrLkOptFlowEstimatorBase* cv_PtrLcv_videostab_PyrLkOptFlowEstimatorBaseG_getInnerPtrMut(cv::Ptr<cv::videostab::PyrLkOptFlowEstimatorBase>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_PyrLkOptFlowEstimatorBaseG_delete(cv::Ptr<cv::videostab::PyrLkOptFlowEstimatorBase>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::PyrLkOptFlowEstimatorBase>* cv_PtrLcv_videostab_PyrLkOptFlowEstimatorBaseG_new_const_PyrLkOptFlowEstimatorBase(cv::videostab::PyrLkOptFlowEstimatorBase* val) {
			return new cv::Ptr<cv::videostab::PyrLkOptFlowEstimatorBase>(val);
	}
	
}

extern "C" {
	const cv::videostab::SparsePyrLkOptFlowEstimator* cv_PtrLcv_videostab_SparsePyrLkOptFlowEstimatorG_getInnerPtr_const(const cv::Ptr<cv::videostab::SparsePyrLkOptFlowEstimator>* instance) {
			return instance->get();
	}
	
	cv::videostab::SparsePyrLkOptFlowEstimator* cv_PtrLcv_videostab_SparsePyrLkOptFlowEstimatorG_getInnerPtrMut(cv::Ptr<cv::videostab::SparsePyrLkOptFlowEstimator>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_SparsePyrLkOptFlowEstimatorG_delete(cv::Ptr<cv::videostab::SparsePyrLkOptFlowEstimator>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::ISparseOptFlowEstimator>* cv_PtrLcv_videostab_SparsePyrLkOptFlowEstimatorG_to_PtrOfISparseOptFlowEstimator(cv::Ptr<cv::videostab::SparsePyrLkOptFlowEstimator>* instance) {
			return new cv::Ptr<cv::videostab::ISparseOptFlowEstimator>(instance->dynamicCast<cv::videostab::ISparseOptFlowEstimator>());
	}
	
	cv::Ptr<cv::videostab::PyrLkOptFlowEstimatorBase>* cv_PtrLcv_videostab_SparsePyrLkOptFlowEstimatorG_to_PtrOfPyrLkOptFlowEstimatorBase(cv::Ptr<cv::videostab::SparsePyrLkOptFlowEstimator>* instance) {
			return new cv::Ptr<cv::videostab::PyrLkOptFlowEstimatorBase>(instance->dynamicCast<cv::videostab::PyrLkOptFlowEstimatorBase>());
	}
	
	cv::Ptr<cv::videostab::SparsePyrLkOptFlowEstimator>* cv_PtrLcv_videostab_SparsePyrLkOptFlowEstimatorG_new_const_SparsePyrLkOptFlowEstimator(cv::videostab::SparsePyrLkOptFlowEstimator* val) {
			return new cv::Ptr<cv::videostab::SparsePyrLkOptFlowEstimator>(val);
	}
	
}

extern "C" {
	const cv::videostab::StabilizerBase* cv_PtrLcv_videostab_StabilizerBaseG_getInnerPtr_const(const cv::Ptr<cv::videostab::StabilizerBase>* instance) {
			return instance->get();
	}
	
	cv::videostab::StabilizerBase* cv_PtrLcv_videostab_StabilizerBaseG_getInnerPtrMut(cv::Ptr<cv::videostab::StabilizerBase>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_StabilizerBaseG_delete(cv::Ptr<cv::videostab::StabilizerBase>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::videostab::ToFileMotionWriter* cv_PtrLcv_videostab_ToFileMotionWriterG_getInnerPtr_const(const cv::Ptr<cv::videostab::ToFileMotionWriter>* instance) {
			return instance->get();
	}
	
	cv::videostab::ToFileMotionWriter* cv_PtrLcv_videostab_ToFileMotionWriterG_getInnerPtrMut(cv::Ptr<cv::videostab::ToFileMotionWriter>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_ToFileMotionWriterG_delete(cv::Ptr<cv::videostab::ToFileMotionWriter>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::ImageMotionEstimatorBase>* cv_PtrLcv_videostab_ToFileMotionWriterG_to_PtrOfImageMotionEstimatorBase(cv::Ptr<cv::videostab::ToFileMotionWriter>* instance) {
			return new cv::Ptr<cv::videostab::ImageMotionEstimatorBase>(instance->dynamicCast<cv::videostab::ImageMotionEstimatorBase>());
	}
	
	cv::Ptr<cv::videostab::ToFileMotionWriter>* cv_PtrLcv_videostab_ToFileMotionWriterG_new_const_ToFileMotionWriter(cv::videostab::ToFileMotionWriter* val) {
			return new cv::Ptr<cv::videostab::ToFileMotionWriter>(val);
	}
	
}

extern "C" {
	const cv::videostab::TranslationBasedLocalOutlierRejector* cv_PtrLcv_videostab_TranslationBasedLocalOutlierRejectorG_getInnerPtr_const(const cv::Ptr<cv::videostab::TranslationBasedLocalOutlierRejector>* instance) {
			return instance->get();
	}
	
	cv::videostab::TranslationBasedLocalOutlierRejector* cv_PtrLcv_videostab_TranslationBasedLocalOutlierRejectorG_getInnerPtrMut(cv::Ptr<cv::videostab::TranslationBasedLocalOutlierRejector>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_TranslationBasedLocalOutlierRejectorG_delete(cv::Ptr<cv::videostab::TranslationBasedLocalOutlierRejector>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::IOutlierRejector>* cv_PtrLcv_videostab_TranslationBasedLocalOutlierRejectorG_to_PtrOfIOutlierRejector(cv::Ptr<cv::videostab::TranslationBasedLocalOutlierRejector>* instance) {
			return new cv::Ptr<cv::videostab::IOutlierRejector>(instance->dynamicCast<cv::videostab::IOutlierRejector>());
	}
	
	cv::Ptr<cv::videostab::TranslationBasedLocalOutlierRejector>* cv_PtrLcv_videostab_TranslationBasedLocalOutlierRejectorG_new_const_TranslationBasedLocalOutlierRejector(cv::videostab::TranslationBasedLocalOutlierRejector* val) {
			return new cv::Ptr<cv::videostab::TranslationBasedLocalOutlierRejector>(val);
	}
	
}

extern "C" {
	const cv::videostab::TwoPassStabilizer* cv_PtrLcv_videostab_TwoPassStabilizerG_getInnerPtr_const(const cv::Ptr<cv::videostab::TwoPassStabilizer>* instance) {
			return instance->get();
	}
	
	cv::videostab::TwoPassStabilizer* cv_PtrLcv_videostab_TwoPassStabilizerG_getInnerPtrMut(cv::Ptr<cv::videostab::TwoPassStabilizer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_TwoPassStabilizerG_delete(cv::Ptr<cv::videostab::TwoPassStabilizer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::IFrameSource>* cv_PtrLcv_videostab_TwoPassStabilizerG_to_PtrOfIFrameSource(cv::Ptr<cv::videostab::TwoPassStabilizer>* instance) {
			return new cv::Ptr<cv::videostab::IFrameSource>(instance->dynamicCast<cv::videostab::IFrameSource>());
	}
	
	cv::Ptr<cv::videostab::StabilizerBase>* cv_PtrLcv_videostab_TwoPassStabilizerG_to_PtrOfStabilizerBase(cv::Ptr<cv::videostab::TwoPassStabilizer>* instance) {
			return new cv::Ptr<cv::videostab::StabilizerBase>(instance->dynamicCast<cv::videostab::StabilizerBase>());
	}
	
	cv::Ptr<cv::videostab::TwoPassStabilizer>* cv_PtrLcv_videostab_TwoPassStabilizerG_new_const_TwoPassStabilizer(cv::videostab::TwoPassStabilizer* val) {
			return new cv::Ptr<cv::videostab::TwoPassStabilizer>(val);
	}
	
}

extern "C" {
	const cv::videostab::VideoFileSource* cv_PtrLcv_videostab_VideoFileSourceG_getInnerPtr_const(const cv::Ptr<cv::videostab::VideoFileSource>* instance) {
			return instance->get();
	}
	
	cv::videostab::VideoFileSource* cv_PtrLcv_videostab_VideoFileSourceG_getInnerPtrMut(cv::Ptr<cv::videostab::VideoFileSource>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_VideoFileSourceG_delete(cv::Ptr<cv::videostab::VideoFileSource>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::IFrameSource>* cv_PtrLcv_videostab_VideoFileSourceG_to_PtrOfIFrameSource(cv::Ptr<cv::videostab::VideoFileSource>* instance) {
			return new cv::Ptr<cv::videostab::IFrameSource>(instance->dynamicCast<cv::videostab::IFrameSource>());
	}
	
	cv::Ptr<cv::videostab::VideoFileSource>* cv_PtrLcv_videostab_VideoFileSourceG_new_const_VideoFileSource(cv::videostab::VideoFileSource* val) {
			return new cv::Ptr<cv::videostab::VideoFileSource>(val);
	}
	
}

extern "C" {
	const cv::videostab::WeightingDeblurer* cv_PtrLcv_videostab_WeightingDeblurerG_getInnerPtr_const(const cv::Ptr<cv::videostab::WeightingDeblurer>* instance) {
			return instance->get();
	}
	
	cv::videostab::WeightingDeblurer* cv_PtrLcv_videostab_WeightingDeblurerG_getInnerPtrMut(cv::Ptr<cv::videostab::WeightingDeblurer>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_WeightingDeblurerG_delete(cv::Ptr<cv::videostab::WeightingDeblurer>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::videostab::DeblurerBase>* cv_PtrLcv_videostab_WeightingDeblurerG_to_PtrOfDeblurerBase(cv::Ptr<cv::videostab::WeightingDeblurer>* instance) {
			return new cv::Ptr<cv::videostab::DeblurerBase>(instance->dynamicCast<cv::videostab::DeblurerBase>());
	}
	
	cv::Ptr<cv::videostab::WeightingDeblurer>* cv_PtrLcv_videostab_WeightingDeblurerG_new_const_WeightingDeblurer(cv::videostab::WeightingDeblurer* val) {
			return new cv::Ptr<cv::videostab::WeightingDeblurer>(val);
	}
	
}

extern "C" {
	const cv::videostab::WobbleSuppressorBase* cv_PtrLcv_videostab_WobbleSuppressorBaseG_getInnerPtr_const(const cv::Ptr<cv::videostab::WobbleSuppressorBase>* instance) {
			return instance->get();
	}
	
	cv::videostab::WobbleSuppressorBase* cv_PtrLcv_videostab_WobbleSuppressorBaseG_getInnerPtrMut(cv::Ptr<cv::videostab::WobbleSuppressorBase>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_videostab_WobbleSuppressorBaseG_delete(cv::Ptr<cv::videostab::WobbleSuppressorBase>* instance) {
			delete instance;
	}
	
}


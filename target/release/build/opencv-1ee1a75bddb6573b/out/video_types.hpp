extern "C" {
	const cv::BackgroundSubtractor* cv_PtrLcv_BackgroundSubtractorG_getInnerPtr_const(const cv::Ptr<cv::BackgroundSubtractor>* instance) {
			return instance->get();
	}
	
	cv::BackgroundSubtractor* cv_PtrLcv_BackgroundSubtractorG_getInnerPtrMut(cv::Ptr<cv::BackgroundSubtractor>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_BackgroundSubtractorG_delete(cv::Ptr<cv::BackgroundSubtractor>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_BackgroundSubtractorG_to_PtrOfAlgorithm(cv::Ptr<cv::BackgroundSubtractor>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::BackgroundSubtractorKNN* cv_PtrLcv_BackgroundSubtractorKNNG_getInnerPtr_const(const cv::Ptr<cv::BackgroundSubtractorKNN>* instance) {
			return instance->get();
	}
	
	cv::BackgroundSubtractorKNN* cv_PtrLcv_BackgroundSubtractorKNNG_getInnerPtrMut(cv::Ptr<cv::BackgroundSubtractorKNN>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_BackgroundSubtractorKNNG_delete(cv::Ptr<cv::BackgroundSubtractorKNN>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_BackgroundSubtractorKNNG_to_PtrOfAlgorithm(cv::Ptr<cv::BackgroundSubtractorKNN>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::BackgroundSubtractor>* cv_PtrLcv_BackgroundSubtractorKNNG_to_PtrOfBackgroundSubtractor(cv::Ptr<cv::BackgroundSubtractorKNN>* instance) {
			return new cv::Ptr<cv::BackgroundSubtractor>(instance->dynamicCast<cv::BackgroundSubtractor>());
	}
	
}

extern "C" {
	const cv::BackgroundSubtractorMOG2* cv_PtrLcv_BackgroundSubtractorMOG2G_getInnerPtr_const(const cv::Ptr<cv::BackgroundSubtractorMOG2>* instance) {
			return instance->get();
	}
	
	cv::BackgroundSubtractorMOG2* cv_PtrLcv_BackgroundSubtractorMOG2G_getInnerPtrMut(cv::Ptr<cv::BackgroundSubtractorMOG2>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_BackgroundSubtractorMOG2G_delete(cv::Ptr<cv::BackgroundSubtractorMOG2>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_BackgroundSubtractorMOG2G_to_PtrOfAlgorithm(cv::Ptr<cv::BackgroundSubtractorMOG2>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::BackgroundSubtractor>* cv_PtrLcv_BackgroundSubtractorMOG2G_to_PtrOfBackgroundSubtractor(cv::Ptr<cv::BackgroundSubtractorMOG2>* instance) {
			return new cv::Ptr<cv::BackgroundSubtractor>(instance->dynamicCast<cv::BackgroundSubtractor>());
	}
	
}

extern "C" {
	const cv::DISOpticalFlow* cv_PtrLcv_DISOpticalFlowG_getInnerPtr_const(const cv::Ptr<cv::DISOpticalFlow>* instance) {
			return instance->get();
	}
	
	cv::DISOpticalFlow* cv_PtrLcv_DISOpticalFlowG_getInnerPtrMut(cv::Ptr<cv::DISOpticalFlow>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_DISOpticalFlowG_delete(cv::Ptr<cv::DISOpticalFlow>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_DISOpticalFlowG_to_PtrOfAlgorithm(cv::Ptr<cv::DISOpticalFlow>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::DenseOpticalFlow>* cv_PtrLcv_DISOpticalFlowG_to_PtrOfDenseOpticalFlow(cv::Ptr<cv::DISOpticalFlow>* instance) {
			return new cv::Ptr<cv::DenseOpticalFlow>(instance->dynamicCast<cv::DenseOpticalFlow>());
	}
	
}

extern "C" {
	const cv::DenseOpticalFlow* cv_PtrLcv_DenseOpticalFlowG_getInnerPtr_const(const cv::Ptr<cv::DenseOpticalFlow>* instance) {
			return instance->get();
	}
	
	cv::DenseOpticalFlow* cv_PtrLcv_DenseOpticalFlowG_getInnerPtrMut(cv::Ptr<cv::DenseOpticalFlow>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_DenseOpticalFlowG_delete(cv::Ptr<cv::DenseOpticalFlow>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_DenseOpticalFlowG_to_PtrOfAlgorithm(cv::Ptr<cv::DenseOpticalFlow>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::FarnebackOpticalFlow* cv_PtrLcv_FarnebackOpticalFlowG_getInnerPtr_const(const cv::Ptr<cv::FarnebackOpticalFlow>* instance) {
			return instance->get();
	}
	
	cv::FarnebackOpticalFlow* cv_PtrLcv_FarnebackOpticalFlowG_getInnerPtrMut(cv::Ptr<cv::FarnebackOpticalFlow>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_FarnebackOpticalFlowG_delete(cv::Ptr<cv::FarnebackOpticalFlow>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_FarnebackOpticalFlowG_to_PtrOfAlgorithm(cv::Ptr<cv::FarnebackOpticalFlow>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::DenseOpticalFlow>* cv_PtrLcv_FarnebackOpticalFlowG_to_PtrOfDenseOpticalFlow(cv::Ptr<cv::FarnebackOpticalFlow>* instance) {
			return new cv::Ptr<cv::DenseOpticalFlow>(instance->dynamicCast<cv::DenseOpticalFlow>());
	}
	
}

extern "C" {
	const cv::SparseOpticalFlow* cv_PtrLcv_SparseOpticalFlowG_getInnerPtr_const(const cv::Ptr<cv::SparseOpticalFlow>* instance) {
			return instance->get();
	}
	
	cv::SparseOpticalFlow* cv_PtrLcv_SparseOpticalFlowG_getInnerPtrMut(cv::Ptr<cv::SparseOpticalFlow>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_SparseOpticalFlowG_delete(cv::Ptr<cv::SparseOpticalFlow>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_SparseOpticalFlowG_to_PtrOfAlgorithm(cv::Ptr<cv::SparseOpticalFlow>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
}

extern "C" {
	const cv::SparsePyrLKOpticalFlow* cv_PtrLcv_SparsePyrLKOpticalFlowG_getInnerPtr_const(const cv::Ptr<cv::SparsePyrLKOpticalFlow>* instance) {
			return instance->get();
	}
	
	cv::SparsePyrLKOpticalFlow* cv_PtrLcv_SparsePyrLKOpticalFlowG_getInnerPtrMut(cv::Ptr<cv::SparsePyrLKOpticalFlow>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_SparsePyrLKOpticalFlowG_delete(cv::Ptr<cv::SparsePyrLKOpticalFlow>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_SparsePyrLKOpticalFlowG_to_PtrOfAlgorithm(cv::Ptr<cv::SparsePyrLKOpticalFlow>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::SparseOpticalFlow>* cv_PtrLcv_SparsePyrLKOpticalFlowG_to_PtrOfSparseOpticalFlow(cv::Ptr<cv::SparsePyrLKOpticalFlow>* instance) {
			return new cv::Ptr<cv::SparseOpticalFlow>(instance->dynamicCast<cv::SparseOpticalFlow>());
	}
	
}

extern "C" {
	const cv::Tracker* cv_PtrLcv_TrackerG_getInnerPtr_const(const cv::Ptr<cv::Tracker>* instance) {
			return instance->get();
	}
	
	cv::Tracker* cv_PtrLcv_TrackerG_getInnerPtrMut(cv::Ptr<cv::Tracker>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_TrackerG_delete(cv::Ptr<cv::Tracker>* instance) {
			delete instance;
	}
	
}

extern "C" {
	const cv::TrackerDaSiamRPN* cv_PtrLcv_TrackerDaSiamRPNG_getInnerPtr_const(const cv::Ptr<cv::TrackerDaSiamRPN>* instance) {
			return instance->get();
	}
	
	cv::TrackerDaSiamRPN* cv_PtrLcv_TrackerDaSiamRPNG_getInnerPtrMut(cv::Ptr<cv::TrackerDaSiamRPN>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_TrackerDaSiamRPNG_delete(cv::Ptr<cv::TrackerDaSiamRPN>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Tracker>* cv_PtrLcv_TrackerDaSiamRPNG_to_PtrOfTracker(cv::Ptr<cv::TrackerDaSiamRPN>* instance) {
			return new cv::Ptr<cv::Tracker>(instance->dynamicCast<cv::Tracker>());
	}
	
}

extern "C" {
	const cv::TrackerGOTURN* cv_PtrLcv_TrackerGOTURNG_getInnerPtr_const(const cv::Ptr<cv::TrackerGOTURN>* instance) {
			return instance->get();
	}
	
	cv::TrackerGOTURN* cv_PtrLcv_TrackerGOTURNG_getInnerPtrMut(cv::Ptr<cv::TrackerGOTURN>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_TrackerGOTURNG_delete(cv::Ptr<cv::TrackerGOTURN>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Tracker>* cv_PtrLcv_TrackerGOTURNG_to_PtrOfTracker(cv::Ptr<cv::TrackerGOTURN>* instance) {
			return new cv::Ptr<cv::Tracker>(instance->dynamicCast<cv::Tracker>());
	}
	
}

extern "C" {
	const cv::TrackerMIL* cv_PtrLcv_TrackerMILG_getInnerPtr_const(const cv::Ptr<cv::TrackerMIL>* instance) {
			return instance->get();
	}
	
	cv::TrackerMIL* cv_PtrLcv_TrackerMILG_getInnerPtrMut(cv::Ptr<cv::TrackerMIL>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_TrackerMILG_delete(cv::Ptr<cv::TrackerMIL>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Tracker>* cv_PtrLcv_TrackerMILG_to_PtrOfTracker(cv::Ptr<cv::TrackerMIL>* instance) {
			return new cv::Ptr<cv::Tracker>(instance->dynamicCast<cv::Tracker>());
	}
	
}

extern "C" {
	const cv::VariationalRefinement* cv_PtrLcv_VariationalRefinementG_getInnerPtr_const(const cv::Ptr<cv::VariationalRefinement>* instance) {
			return instance->get();
	}
	
	cv::VariationalRefinement* cv_PtrLcv_VariationalRefinementG_getInnerPtrMut(cv::Ptr<cv::VariationalRefinement>* instance) {
			return instance->get();
	}
	
	void cv_PtrLcv_VariationalRefinementG_delete(cv::Ptr<cv::VariationalRefinement>* instance) {
			delete instance;
	}
	
	cv::Ptr<cv::Algorithm>* cv_PtrLcv_VariationalRefinementG_to_PtrOfAlgorithm(cv::Ptr<cv::VariationalRefinement>* instance) {
			return new cv::Ptr<cv::Algorithm>(instance->dynamicCast<cv::Algorithm>());
	}
	
	cv::Ptr<cv::DenseOpticalFlow>* cv_PtrLcv_VariationalRefinementG_to_PtrOfDenseOpticalFlow(cv::Ptr<cv::VariationalRefinement>* instance) {
			return new cv::Ptr<cv::DenseOpticalFlow>(instance->dynamicCast<cv::DenseOpticalFlow>());
	}
	
}


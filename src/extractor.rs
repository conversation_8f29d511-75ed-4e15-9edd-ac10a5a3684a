use std::path::{Path, PathBuf};
use std::fs;
use indicatif::{ProgressBar, ProgressStyle};
use image::{RgbImage, ImageFormat};

use crate::types::{SceneTransition, DetectorConfig, Result, SceneDetectorError};

/// 帧提取器（简化版本）
pub struct FrameExtractor {
    config: DetectorConfig,
}

impl FrameExtractor {
    pub fn new(config: DetectorConfig) -> Self {
        Self { config }
    }

    /// 提取所有场景的帧
    pub fn extract_frames<P: AsRef<Path>>(
        &self,
        input_path: P,
        scenes: &mut Vec<SceneTransition>,
    ) -> Result<()> {
        let input_path = input_path.as_ref();

        // 创建输出目录
        self.create_output_directories()?;

        if input_path.is_dir() {
            // 如果输入是图像序列目录，复制相关图像
            self.extract_from_image_sequence(input_path, scenes)?;
        } else {
            // 检查是否为视频文件
            let extension = input_path.extension()
                .and_then(|ext| ext.to_str())
                .unwrap_or("")
                .to_lowercase();

            match extension.as_str() {
                "mp4" | "avi" | "mov" | "mkv" | "wmv" | "flv" | "webm" => {
                    // 使用OpenCV从视频文件提取帧
                    self.extract_from_video(input_path, scenes)?;
                }
                _ => {
                    // 对于不支持的格式，创建占位符
                    self.create_placeholder_frames(scenes)?;
                }
            }
        }

        // 如果启用了视频片段提取，则提取视频片段
        if self.config.extract_clips && !input_path.is_dir() {
            self.extract_video_clips(input_path, scenes)?;
        }

        println!("✓ 帧提取完成");
        Ok(())
    }

    /// 从视频文件中提取帧
    fn extract_from_video<P: AsRef<Path>>(
        &self,
        video_path: P,
        scenes: &mut Vec<SceneTransition>,
    ) -> Result<()> {
        use opencv::{videoio, core::Mat, imgcodecs, prelude::*};

        let video_path = video_path.as_ref();

        // 打开视频文件
        let mut cap = videoio::VideoCapture::from_file(
            video_path.to_str().unwrap(),
            videoio::CAP_ANY
        ).map_err(|e| SceneDetectorError::Detection(format!("无法打开视频文件: {}", e)))?;

        if !cap.is_opened()
            .map_err(|e| SceneDetectorError::Detection(format!("视频文件打开失败: {}", e)))? {
            return Err(SceneDetectorError::InvalidVideo("无法打开视频文件".to_string()));
        }

        // 获取视频信息
        let fps = cap.get(videoio::CAP_PROP_FPS)
            .map_err(|e| SceneDetectorError::Detection(format!("无法获取FPS: {}", e)))?;

        let total_scenes = scenes.len();
        println!("开始提取帧，共 {} 个镜头", total_scenes);

        // 设置进度条
        let progress = if self.config.show_progress {
            let pb = ProgressBar::new(total_scenes as u64);
            pb.set_style(ProgressStyle::default_bar()
                .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {pos}/{len} 镜头 ({eta})")
                .unwrap()
                .progress_chars("#>-"));
            Some(pb)
        } else {
            None
        };

        for (scene_idx, scene) in scenes.iter().enumerate() {
            // 提取第一帧
            self.extract_frame_at_time(&mut cap, scene.start_time, scene_idx, "first", fps)?;

            // 提取最后一帧
            self.extract_frame_at_time(&mut cap, scene.end_time, scene_idx, "last", fps)?;

            if let Some(ref pb) = progress {
                pb.set_position((scene_idx + 1) as u64);
            }
        }

        if let Some(ref pb) = progress {
            pb.finish();
        }

        Ok(())
    }

    /// 在指定时间提取帧
    fn extract_frame_at_time(
        &self,
        cap: &mut opencv::videoio::VideoCapture,
        timestamp: f64,
        scene_idx: usize,
        frame_type: &str,
        fps: f64,
    ) -> Result<()> {
        use opencv::{videoio, core::Mat, imgcodecs, prelude::*};

        // 计算帧号
        let frame_number = (timestamp * fps) as i32;

        // 跳转到指定帧
        cap.set(videoio::CAP_PROP_POS_FRAMES, frame_number as f64)
            .map_err(|e| SceneDetectorError::Detection(format!("无法跳转到帧 {}: {}", frame_number, e)))?;

        // 读取帧
        let mut mat = Mat::default();
        let ret = cap.read(&mut mat)
            .map_err(|e| SceneDetectorError::Detection(format!("读取帧失败: {}", e)))?;

        if !ret {
            return Ok(()); // 跳过无效帧
        }

        // 检查帧是否为空
        if mat.empty() {
            return Ok(()); // 跳过无效帧
        }

        // 保存帧
        let output_path = format!("{}/scene_{:03}_{}.jpg",
            self.config.output_dir.display(),
            scene_idx + 1,
            frame_type
        );

        imgcodecs::imwrite(&output_path, &mat, &opencv::core::Vector::new())
            .map_err(|e| SceneDetectorError::Detection(format!("保存帧失败: {}", e)))?;

        Ok(())
    }

    /// 从图像序列中提取帧
    fn extract_from_image_sequence<P: AsRef<Path>>(
        &self,
        dir_path: P,
        scenes: &mut Vec<SceneTransition>,
    ) -> Result<()> {
        let dir_path = dir_path.as_ref();
        
        // 查找图像文件
        let image_files = self.find_image_files(dir_path)?;
        
        if image_files.is_empty() {
            return Ok(());
        }

        let total_scenes = scenes.len();
        println!("开始提取帧，共 {} 个镜头", total_scenes);

        // 设置进度条
        let progress = if self.config.show_progress {
            let pb = ProgressBar::new(total_scenes as u64);
            pb.set_style(
                ProgressStyle::default_bar()
                    .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {pos}/{len} 镜头 ({eta})")
                    .unwrap()
                    .progress_chars("#>-"),
            );
            Some(pb)
        } else {
            None
        };

        // 为每个场景提取帧
        for (index, scene) in scenes.iter_mut().enumerate() {
            if let Some(pb) = &progress {
                pb.set_position(index as u64);
            }

            self.extract_scene_frames_from_sequence(&image_files, scene)?;

            if self.config.extract_keyframes {
                self.extract_keyframes_from_sequence(&image_files, scene)?;
            }
        }

        if let Some(pb) = &progress {
            pb.finish_with_message("帧提取完成");
        }

        Ok(())
    }

    /// 从图像序列中提取场景帧
    fn extract_scene_frames_from_sequence(
        &self,
        image_files: &[PathBuf],
        scene: &mut SceneTransition,
    ) -> Result<()> {
        let start_idx = scene.start_frame as usize;
        let end_idx = scene.end_frame as usize;

        // 提取首帧
        if start_idx < image_files.len() {
            let first_frame_path = self.copy_frame(
                &image_files[start_idx], 
                scene.scene_id, 
                "first", 
                scene.start_frame
            )?;
            scene.first_frame_path = Some(first_frame_path);
        }

        // 提取尾帧（如果不是同一帧）
        if end_idx < image_files.len() && end_idx != start_idx {
            let last_frame_path = self.copy_frame(
                &image_files[end_idx], 
                scene.scene_id, 
                "last", 
                scene.end_frame
            )?;
            scene.last_frame_path = Some(last_frame_path);
        } else {
            scene.last_frame_path = scene.first_frame_path.clone();
        }

        Ok(())
    }

    /// 从图像序列中提取关键帧
    fn extract_keyframes_from_sequence(
        &self,
        image_files: &[PathBuf],
        scene: &mut SceneTransition,
    ) -> Result<()> {
        let frame_count = scene.frame_count();
        if frame_count <= self.config.keyframe_interval * 2 {
            // 场景太短，不需要额外的关键帧
            return Ok(());
        }

        let mut keyframe_paths = Vec::new();
        let mut current_frame = scene.start_frame + self.config.keyframe_interval;

        while current_frame < scene.end_frame - self.config.keyframe_interval {
            let frame_idx = current_frame as usize;
            if frame_idx < image_files.len() {
                let keyframe_path = self.copy_keyframe(
                    &image_files[frame_idx], 
                    scene.scene_id, 
                    keyframe_paths.len(), 
                    current_frame
                )?;
                keyframe_paths.push(keyframe_path);
            }
            current_frame += self.config.keyframe_interval;
        }

        scene.keyframes_paths = keyframe_paths;
        Ok(())
    }

    /// 复制帧文件
    fn copy_frame(
        &self,
        source_path: &Path,
        scene_id: usize,
        frame_type: &str,
        frame_number: u32,
    ) -> Result<PathBuf> {
        // 生成文件名
        let filename = format!("scene_{:04}_{}_frame_{:06}.jpg", scene_id, frame_type, frame_number);
        let output_path = self.config.output_dir.join("frames").join(&filename);

        // 复制文件
        fs::copy(source_path, &output_path)?;

        Ok(output_path)
    }

    /// 复制关键帧文件
    fn copy_keyframe(
        &self,
        source_path: &Path,
        scene_id: usize,
        keyframe_index: usize,
        frame_number: u32,
    ) -> Result<PathBuf> {
        // 生成文件名
        let filename = format!("scene_{:04}_keyframe_{:02}_frame_{:06}.jpg", 
                              scene_id, keyframe_index, frame_number);
        let output_path = self.config.output_dir.join("keyframes").join(&filename);

        // 复制文件
        fs::copy(source_path, &output_path)?;

        Ok(output_path)
    }

    /// 创建占位符帧（用于视频文件）
    fn create_placeholder_frames(&self, scenes: &mut Vec<SceneTransition>) -> Result<()> {
        println!("创建占位符帧文件...");

        for scene in scenes.iter_mut() {
            // 创建占位符首帧
            let first_frame_path = self.create_placeholder_image(
                scene.scene_id, 
                "first", 
                scene.start_frame
            )?;
            scene.first_frame_path = Some(first_frame_path);

            // 创建占位符尾帧
            if scene.end_frame != scene.start_frame {
                let last_frame_path = self.create_placeholder_image(
                    scene.scene_id, 
                    "last", 
                    scene.end_frame
                )?;
                scene.last_frame_path = Some(last_frame_path);
            } else {
                scene.last_frame_path = scene.first_frame_path.clone();
            }

            // 创建占位符关键帧
            if self.config.extract_keyframes {
                let mut keyframe_paths = Vec::new();
                let mut current_frame = scene.start_frame + self.config.keyframe_interval;
                let mut keyframe_index = 0;

                while current_frame < scene.end_frame - self.config.keyframe_interval {
                    let keyframe_path = self.create_placeholder_keyframe(
                        scene.scene_id, 
                        keyframe_index, 
                        current_frame
                    )?;
                    keyframe_paths.push(keyframe_path);
                    
                    current_frame += self.config.keyframe_interval;
                    keyframe_index += 1;
                }

                scene.keyframes_paths = keyframe_paths;
            }
        }

        Ok(())
    }

    /// 创建占位符图像
    fn create_placeholder_image(
        &self,
        scene_id: usize,
        frame_type: &str,
        frame_number: u32,
    ) -> Result<PathBuf> {
        // 生成文件名
        let filename = format!("scene_{:04}_{}_frame_{:06}.jpg", scene_id, frame_type, frame_number);
        let output_path = self.config.output_dir.join("frames").join(&filename);

        // 创建简单的占位符图像
        self.create_simple_placeholder(&output_path, &format!("Scene {} - {} Frame", scene_id + 1, frame_type))?;

        Ok(output_path)
    }

    /// 创建占位符关键帧
    fn create_placeholder_keyframe(
        &self,
        scene_id: usize,
        keyframe_index: usize,
        frame_number: u32,
    ) -> Result<PathBuf> {
        // 生成文件名
        let filename = format!("scene_{:04}_keyframe_{:02}_frame_{:06}.jpg", 
                              scene_id, keyframe_index, frame_number);
        let output_path = self.config.output_dir.join("keyframes").join(&filename);

        // 创建简单的占位符图像
        self.create_simple_placeholder(&output_path, &format!("Scene {} - Keyframe {}", scene_id + 1, keyframe_index + 1))?;

        Ok(output_path)
    }

    /// 创建简单的占位符图像
    fn create_simple_placeholder(&self, output_path: &Path, _text: &str) -> Result<()> {
        // 确保父目录存在
        if let Some(parent) = output_path.parent() {
            fs::create_dir_all(parent)?;
        }

        // 创建一个简单的彩色图像作为占位符
        let width = 320;
        let height = 240;
        let mut img = RgbImage::new(width, height);

        // 填充渐变色
        for (x, y, pixel) in img.enumerate_pixels_mut() {
            let r = (x * 255 / width) as u8;
            let g = (y * 255 / height) as u8;
            let b = ((x + y) * 255 / (width + height)) as u8;
            *pixel = image::Rgb([r, g, b]);
        }

        // 保存图像
        img.save_with_format(output_path, ImageFormat::Jpeg)
            .map_err(|e| SceneDetectorError::FrameExtraction(format!("Failed to save placeholder: {}", e)))?;

        Ok(())
    }

    /// 查找目录中的图像文件
    fn find_image_files<P: AsRef<Path>>(&self, dir_path: P) -> Result<Vec<PathBuf>> {
        let mut image_files = Vec::new();
        let image_extensions = ["jpg", "jpeg", "png", "bmp", "tiff"];

        for entry in fs::read_dir(dir_path)? {
            let entry = entry?;
            let path = entry.path();
            
            if path.is_file() {
                if let Some(extension) = path.extension() {
                    if let Some(ext_str) = extension.to_str() {
                        if image_extensions.contains(&ext_str.to_lowercase().as_str()) {
                            image_files.push(path);
                        }
                    }
                }
            }
        }

        image_files.sort();
        Ok(image_files)
    }

    /// 创建输出目录结构
    pub fn create_output_directories(&self) -> Result<()> {
        let base_dir = &self.config.output_dir;
        
        // 创建基础目录
        fs::create_dir_all(base_dir)?;
        
        // 创建帧目录
        fs::create_dir_all(base_dir.join("frames"))?;
        
        // 如果需要提取关键帧，创建关键帧目录
        if self.config.extract_keyframes {
            fs::create_dir_all(base_dir.join("keyframes"))?;
        }

        Ok(())
    }

    /// 生成缩略图（简化版本）
    pub fn extract_thumbnails<P: AsRef<Path>>(
        &self,
        _input_path: P,
        scenes: &[SceneTransition],
        thumbnail_size: (u32, u32),
    ) -> Result<Vec<PathBuf>> {
        // 创建缩略图目录
        let thumbnail_dir = self.config.output_dir.join("thumbnails");
        fs::create_dir_all(&thumbnail_dir)?;

        let mut thumbnail_paths = Vec::new();

        for scene in scenes {
            // 生成文件名
            let filename = format!("scene_{:04}_thumbnail.jpg", scene.scene_id);
            let output_path = thumbnail_dir.join(&filename);

            // 创建缩略图占位符
            self.create_thumbnail_placeholder(&output_path, thumbnail_size, scene.scene_id)?;
            thumbnail_paths.push(output_path);
        }

        Ok(thumbnail_paths)
    }

    /// 创建缩略图占位符
    fn create_thumbnail_placeholder(
        &self,
        output_path: &Path,
        size: (u32, u32),
        scene_id: usize,
    ) -> Result<()> {
        let mut img = RgbImage::new(size.0, size.1);

        // 为每个场景创建不同的颜色
        let hue = (scene_id * 60) % 360;
        let (r, g, b) = self.hsv_to_rgb(hue as f64, 0.7, 0.9);

        for pixel in img.pixels_mut() {
            *pixel = image::Rgb([r, g, b]);
        }

        img.save_with_format(output_path, ImageFormat::Jpeg)
            .map_err(|e| SceneDetectorError::FrameExtraction(format!("Failed to save thumbnail: {}", e)))?;

        Ok(())
    }

    /// HSV到RGB转换
    fn hsv_to_rgb(&self, h: f64, s: f64, v: f64) -> (u8, u8, u8) {
        let c = v * s;
        let x = c * (1.0 - ((h / 60.0) % 2.0 - 1.0).abs());
        let m = v - c;

        let (r_prime, g_prime, b_prime) = if h < 60.0 {
            (c, x, 0.0)
        } else if h < 120.0 {
            (x, c, 0.0)
        } else if h < 180.0 {
            (0.0, c, x)
        } else if h < 240.0 {
            (0.0, x, c)
        } else if h < 300.0 {
            (x, 0.0, c)
        } else {
            (c, 0.0, x)
        };

        let r = ((r_prime + m) * 255.0) as u8;
        let g = ((g_prime + m) * 255.0) as u8;
        let b = ((b_prime + m) * 255.0) as u8;

        (r, g, b)
    }

    /// 提取视频片段
    fn extract_video_clips<P: AsRef<Path>>(
        &self,
        video_path: P,
        scenes: &mut Vec<SceneTransition>,
    ) -> Result<()> {
        let video_path = video_path.as_ref();
        let clips_dir = self.config.output_dir.join("clips");

        // 创建clips目录
        fs::create_dir_all(&clips_dir)?;

        println!("开始提取视频片段，共 {} 个镜头", scenes.len());

        // 设置进度条
        let progress = if self.config.show_progress {
            let pb = ProgressBar::new(scenes.len() as u64);
            pb.set_style(ProgressStyle::default_bar()
                .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {pos}/{len} 片段 ({eta})")
                .unwrap()
                .progress_chars("#>-"));
            Some(pb)
        } else {
            None
        };

        for (index, scene) in scenes.iter_mut().enumerate() {
            if let Some(ref pb) = progress {
                pb.set_position(index as u64);
            }

            // 计算时间戳
            let start_time = scene.start_time;
            let duration = scene.duration;

            // 生成输出文件名
            let clip_filename = format!("scene_{:04}_{:.2}s-{:.2}s.mp4",
                scene.scene_id, start_time, start_time + duration);
            let clip_path = clips_dir.join(&clip_filename);

            // 使用ffmpeg提取视频片段
            let success = self.extract_clip_with_ffmpeg(
                video_path,
                &clip_path,
                start_time,
                duration
            )?;

            if success {
                // 更新场景信息，添加视频片段路径
                scene.clip_path = Some(clip_path.to_string_lossy().to_string());
            }
        }

        if let Some(ref pb) = progress {
            pb.set_position(scenes.len() as u64);
            pb.finish_with_message("视频片段提取完成");
        }

        Ok(())
    }

    /// 使用ffmpeg提取视频片段
    fn extract_clip_with_ffmpeg<P: AsRef<Path>>(
        &self,
        input_path: P,
        output_path: P,
        start_time: f64,
        duration: f64,
    ) -> Result<bool> {
        use std::process::Command;

        let input_path = input_path.as_ref();
        let output_path = output_path.as_ref();

        // 构建ffmpeg命令
        let mut cmd = Command::new("ffmpeg");
        cmd.arg("-i")
           .arg(input_path)
           .arg("-ss")
           .arg(format!("{:.3}", start_time))
           .arg("-t")
           .arg(format!("{:.3}", duration))
           .arg("-c")
           .arg("copy")  // 使用copy避免重新编码，提高速度
           .arg("-avoid_negative_ts")
           .arg("make_zero")
           .arg("-y")  // 覆盖输出文件
           .arg(output_path);

        // 如果不显示详细输出，隐藏ffmpeg的输出
        if !self.config.show_progress {
            cmd.stdout(std::process::Stdio::null())
               .stderr(std::process::Stdio::null());
        }

        // 执行命令
        let output = cmd.output()
            .map_err(|e| SceneDetectorError::Detection(
                format!("无法执行ffmpeg命令: {}. 请确保已安装ffmpeg", e)
            ))?;

        if output.status.success() {
            Ok(true)
        } else {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            eprintln!("ffmpeg错误: {}", error_msg);
            Ok(false)
        }
    }
}
